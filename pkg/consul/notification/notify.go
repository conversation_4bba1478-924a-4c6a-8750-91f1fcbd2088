package notification

import (
	"encoding/json"
	"os"
	"sync"
	"zx/zx-consistency/pkg/consul"
	"zx/zxgo/log"

	consulapi "github.com/hashicorp/consul/api"
)

var NotifyConfigMap *sync.Map = new(sync.Map)

func init() {
	key := "Notify"
	zxRuntimeEnv := os.Getenv("zx_runtime")
	if zxRuntimeEnv == "test" {
		key = "Notify_test"
	}

	log.Info("zx_runtime: ", zxRuntimeEnv)
	log.Info("consul notify config key: ", key)

	consul.RegisterConsulConfigHandler(key, (*NotifyConfig)(nil))
}

type NotifyLevelType string

const (
	NotifyLevelForPanic NotifyLevelType = "Panic" // Go 运行时触发的不可恢复错误，会打印堆栈并终止程序（可被 recover 捕获）
	NotifyLevelForFatal NotifyLevelType = "Fatal" // 致命，表示程序无法继续运行，但不会主动触发崩溃，通过会在记录日志后终止程序
	NotifyLevelForAlert NotifyLevelType = "Alert" // 告警，需要立即关注但不一定导致系统崩溃的问题
	NotifyLevelForWarn  NotifyLevelType = "Warn"  // 警告，表示潜在问题或非致命错误，系统仍可正常运行，但需关注
	NotifyLevelForError NotifyLevelType = "Error" // 错误，表示功能异常或失败，但系统可能仍能部分运行
	NotifyLevelForInfo  NotifyLevelType = "Info"  // 信息，系统运行状态、正常操作
	NotifyLevelForTrace NotifyLevelType = "Trace" // 跟踪，比 Debug 更细粒度，用于追踪程序执行的详细流程
	NotifyLevelForDebug NotifyLevelType = "Debug" // 调试，最低级别，用于开发阶段记录详细信息，生产环境通常关闭
)

// NotifyConfig is 通知配置（用于异常处理通知）
type NotifyConfig struct {
	//Items []*NotifyConfigItem `json:"items"`
	Level      int             `json:"level"`        // 等级
	Name       NotifyLevelType `json:"name"`         // 类型
	WebHookUrl string          `json:"web_hook_url"` // 发送url地址。目前只实现飞书发送
}

//// NotifyConfigItem is 通知配置元素
//type NotifyConfigItem struct {
//	Level      int             `json:"level"`        // 等级
//	Name       NotifyLevelType `json:"name"`         // 类型
//	WebHookUrl string          `json:"web_hook_url"` // 发送url地址。目前只实现飞书发送
//}

// ProcessConfig 处理通知配置
func (n *NotifyConfig) ProcessConfig(kv *consulapi.KVPair) (err error) {
	if kv == nil {
		return
	}

	log.Debug("NotifyConfig) ProcessConfig >>> kv value: ", string(kv.Value))

	// 解析配置参数
	notifyConfigList := make([]*NotifyConfig, 0)
	err = json.Unmarshal(kv.Value, &notifyConfigList)
	if err != nil {
		log.Error(err)
		return
	}

	for _, notifyConfig := range notifyConfigList {
		// 加入全局变量中
		NotifyConfigMap.Store(notifyConfig.Name, *notifyConfig)
	}

	// 打印配置信息
	configData, _ := json.Marshal(notifyConfigList)
	log.Info("NotifyConfigData: ", string(configData))

	return
}

// GetNotifyConfig 获取通知配置
func GetNotifyConfig(key any) *NotifyConfig {
	if value, ok := NotifyConfigMap.Load(key); ok {
		if config, ok := value.(*NotifyConfig); ok {
			return config
		}
	}
	return &NotifyConfig{}
}
