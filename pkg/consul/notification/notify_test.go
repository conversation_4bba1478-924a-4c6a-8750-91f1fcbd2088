package notification

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"testing"
	"time"
	"zx/zxgo/notify"

	consulapi "github.com/hashicorp/consul/api"
	"github.com/stretchr/testify/assert"
)

func TestNotifyConfig_ProcessConfig(t *testing.T) {
	// 设置测试环境
	os.Setenv("zx_runtime", "test")
	defer os.Unsetenv("zx_runtime")

	tests := []struct {
		name    string
		kv      *consulapi.KVPair
		wantErr bool
	}{
		{
			name: "正常配置处理",
			kv: &consulapi.KVPair{
				Value: []byte(`[
					{
						"level": 1,
						"name": "Panic",
						"web_hook_url": "http://test.com/panic"
					},
					{
						"level": 2,
						"name": "Fatal",
						"web_hook_url": "http://test.com/fatal"
					}
				]`),
			},
			wantErr: false,
		},
		{
			name:    "空配置处理",
			kv:      nil,
			wantErr: false,
		},
		{
			name: "无效JSON配置",
			kv: &consulapi.KVPair{
				Value: []byte(`invalid json`),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &NotifyConfig{}
			err := config.ProcessConfig(tt.kv)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetNotifyConfig(t *testing.T) {
	// 设置测试环境
	os.Setenv("zx_runtime", "test")
	defer os.Unsetenv("zx_runtime")

	// 准备测试数据
	testConfig := &NotifyConfig{
		Level:      1,
		Name:       NotifyLevelForPanic,
		WebHookUrl: "http://test.com/panic",
	}
	NotifyConfigMap.Store(NotifyLevelForPanic, testConfig)

	tests := []struct {
		name     string
		key      string
		expected *NotifyConfig
	}{
		{
			name:     "获取存在的配置",
			key:      string(NotifyLevelForPanic),
			expected: testConfig,
		},
		{
			name:     "获取不存在的配置",
			key:      "NonExistent",
			expected: &NotifyConfig{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := GetNotifyConfig(tt.key)
			assert.Equal(t, tt.expected, config)
		})
	}
}

func TestNotifyLevelType(t *testing.T) {
	tests := []struct {
		name     string
		level    NotifyLevelType
		expected string
	}{
		{
			name:     "Panic级别",
			level:    NotifyLevelForPanic,
			expected: "Panic",
		},
		{
			name:     "Fatal级别",
			level:    NotifyLevelForFatal,
			expected: "Fatal",
		},
		{
			name:     "Alert级别",
			level:    NotifyLevelForAlert,
			expected: "Alert",
		},
		{
			name:     "Warn级别",
			level:    NotifyLevelForWarn,
			expected: "Warn",
		},
		{
			name:     "Error级别",
			level:    NotifyLevelForError,
			expected: "Error",
		},
		{
			name:     "Info级别",
			level:    NotifyLevelForInfo,
			expected: "Info",
		},
		{
			name:     "Trace级别",
			level:    NotifyLevelForTrace,
			expected: "Trace",
		},
		{
			name:     "Debug级别",
			level:    NotifyLevelForDebug,
			expected: "Debug",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.level))
		})
	}
}

func TestNotifyConfig_JSON(t *testing.T) {
	config := &NotifyConfig{
		Level:      1,
		Name:       NotifyLevelForPanic,
		WebHookUrl: "http://test.com/panic",
	}

	// 测试JSON序列化
	jsonData, err := json.Marshal(config)
	assert.NoError(t, err)

	// 测试JSON反序列化
	var newConfig NotifyConfig
	err = json.Unmarshal(jsonData, &newConfig)
	assert.NoError(t, err)
	assert.Equal(t, config, &newConfig)
}

var notifyConfigMap sync.Map

func TestNotifyProcess(t *testing.T) {
	errMsg := fmt.Sprintln("Kafka消费异常, panic错误: invalid memory address or nil pointer dereference [recovered]")
	//errMsg = strings.ReplaceAll(errMsg, " ", "")

	//构建配置参数
	notifyConfigList := []*NotifyConfig{
		{
			Level:      0,
			Name:       NotifyLevelForPanic,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      1,
			Name:       NotifyLevelForFatal,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      2,
			Name:       NotifyLevelForAlert,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      3,
			Name:       NotifyLevelForWarn,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      4,
			Name:       NotifyLevelForInfo,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      5,
			Name:       NotifyLevelForTrace,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
		{
			Level:      6,
			Name:       NotifyLevelForDebug,
			WebHookUrl: "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec",
		},
	}

	//模拟读取consul配置加入全局变量中
	for _, notifyConfig := range notifyConfigList {
		notifyConfigMap.Store(notifyConfig.Name, notifyConfig)
	}

	// 验证存储是否成功
	value, ok := notifyConfigMap.Load(NotifyLevelForPanic)
	if ok {
		if config, ok := value.(*NotifyConfig); ok {
			t.Logf("存储的配置: %+v", config)
		} else {
			t.Errorf("类型断言失败，实际类型: %T", value)
		}
	} else {
		t.Error("未找到Panic配置")
	}

	// 打印配置信息
	configData, _ := json.Marshal(notifyConfigList)
	t.Log("NotifyConfigData: ", string(configData))

	// 获取对应通知配置
	notifyConfig := getNotifyConfig(string(NotifyLevelForPanic))
	if notifyConfig == nil {
		t.Fatal("获取配置失败")
	}

	timeNow := time.Now().Format("2006-01-02 15:04:05")

	// 构建通知数据
	notifyData := notify.NotificationMessage{
		Name:       string(notifyConfig.Name),
		Message:    errMsg,
		WebHookUrl: notifyConfig.WebHookUrl,
		CreatedAt:  timeNow,
	}

	// 发送飞书群组
	err := notifyData.SendNotifyMsgToFeiShu()
	if err != nil {
		t.Errorf("发送飞书失败,err: %s", err.Error())
		return
	}

	t.Log("发送飞书群组成功")
}

// GetNotifyConfig 获取通知配置
func getNotifyConfig(key string) *NotifyConfig {
	if value, ok := notifyConfigMap.Load(NotifyLevelType(key)); ok {
		if config, ok := value.(*NotifyConfig); ok {
			return config
		}
	}
	return nil
}
