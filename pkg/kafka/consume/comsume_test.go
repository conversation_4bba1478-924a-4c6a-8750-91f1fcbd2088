package consume

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"
	"zx/unit/pkg/redis"
	"zx/zx-consistency/pkg/consul/notification"
	"zx/zx-consistency/pkg/kafka"
	"zx/zxgo/notify"
)

// 模拟 ConsumeHandler
type mockConsumeHandler struct{}

func (m *mockConsumeHandler) ProcessMsg(topic string, msgData []byte) error {
	return nil
}

// 模拟 notify.NotificationMessage
type mockNotificationMessage struct {
	notify.NotificationMessage
	sendSuccess bool
}

func (m *mockNotificationMessage) SendNotifyMsgToFeiShu() error {
	if m.sendSuccess {
		return nil
	}
	return errors.New("mock send feishu error")
}

func TestKafkaConsume_PanicProcessor(t *testing.T) {
	// 初始化测试环境
	ctx := context.Background()
	handler := &mockConsumeHandler{}
	kafkaConsume := &KafkaConsume{
		//Topic:   "test_topic",
		Ctx:     ctx,
		Handler: handler,
	}

	// 测试用例
	tests := []struct {
		name            string
		recoveredErr    interface{}
		mockSendSuccess bool
		wantErr         bool
	}{
		{
			name:            "正常错误处理-发送成功",
			recoveredErr:    "test panic error",
			mockSendSuccess: true,
			wantErr:         false,
		},
		{
			name:            "正常错误处理-发送失败",
			recoveredErr:    "test panic error",
			mockSendSuccess: false,
			wantErr:         false,
		},
		{
			name:            "空错误处理",
			recoveredErr:    nil,
			mockSendSuccess: true,
			wantErr:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置实际通知配置
			notifyConfig := []*notification.NotifyConfig{
				{
					Level:      0,
					Name:       "Panic",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      1,
					Name:       "Fatal",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      2,
					Name:       "Alert",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      3,
					Name:       "Warn",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      4,
					Name:       "Info",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      5,
					Name:       "Trace",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
				{
					Level:      6,
					Name:       "Debug",
					WebHookUrl: "a37f9145-415d-ea34-8295-78e404b1af44",
				},
			}

			notification.NotifyConfigMap.Store(tt.name, notifyConfig)

			// 执行测试
			err := kafkaConsume.PanicProcessor(tt.recoveredErr)

			// 验证结果
			if (err != nil) != tt.wantErr {
				t.Errorf("PanicProcessor() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 验证 Redis 中是否存在通知数据
			if tt.recoveredErr != nil {
				redisClient := redis.Client()
				keys, err := redisClient.Keys("zx_notify_*").Result()
				if err != nil {
					t.Errorf("Failed to get Redis keys: %v", err)
				}
				if len(keys) == 0 {
					t.Error("Expected Redis keys to exist, but none found")
				}
			}

			// 清理测试数据
			cleanupTestEnv()
		})
	}
}

func TestSendNotifyMsgFailProcessToFeiShu(t *testing.T) {
	// 准备测试数据
	testKey := "test_notify_key"
	testData := `{Name:"panic","business_type":"test_topic","message":"test error",WebHookUrl:"http://test.com","created_at":"2024-01-01 00:00:00"}`

	// 写入测试数据到 Redis
	redisClient := redis.Client()
	err := redisClient.Set(testKey, testData, time.Hour).Err()
	if err != nil {
		t.Fatalf("Failed to set test data in Redis: %v", err)
	}
	defer redisClient.Del(testKey)

	// 执行测试
	kafka.SendNotifyMsgFailProcessToFeiShu(testKey)

	// 验证结果
	// 由于这是一个异步操作，我们需要等待一段时间来检查结果
	time.Sleep(time.Second * 2)

	// 检查 Redis 中的 key 是否被删除
	exists, err := redisClient.Exists(testKey).Result()
	if err != nil {
		t.Errorf("Failed to check Redis key existence: %v", err)
	}
	if exists == 1 {
		t.Error("Expected Redis key to be deleted, but it still exists")
	}
}

// 辅助函数：清理测试环境
func cleanupTestEnv() {
	redisClient := redis.Client()
	keys, _ := redisClient.Keys("zx_notify_*").Result()
	for _, key := range keys {
		redisClient.Del(key)
	}
}

func TestRedisProcess(t *testing.T) {
	// 初始化 Redis 配置
	if err := redis.Init(&redis.Config{
		Master:          "",
		Addr:            []string{"*************:6379"},
		Password:        "kst_asv@123",
		DB:              0,
		MaxRetries:      3,
		MaxRetryBackoff: 1000,
	}); err != nil {
		t.Errorf("redis初始化失败,err: %s", err.Error())
		return
	}

	errMsg := fmt.Sprintln("Kafka消费异常, panic错误: invalid memory address or nil pointer dereference [recovered]")
	timeNow := time.Now().Format("2006-01-02 15:04:05")
	webHookUrl := "https://open.feishu.cn/open-apis/bot/v2/hook/185ead56-9553-4c8f-9271-6570f3e4ecec"
	redisKey := fmt.Sprintf("zx_notify_%s_%s", "Panic", timeNow)

	// 准备测试数据
	notifyData := notify.NotificationMessage{
		Name:       "Panic",
		Message:    errMsg,
		WebHookUrl: webHookUrl,
		CreatedAt:  timeNow,
	}

	// 序列化通知数据
	notifyDataBytes, err := json.Marshal(notifyData)
	if err != nil {
		t.Errorf("kafka consume PanicErr 序列化通知数据失败, err: %s", err.Error())
		return
	}

	// 连接redis
	redisClient := redis.Client()

	// 写入测试数据到redis
	err = redisClient.Set(redisKey, string(notifyDataBytes), time.Hour).Err()
	if err != nil {
		t.Errorf("Failed to set test data in Redis: %v", err)
		return
	}

	// 读取redis中的数据
	result, err := redisClient.Get(redisKey).Result()
	if err != nil {
		t.Errorf("Failed to get data from Redis: %v", err)
		return
	}

	t.Logf("redis result: %v", result)

	// 验证数据
	if result != string(notifyDataBytes) {
		t.Errorf("Expected data %s, got %s", notifyData, result)
		return
	}

	//发送到飞书群组
	// 开一个定时任务，每1分钟循环去发送
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	done := false
	for range ticker.C {
		if done {
			break
		}
		var data notify.NotificationMessage
		if err := json.Unmarshal([]byte(result), &data); err != nil {
			t.Errorf("解析通知数据失败: %v", err)
			return
		}

		if err := notifyData.SendNotifyMsgToFeiShu(); err != nil {
			t.Errorf("发送飞书通知失败: %v", err)
			continue
		}

		// 发送成功后删除redis key
		redisClient.Del(redisKey)
		t.Logf("告警消息发送成功，data \n redisKey: %s 删除成功: %s", result, redisKey)
		done = true
	}

	results, err := redisClient.Get(redisKey).Result()
	if err != nil {
		t.Errorf("Failed to get data from Redis: %v", err)
	}

	t.Logf("results: %s", results)
	if results == "" {
		t.Log("验证成功")
	}
}
