package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Color)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*Color)(nil))
}

type Color struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	ColorId   string    `json:"color_id"      gorm:"unique;not null"` //颜色id
	Name      string    `json:"name"          gorm:"not null"`        //名称
	CName     string    `json:"cname"`                                //中文名
	ColorNum  string    `json:"color_num"     gorm:"not null"`        //色号
	Sort      int       `json:"sort"          gorm:"not null"`        //排序
	IsWhite   *bool     `json:"is_white"`                             //打印内领标是否使用白色，默认false，为黑色

	BgFront string `json:"bg_front"` //正面模特图 背景图
	BgBack  string `json:"bg_back"`  //背面模特图 背景图
	// || false代表此颜色是浅色，打印领标用深色，true代表此颜色是深色，打印领标用浅色
	EmbCloth  string `json:"emb_cloth"`                   // 衬布编号
	ProjectId int    `json:"project_id" gorm:"default:1"` // 喷淋方案id，默认为1
}

func (c *Color) TableName() string {
	return "color"
}

// 新增颜色
func (c *Color) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(c.TableName()).Create(c).Error
}

// 查询
func (c *Color) Query() (List []*Color, err error) {
	db := mysql.NewConn().Table(c.TableName())
	if c.Name != "" {
		db = db.Where("name like ?", "%"+c.Name+"%")
	}

	if len(c.ColorId) > 0 {
		db = db.Where("color_id = ?", c.ColorId)
	}

	err = db.Order("sort asc").Find(&List).Error
	return
}

func (c *Color) GetList(pn, ps int) (list []*Color, count int, err error) {
	db := mysql.NewConn().Table(c.TableName())
	if c.Name != "" {
		db = db.Where("c_name like ?", "%"+c.Name+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("sort asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

// 根据 colorID 查询数据
func (c *Color) QueryByColorID() (err error) {
	db := mysql.NewConn().Table(c.TableName())
	if len(c.ColorId) > 0 {
		db = db.Where("color_id = ?", c.ColorId)
	}
	err = db.Order("sort asc").First(c).Error
	return
}

// 根据 colorID arr 查询数据
func (c *Color) QueryByColorIds(colorIds []string) (list []*Color, err error) {
	db := mysql.NewConn().Table(c.TableName())
	db = db.Where("color_id IN (?)", colorIds)
	err = db.Find(&list).Error
	return
}

// 查询最新的 color_id 和 sort
func (c *Color) QueryNewest() (err error) {
	return mysql.NewConn().Table(c.TableName()).Order("id desc").First(c).Error
}

func (c *Color) PutColorNum() (err error) {
	return mysql.NewConn().Model(&Color{}).Where("c_name = ?", c.CName).Update("color_num", c.ColorNum).Error
}

func (c *Color) PutColorValueByID(update map[string]interface{}) (err error) {
	err = mysql.NewConn().Model(&Color{}).Where("id = ?", c.ID).Updates(update).Error
	return
}

func (c *Color) PutColorName() (err error) {
	return mysql.NewConn().Model(&Color{}).Where("c_name = ?", c.CName).Update("name", c.Name).Error
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (c *Color) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (c *Color) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Color)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Color) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Color) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Color, 0)

	err := db.Find(&list).Error

	return list, err
}

func (c *Color) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&Color{}).Where("id IN (?)", ids).Delete(c).Error
	return
}

// 更新喷淋方案
func (c *Color) UpdateProject(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&Color{})

	if len(c.ColorId) > 0 {
		db = db.Where("color_id = ?", c.ColorId)
	}

	err = db.Update("project_id", c.ProjectId).Error
	return
}
