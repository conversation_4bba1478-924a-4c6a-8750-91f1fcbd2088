package third_shop

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ThirdShopProductInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ThirdShopProductInfo)(nil))
}

type ThirdShopProductInfoObj struct {
	ID         uint            `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
	Title      string          `json:"title,omitempty"`
	BodyHTML   string          `json:"body_html,omitempty"`
	Handle     string          `json:"handle,omitempty"`
	Options    []ProductOption `json:"options,omitempty"`
	Variants   []Variant       `json:"variants,omitempty"`
	Image      string          `json:"image,omitempty"`
	ProductID  int64           `json:"product_id,omitempty"`
	PodShopID  int64           `json:"pod_shop_id"`
	ShopDomain string          `json:"shop_domain"`
}

type ThirdShopProductInfoList struct {
	ID         uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	Title      string    `json:"title,omitempty"`
	BodyHTML   string    `json:"body_html,omitempty"`
	Handle     string    `json:"handle,omitempty"`
	Image      string    `json:"image,omitempty"`
	ProductID  int64     `json:"product_id,omitempty"`
	PodShopID  int64     `json:"pod_shop_id"`
	ShopDomain string    `json:"shop_domain"`
}

type ThirdShopProductInfo struct {
	ID         uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	Title      string    `json:"title,omitempty"`
	BodyHTML   string    `json:"body_html,omitempty"`
	Handle     string    `json:"handle,omitempty"`
	Options    string    `json:"options,omitempty"`
	Variants   string    `json:"variants,omitempty"`
	Image      string    `json:"image,omitempty"`
	ProductID  int64     `json:"product_id,omitempty"` // 第三方平台商品 ID
	PodShopID  int64     `json:"pod_shop_id"`          // pod 平台商店 ID
	ShopDomain string    `json:"shop_domain"`
}

type Variant struct {
	ID                 int64  `json:"id,omitempty"`
	ProductID          int64  `json:"product_id,omitempty"`
	Title              string `json:"title,omitempty"`
	Sku                string `json:"sku,omitempty"` // 第三方的 sku
	Position           int    `json:"position,omitempty"`
	FulfillmentService string `json:"fulfillment_service,omitempty"`
	Option1            string `json:"option1,omitempty"`
	Option2            string `json:"option2,omitempty"`
	Option3            string `json:"option3,omitempty"`
	Image              string `json:"image,omitempty"`
	DesignID           int64  `json:"design_id,omitempty"`  // 绑定到 pod 平台的设计ID
	PodSku             string `json:"pod_sku,omitempty"`    // 绑定到 pod 平台的 sku (不包含设计 id)
	Model              string `json:"model,omitempty"`      // 绑定到 pod 平台的展示图
	ColorID            string `json:"color_id,omitempty"`   // 绑定到 pod 平台的颜色 ID
	ColorName          string `json:"color_name,omitempty"` // 绑定到 pod 平台的颜色名称
	ColorNum           string `json:"color_num,omitempty"`  // 绑定到 pod 平台的颜色色号
	SizeID             string `json:"size_id,omitempty"`    // 绑定到 pod 平台的尺码 ID
	SizeName           string `json:"size_name,omitempty"`  // 绑定到 pod 平台的尺码名称
}

type ProductOption struct {
	ID        int64    `json:"id,omitempty"`
	ProductID int64    `json:"product_id,omitempty"`
	Name      string   `json:"name,omitempty"`
	Position  int      `json:"position,omitempty"`
	Values    []string `json:"values,omitempty"`
}

func (t *ThirdShopProductInfo) TableName() string {
	return "third_shop_product_info"
}

// 新增sku
func (t *ThirdShopProductInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(t.TableName())
	return db.Create(t).Error
}

// 查询第三方店铺商品列表
func (t *ThirdShopProductInfo) QueryList(page, size int) (list []*ThirdShopProductInfo, count int64, err error) {

	db := mysql.NewConn().Model(t)

	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}

	if t.PodShopID > 0 {
		db = db.Where("pod_shop_id = ?", t.PodShopID)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Offset((page - 1) * size).Limit(size).Order("id desc").Find(&list).Error

	return
}

// 查询
func (t *ThirdShopProductInfo) Query() (err error) {

	db := mysql.NewConn().Model(t)

	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}

	if t.PodShopID > 0 {
		db = db.Where("pod_shop_id = ?", t.PodShopID)
	}

	if t.ProductID > 0 {
		db = db.Where("product_id = ?", t.ProductID)
	}

	return db.First(t).Error
}

// 根据商店 id 删除数据
func (t *ThirdShopProductInfo) DeleteByShopID(tx ...*gorm.DB) (list []*ThirdShopProductInfo, err error) {

	if t.PodShopID < 1 {
		return nil, nil
	}

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Table(t.TableName()).Where("pod_shop_id = ?", t.PodShopID).Find(&list).Delete(&ThirdShopProductInfo{}).Error
	return
}

// 查询第三方店铺商品列表
func (t *ThirdShopProductInfo) Exist() (bool, error) {

	var count int = 0

	db := mysql.NewConn().Model(t)

	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}

	if t.ProductID > 0 {
		db = db.Where("product_id = ?", t.ProductID)
	}

	db = db.Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count > 0, nil
}

// 更新
func (t *ThirdShopProductInfo) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&ThirdShopProductInfo{})
	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}
	return db.Update(t).Error
}

// 根据 map 根据，需要注意点是，这里只更新 string 类型
func (t *ThirdShopProductInfo) UpdateMap(dataMap map[string]string, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&ThirdShopProductInfo{})
	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}

	for k, v := range dataMap {
		db = db.Update(k, v)
	}

	return db.Update(t).Error
}
func (c *ThirdShopProductInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ThirdShopProductInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *ThirdShopProductInfo) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ThirdShopProductInfo{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ThirdShopProductInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ThirdShopProductInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ThirdShopProductInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ThirdShopProductInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
