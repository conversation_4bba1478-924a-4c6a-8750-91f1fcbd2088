package article

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*HelpArticle)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*HelpArticle)(nil))
}

type HelpArticle struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	ClassifyName string `json:"classify_name"`              //分类名称
	Title        string `json:"title"`                      //标题
	CTR          int    `json:"ctr"`                        //点击率
	Content      string `json:"content"  gorm:"type:BLOB;"` //内容
	Sort         int    `json:"sort"`                       //排序

	MetaDescription string `json:"meta_description" gorm:"type:BLOB;"` // seo mate 描述
	SeoTitle        string `json:"seo_title"`                          // seo 标题
}

func (h *HelpArticle) TableName() string {
	return "help_article"
}

func (h *HelpArticle) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(h.TableName()).Create(h).Error
}

func (h *HelpArticle) Update() error {
	return mysql.NewConn().Model(&HelpArticle{}).Where("id = ?", h.ID).Updates(h).Error
}

// 获取文章详情 并且更新点击率
func (h *HelpArticle) GetContent() (err error) {
	err = mysql.NewConn().Model(h).Where("id = ?", h.ID).Update("ctr", gorm.Expr("ctr + ?", 1)).First(h).Error
	return
}

// 分页筛选获取列表
func (h *HelpArticle) GetList(pn, ps int) (list []*HelpArticle, count int, err error) {

	db := mysql.NewConn().Table(h.TableName())

	if h.ClassifyName != "" {
		db = db.Where("classify_name = ?", h.ClassifyName)
	}
	if h.Title != "" {
		db = db.Where("title LIKE ?", "%"+h.Title+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("sort desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (h *HelpArticle) Delete() (err error) {
	return mysql.NewConn().Table(h.TableName()).Where("id = ?", h.ID).Delete(h).Error
}

func (c *HelpArticle) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*HelpArticle, 0)

	err := db.Find(&list).Error

	return list, err
}

func (h *HelpArticle) DeleteByIds(ids []uint, param ...interface{}) (err error) {
	db := mysql.NewConn()
	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&HelpArticle{}).Where("id IN (?)", ids).Delete(h).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *HelpArticle) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *HelpArticle) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(HelpArticle)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *HelpArticle) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
