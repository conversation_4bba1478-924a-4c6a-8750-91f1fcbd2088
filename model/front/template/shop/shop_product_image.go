package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ShopProductImage)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ShopProductImage)(nil))
}

type ShopProductImage struct {
	ID                      int64      `json:"id,omitempty"`
	ShopifyImageID          int64      `json:"shopify_image_id"`
	ProductID               int64      `json:"product_id,omitempty"` //pod平台商品
	ShopProductID           int64      `json:"shop_product_id"`      //第三方平台商品
	ListingID               int64      `json:"listing_id"`           //etsy listing ID (商品id)
	ListingImageID          int64      `json:"listing_image_id"`
	UserID                  uint       `json:"user_id"`
	ShopId                  uint       `json:"shop_id"`
	Position                int        `json:"position,omitempty"`
	CreatedAt               *time.Time `json:"created_at,omitempty"`
	UpdatedAt               *time.Time `json:"updated_at,omitempty"`
	Width                   int        `json:"width,omitempty"`
	Height                  int        `json:"height,omitempty"`
	Src                     string     `json:"src,omitempty"`
	Attachment              string     `json:"attachment,omitempty"`
	FileName                string     `json:"file_name,omitempty"`
	VariantIds              string     `json:"variant_ids,omitempty"`
	SrcPod                  string     `json:"src_pod"` //记录在pod平台的源图片Url
	Skus                    string     `json:"skus"`
	WixProductID            string     `json:"wix_product_id"`             // wix 商品 id
	WixImageID              string     `json:"wix_image_id"`               // wix 图片 id
	SSImgID                 string     `json:"ss_img_id"`                  // squarespace 图片ID
	SSProductID             string     `json:"ss_product_id"`              // squarespace 商品ID
	ShopifyGraphqlProductID string     `json:"shopify_graphql_product_id"` // shopify graphql 商品 id
	ShopifyGraphqlImageID   string     `json:"shopify_graphql_image_id"`   // shopify graphql 图片 id
}

func (s *ShopProductImage) TableName() string {
	return "shop_product_image"
}

// 新增图片信息
func (s *ShopProductImage) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 判断 wix 图片是否已经存在
func (s *ShopProductImage) IsExisted() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(s.TableName())

	if len(s.WixImageID) > 0 && len(s.WixProductID) > 0 {
		db = db.Where("wix_image_id = ?", s.WixImageID)
		db = db.Where("wix_product_id = ?", s.WixProductID)
	} else if len(s.SrcPod) > 0 {
		db = db.Where("src_pod = ?", s.SrcPod)
	}

	db = db.Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 1, nil
}

// 更新图片信息
func (s *ShopProductImage) Update(tx ...*gorm.DB) error {
	return mysql.NewConn().Model(&ShopProductImage{}).Where("id = ?", s.ID).Update(s).Error
}

// 根据 product id 删除
func (s *ShopProductImage) DeleteImagesByShopProductId(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if s.ShopProductID < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("shop_product_id = ?", s.ShopProductID).Delete(s).Error
}

// 根据 product id 删除
func (s *ShopProductImage) DeleteImagesByPodShopProductId(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if s.ShopProductID < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("product_id = ?", s.ShopProductID).Delete(s).Error
}

// 根据 主键 id 删除
func (s *ShopProductImage) DeleteImagesByIds(ids []int64, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if len(ids) < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("id IN (?)", ids).Delete(s).Error
}

// 根据 WixImageID 删除
func (s *ShopProductImage) DeleteImagesByWixImageID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if len(s.WixImageID) < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("wix_image_id = ?", s.WixImageID).Delete(s).Error
}

// 根据 ListingID 查找 shopImgList
func (s *ShopProductImage) GetByListingID() (list []*ShopProductImage, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("listing_id = ?", s.ListingID).Find(&list).Error
	return
}

// 根据 SSProductID 查找 shopImgList
func (s *ShopProductImage) GetBySSPid() (list []*ShopProductImage, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("ss_product_id = ?", s.SSProductID).Find(&list).Error
	return
}

// 根据 商店商品id 查找 shopImgList
func (s *ShopProductImage) GetByPodShopProductId() (list []*ShopProductImage, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("product_id = ?", s.ProductID).Find(&list).Error
	return
}

// 查询一个商店商品所有已存储图片同步信息
func (s *ShopProductImage) QueryList() (list []*ShopProductImage, err error) {
	db := mysql.NewConn().Table(s.TableName())
	if len(s.WixProductID) > 0 {
		db = db.Where("wix_product_id = ?", s.WixProductID)
	}
	err = db.Find(&list).Error
	return
}

// 根据 ListingID AND ListingImageID 删除图片
func (s *ShopProductImage) DelImgByListingID() error {
	err := mysql.NewConn().Table(s.TableName()).Where("listing_id = ?", s.ListingID).Where("listing_image_id = ?", s.ListingImageID).Delete(s).Error
	return err
}

// Squarespace 使用方法，根据 SSProductID and SSImgID 删除图片记录
func (s *ShopProductImage) DelBySSImg() error {
	return mysql.NewConn().Table(s.TableName()).Where("ss_product_id = ?", s.SSProductID).Where("ss_img_id = ?", s.SSImgID).Delete(s).Error
}

func (c *ShopProductImage) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ShopProductImage, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *ShopProductImage) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ShopProductImage{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ShopProductImage) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ShopProductImage) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ShopProductImage)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[uint(detailObj.ID)] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ShopProductImage) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
