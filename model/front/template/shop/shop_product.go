package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	design "zx/zx-consistency/model/front/template/design"
	model2 "zx/zx-consistency/model/front/user"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ShopProduct)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ShopProduct)(nil))
}

const (
	_ = iota
	STATUS_TO_BE_UPDATED
	STATUS_UPDATED
	STATUS_IGNOR
)

var FALSE = false
var TRUE = true

type VariantReq struct {
	Size        string `json:"size"`
	Color       string `json:"color"`
	Cost        int64  `json:"cost"`         //成本，单位美分
	RetailPrice int64  `json:"retail_price"` //零售价，单位美分
	//Profit      int64  `json:"profit"`       //利润，单位美分
	Disabled bool   `json:"disabled"`                   //为true时，表示库存不足，shopify不创建此变体，etsy禁用此变体 ,Squarespace不创建
	Sku      string `json:"sku"`                        // pod 平台的 sku（spu+颜色+尺码）
	DesignID int64  `form:"design_id" json:"design_id"` // 模板 id
}

type ShopProduct struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"ID"` //第三方平台商品
	Deleted   int       `json:"deleted"       gorm:"default:0"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Versions  int       `json:"versions"` // 版本号  1 2 3 4累加，取自来源模板设计的版本号
	SkuTag    string    `json:"sku_tag"`  // sku标签 ABCDEFG,手动标记

	UserID    uint   `json:"user_id"`
	DesignID  int64  `json:"design_id"`
	ProductID uint   `json:"product_id"` //podpartner平台商品
	Spu       string `json:"spu"`
	ShopId    uint   `json:"shop_id"`
	//MaterialPath string    `json:"material_path"` //设计信息
	ExtraPrice                     int        `json:"extra_price"` //设计面的加价，可以为 0
	BodyHTML                       string     `json:"body_html"  gorm:"type:BLOB;"`
	Vendor                         string     `json:"vendor"`
	ProductCollectionID            int64      `json:"product_collection_id"` // 目前用于 shopify woocommerce
	Tags                           string     `json:"tags"`
	Title                          string     `json:"title"`
	ProductType                    string     `json:"product_type,omitempty"`
	Handle                         string     `json:"handle,omitempty"` // 如果是 woocommerce 的话，就直接是商品的链接
	PublishedAt                    *time.Time `json:"published_at,omitempty"`
	PublishedScope                 string     `json:"published_scope,omitempty"`
	TemplateSuffix                 string     `json:"template_suffix,omitempty"`
	MetafieldsGlobalTitleTag       string     `json:"metafields_global_title_tag,omitempty"`
	MetafieldsGlobalDescriptionTag string     `json:"metafields_global_description_tag,omitempty"`
	//Metafields                     []goshopify.Metafield `json:"metafields,omitempty"`
	AdminGraphqlAPIID string `json:"admin_graphql_api_id,omitempty"`

	Status         int    `json:"status,omitempty"`                   //Synced、Sync Failed、Draft、To be Updated、Ignore、Synchronizing  已同步、同步失败、 草稿、 待更新、忽略、同步中
	FailedSyncDesc string `json:"failed_sync_desc" gorm:"type:BLOB;"` // 记录同步失败时的原因
	Design         string `json:"design"  gorm:"type:BLOB;"`          //一堆字段，记录各种价格，描述等可编辑的内容
	CustomCount    int    `json:"custom_count"  gorm:"default:0"`     //半自定义图层数量 0代表没有  范围 0-3
	EmbCount       int    `json:"emb_count" gorm:"default:0"`         // 刺绣框数量  0代表没有

	ModelImg     string `json:"model_img"  gorm:"type:BLOB;"`     //模特展示图信息
	BrandLabelID uint   `json:"brand_label_id"  gorm:"default:0"` //品牌领标ID  为0是未使用

	ShopProductId     int64  `json:"shop_product_id"`                          // shopify 商品 id
	ListingID         int64  `json:"listing_id"`                               // etsy listing ID (商品id)
	SSProductID       string `json:"ss_product_id"`                            // Squarespace 的商品 id
	IsNewShipping     bool   `form:"is_new_shipping" json:"is_new_shipping"`   // true 的话，新建模板 false 使用shipping_profile_id
	ShippingProfileID int64  `json:"shipping_profile_id"`                      // etsy商品的物流模板 ID 一旦有值，则不创建新模板
	TaxonomyID        int64  `form:"taxonomy_id" json:"taxonomy_id,omitempty"` // etsy 商品分类模板
	ProductUrl        string `json:"product_url"`                              // 商品在etsy的链接地址 | 在 Squarespace 的链接地址

	Collect string `json:"collect"` // 存储状态

	Size        string `json:"size,omitempty"`                           // 草稿
	Color       string `json:"color,omitempty"`                          // 草稿
	Model       string `json:"model"`                                    // 草稿 展示图片 url
	BaseColor   string `json:"base_color"`                               // 草稿 主展示色
	VariantsReq string `json:"variants_req,omitempty" gorm:"type:BLOB;"` // 草稿 变体,包含 pod 平台的 sku
	ImageUrls   string `json:"image_urls"  gorm:"type:BLOB;"`            // 草稿 所有图片的url
	ImageInfos  string `json:"image_infos"  gorm:"type:BLOB;"`           // 草稿 所有图片的信息序列化，套装，具体结构为 type ImageInfo
	SizeGuide   int    `json:"size_guide"`                               // 草稿 是否生成尺码图
	Display     bool   `json:"display"`                                  // 草稿 是否在商店显示 true显示 false草稿
	ImgSizeUrl  string `json:"img_size_url"`                             // 草稿 尺码图片
	//ProductStatus string `json:"product_status"`                       // 草稿 商品状态，是否

	ThirdProductIDStr      string `json:"third_product_id_str"`      // 第三方平台 string 类型的 product id，目前用于 wix|shopify GraphQL
	ProductCollectionIDStr string `json:"product_collection_id_str"` // 第三方平台 string 类型的 collection，目前用于 wix | 第三方平台 squarespace 的 StorePageID 共用 | shopify GraphQL

	WooProductID        int64 `json:"woo_product_id"`         // woocommerce 平台的商品 id
	WooColorAttributeID int64 `json:"woo_color_attribute_id"` // woocommerce color 选项 id
	WooSizeAttributeID  int64 `json:"woo_size_attribute_id"`  // woocommerce size 选项 id

	ProductUrlBase string `json:"product_url_base"` // wix 商品 url，base
	ProductUrlPath string `json:"product_url_path"` // wix 商品 url，path

	BindColors    string `json:"bind_colors"`                      // 参与绑定的颜色id，与第三方的商品绑定时使用，一维 string 数组序列化
	BindSize      string `json:"bind_size"`                        // 参与绑定的尺码id，与第三方的商品绑定时使用，一维 string 数组序列化
	IsBindProduct *bool  `json:"is_bind_product" gorm:"default:0"` // 是否为绑定生成的商店商品
	SrcDesignID   uint   `json:"src_design_id"`                    // pod 原设计 id(可能是商品设计，也可能是商店商品设计)，只有在绑定的时候才有用到
	SrcDesignType int    `json:"src_design_type"`                  // pod平台的设计类型，1：模板设计，2：商店商品设计

	Variants []ShopProductVariant `json:"variants" gorm:"foreignkey:shop_product_id;references:shop_product_id"`
	Images   []ShopProductImage   `json:"images" gorm:"foreignkey:shop_product_id;references:shop_product_id"`
	Options  []ShopProductOption  `json:"options" gorm:"foreignkey:shop_product_id;references:shop_product_id"`
	//ProductID    int64            `json:"product_id"    gorm:"not null"` //由 shopify 端生成的 productID

	EVariants []ShopProductVariant `json:"Evariants" gorm:"foreignkey:listing_id;association_foreignkey:listing_id"`
	EImages   []ShopProductImage   `json:"Eimages" gorm:"foreignkey:listing_id;association_foreignkey:listing_id"`
	EOptions  []ShopProductOption  `json:"Eoptions" gorm:"foreignkey:listing_id;association_foreignkey:listing_id"`

	ProductSize []model.ProductSize   `json:"product_size" gorm:"foreignkey:product_id;association_foreignkey:product_id"`
	ShopInfo    model2.AssociatedShop `json:"shop_info" gorm:"foreignkey:id;association_foreignkey:shop_id"`
	//新增吊牌、包装绑定, 只记录 ID ，通过 Preload 获取数据
	TagDesignID    uint               `json:"tag_design_id" gorm:"default:0"`                                               // 与模板绑定的 吊牌设计ID
	TagDesignInfo  *design.TagDesign  `json:"tag_design_info"  gorm:"foreignkey:tag_design_id;association_foreignkey:id"`   // 吊牌信息
	PackDesignID   uint               `json:"pack_design_id"  gorm:"default:0"`                                             // 包装设计ID
	PackDesignInfo *design.PackDesign `json:"pack_design_info"  gorm:"foreignkey:pack_design_id;association_foreignkey:id"` // 吊牌信息

	IsBlank bool `json:"is_blank"` // 是否空白模板

	RoleType          int            `form:"role_type" json:"role_type" gorm:"default:0"`                                // 区分套装及主从， 参考 const，ShopProductRole
	MasterID          uint           `form:"master_id" json:"master_id" gorm:"default:0"`                                // 如果是套装的话，这里就是主设计的主键id
	SlaveShopProducts []*ShopProduct `json:"slave_shop_products"  gorm:"foreignkey:master_id;association_foreignkey:id"` // 从设计
	SubTitle          string         `json:"sub_title"`                                                                  // 套装子项的标题，用于颜色的命名
	CustomColorInfos  string         `json:"custom_colors" gorm:"type:BLOB;"`                                            // 套装，自命名的颜色名称，具体结构为 struct CustomInfo
	Currency          string         `json:"currency"  gorm:"default:'USD'"`                                             // 货币币种

	LightColorSet string `json:"light_color_set"  gorm:"type:BLOB;"` // 自定义打印设置浅色
	DarkColorSet  string `json:"dark_color_set"   gorm:"type:BLOB;"` // 自定义打印设置深色
	AuditState    int    `json:"audit_state"`                        // 审核状态 1 待审核  2 审核通过  3 审核不通过
	//	2025-3-18 11:31:35 zc 以下新增字段为适配刺绣支持图片，制版任务等。
	DigitizationList   string `json:"digitization_list"  gorm:"type:BLOB;"` // 制版任务信息
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态
	DigitizationCount  int    `json:"digitization_count"  gorm:"default:0"` // 制版任务数量
	DigitizationIDList string `json:"digitization_id_list"`                 // 将制版任务ID 放在这里，用于快速筛选
	DigitizationEdit   int    `json:"digitization_edit" gorm:"default:0"`   // 制版任务编辑信息 0 1 编辑过  2未编辑
}

type ImageInfo struct {
	//ShopProductID uint   `form:"shop_product_id" json:"shop_product_id"` // 商店商品 id，创建商品时没有，更新时使用
	DesignID    int64  `form:"design_id" json:"design_id"`       // 模板 id，不是商店商品模板 id
	Type        string `form:"type" json:"type"`                 // 类型： 主图main|普通normal|尺码size
	Spu         string `form:"spu" json:"spu"`                   //
	ColorID     string `form:"color_id" json:"color_id"`         // 颜色 id
	SurfaceName string `form:"surface_name" json:"surface_name"` // 模特展示面front|back
	Url         string `form:"url" json:"url"`                   //
	ModelTag    string `json:"model_tag"`                        // 图片所属的模特标签
}

type CustomColorInfo struct {
	ColorID    string `json:"color_id"`    // 颜色 id，例如 Y01
	ColorNum   string `json:"color_num"`   // 颜色的 rgb 值，例如 #70a47f
	CustomName string `json:"custom_name"` // 客户自定义颜色名称
}

func (s *ShopProduct) TableName() string {
	return "shop_product"
}

// 新增商品模板 gorm v2
func (s *ShopProduct) Create(tx ...*gorm2.DB) error {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 新增商品模板 gorm v1
func (s *ShopProduct) CreateV1(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 更新商品模板
func (s *ShopProduct) Update(tx ...*gorm2.DB) error {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	//return db.Table(s.TableName()).Where("product_id = ?", s.ProductID).Updates(s).Error
	return db.Session(&gorm2.Session{FullSaveAssociations: true}).Where("id = ?", s.ID).Updates(s).First(s).Error
}

// 更新商品模板
func (s *ShopProduct) UpdateSuite(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	//return db.Table(s.TableName()).Where("product_id = ?", s.ProductID).Updates(s).Error
	return db.Model(&ShopProduct{}).Where("id = ?", s.ID).Updates(s).First(s).Error
}

// 更新商品模板
func (s *ShopProduct) UpdateMasterID(slaveIDs []uint, tx ...*gorm2.DB) error {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	//return db.Table(s.TableName()).Where("product_id = ?", s.ProductID).Updates(s).Error
	return db.Model(&ShopProduct{}).Where("id IN (?)", slaveIDs).Updates(map[string]interface{}{
		"master_id": s.MasterID,
	}).Error
}

// 强制更新 tagDesignID
func (s *ShopProduct) UpdateTagDesign(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&ShopProduct{})

	if s.ID > 0 {
		db = db.Where("id = ? OR master_id = ?", s.ID, s.ID)
	}
	if s.UserID > 0 {
		db = db.Where("user_id = ?", s.UserID)
	}

	return db.Updates(map[string]interface{}{
		"tag_design_id": s.TagDesignID,
	}).First(s).Error
}

// 强制更新 pack_design_id 传0为解绑
func (s *ShopProduct) UpdatePackDesign(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&ShopProduct{})

	if s.ID > 0 {
		db = db.Where("id = ? OR master_id = ?", s.ID, s.ID)
	}
	if s.UserID > 0 {
		db = db.Where("user_id = ?", s.UserID)
	}

	return db.Updates(map[string]interface{}{
		"pack_design_id": s.PackDesignID,
	}).First(s).Error
}

// 批量绑定吊牌设计 by shopID
func (s *ShopProduct) BatchUpdateTagDesign(tagDesignID int, tx ...*gorm.DB) (count int, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&ShopProduct{}).Where("shop_id = ?", s.ShopId).Where("deleted != ?", 1)

	// 这边执行解绑商店时 与商品解绑, 当tagDesignID不为0时，代表解绑tagDesignID
	if tagDesignID != 0 {

		db = db.Where("tag_design_id = ?", tagDesignID)
		// 统计绑定的非从设计数量
		err = db.Where("role_type != ?", ShopProductRoleSlave).Count(&count).Error
		if err != nil {
			return count, err
		}

		db = db.Updates(map[string]interface{}{
			"tag_design_id": 0,
		})

		err = db.Error
		//count = int(db.RowsAffected)
		return count, err
	}

	db = db.Updates(map[string]interface{}{
		"tag_design_id": s.TagDesignID,
	})

	err = db.Error
	count = int(db.RowsAffected)

	return count, err
}

// 批量绑定包装设计 by shopID
func (s *ShopProduct) BatchUpdatePackDesign(packDesignID int, tx ...*gorm.DB) (count int, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&ShopProduct{}).Where("shop_id = ?", s.ShopId).Where("deleted != ?", 1)

	// 这边执行解绑商店时 与商品解绑, 当packDesignID不为0时，代表解绑packDesignID
	if packDesignID != 0 {

		db = db.Where("pack_design_id = ?", packDesignID)
		// 统计绑定的非从设计数量
		err = db.Where("role_type != ?", ShopProductRoleSlave).Count(&count).Error
		if err != nil {
			return count, err
		}

		db = db.Updates(map[string]interface{}{
			"pack_design_id": 0,
		})

		err = db.Error
		//count = int(db.RowsAffected)
		return count, err
	}

	db = db.Updates(map[string]interface{}{
		"pack_design_id": s.PackDesignID,
	})

	err = db.Error
	count = int(db.RowsAffected)

	return count, err
}

func (s *ShopProduct) UpdateByGorm1WithTx(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&ShopProduct{}).Where("id = ?", s.ID).Updates(s).First(s).Error
}

func (s *ShopProduct) UpdateByGorm1WithTx_Suit(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	id := s.ID
	s.ID = 0
	return db.Model(&ShopProduct{}).Where("id = ? OR master_id = ?", id, id).Updates(s).First(s).Error
}

// 使用 gorm1 更新 size_guide
func (s *ShopProduct) UpdateWithSizeGuideG1(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	sizeGuide := s.SizeGuide

	db = db.Model(&ShopProduct{}).Where("id = ?", s.ID)

	err = db.Updates(s).First(s).Error
	if err != nil {
		return
	}

	// 更新 size_guide
	err = db.Update("size_guide", sizeGuide).Error
	if err != nil {
		return
	}
	s.SizeGuide = sizeGuide

	return
}

// 更新模板设计，在保存草稿中使用，用户处理 SizeGuide 为 0 的情况
func (s *ShopProduct) UpdateWithSizeGuide(tx ...*gorm2.DB) (err error) {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	sizeGuideObj := ShopProduct{ID: s.ID, SizeGuide: s.SizeGuide}

	db = db.Session(&gorm2.Session{FullSaveAssociations: true}).Where("id = ?", s.ID)

	err = db.Updates(s).First(s).Error
	if err != nil {
		return
	}

	err = db.Select("size_guide").Updates(sizeGuideObj).Error
	if err != nil {
		return
	}
	s.SizeGuide = sizeGuideObj.SizeGuide
	return
}

// 更新模板设计，在保存草稿中使用，用户处理 SizeGuide 为 0 的情况
func (s *ShopProduct) UpdateWithSizeGuideSuit(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	sizeGuideObj := ShopProduct{ID: s.ID, SizeGuide: s.SizeGuide}

	db = db.Model(&ShopProduct{})
	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}

	err = db.Updates(s).First(s).Error
	if err != nil {
		return
	}

	err = db.Select("size_guide").Updates(sizeGuideObj).Error
	if err != nil {
		return
	}
	s.SizeGuide = sizeGuideObj.SizeGuide
	return
}

func (s *ShopProduct) UpdateByGorm1() error {
	return mysql.NewConn().Model(&ShopProduct{}).Where("id = ?", s.ID).Updates(s).First(s).Error
}

// 更新商品模板
func (s *ShopProduct) UpdateStatus(tx ...*gorm.DB) error {

	db := mysql.NewConn().Table(s.TableName())
	if len(tx) != 0 {
		db = tx[0]
	}

	if s.MasterID > 0 {
		db = db.Where("id = ? OR master_id = ?", s.MasterID, s.MasterID)
	} else {
		db = db.Where("id = ?", s.ID)
	}

	updateMap := make(map[string]interface{})
	updateMap["status"] = s.Status
	updateMap["updated_at"] = time.Now()

	return db.Updates(updateMap).Error

}

// 判断商店商品是否存在
func (s *ShopProduct) Exist(tx ...*gorm.DB) (isExist bool, err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	count := 0
	db = db.Table(s.TableName())
	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.ShopProductId > 0 {
		db = db.Where("shop_product_id = ?", s.ShopProductId)
	}

	// 原设计ID
	if s.SrcDesignID > 0 {
		db = db.Where("src_design_id = ?", s.SrcDesignID)
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}
	db = db.Where("is_bind_product = ?", isBindProduct)

	db = db.Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count > 0, nil
}

// 查询半个商店商品信息
func (s *ShopProduct) GetOneByID() error {
	return mysql.NewConn().Model(s).Where("id = ?", s.ID).First(s).Error
}

// 查询一个商品模板信息
func (s *ShopProduct) Query(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	db = db.Model(s).Where("is_bind_product = ?", isBindProduct)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.MasterID > 0 {
		db = db.Where("master_id = ?", s.MasterID)
	}

	return db.Preload("Variants").Preload("Images").Preload("Options").
		Preload("TagDesignInfo").Preload("PackDesignInfo").First(s).Error
}

// 简单查询，不进行 preload
func (s *ShopProduct) QuerySimple(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	db = db.Model(s).Where("is_bind_product = ?", isBindProduct)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.MasterID > 0 {
		db = db.Where("master_id = ?", s.MasterID)
	}
	if s.UserID > 0 {
		db = db.Where("user_id = ?", s.UserID)
	}

	return db.Preload("TagDesignInfo").Preload("PackDesignInfo").First(s).Error
}

// 查询一个商品模板信息
func (s *ShopProduct) GetOne(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	db = db.Model(s).Where("is_bind_product = ?", isBindProduct)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.MasterID > 0 {
		db = db.Where("master_id = ?", s.MasterID)
	}

	return db.Preload("ProductSize").First(s).Error
}

// 查询一个商品模板信息
func (s *ShopProduct) QueryWithSlave(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	db = db.Model(s).Where("is_bind_product = ?", isBindProduct)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.MasterID > 0 {
		db = db.Where("master_id = ?", s.MasterID)
	}

	return db.Preload("Variants").Preload("Images").Preload("Options").
		Preload("TagDesignInfo").Preload("PackDesignInfo").Preload("SlaveShopProducts").First(s).Error
}

// 查询一个所有未删除的
func (s *ShopProduct) QueryAndSubDeleted(tx ...*gorm.DB) (list []*ShopProduct, err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	db = db.Model(s).Where("is_bind_product = ?", isBindProduct)

	if s.ShopId > 0 {
		db = db.Where("shop_id = ?", s.ShopId)
	}

	err = db.Where("deleted != ?", 1).Order("id desc").Find(&list).Error

	return
}

// 查询一个商品模板信息
func (s *ShopProduct) QueryOne(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(s)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}

	if len(s.ThirdProductIDStr) > 0 {
		db = db.Where("third_product_id_str = ?", s.ThirdProductIDStr)
	}
	err = db.First(s).Error
	return
}

// 查询一个商品模板信息
func (s *ShopProduct) QueryBindProduct(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(s).Where("is_bind_product = ?", true)

	if s.ShopProductId > 0 {
		db = db.Where("shop_product_id = ?", s.ShopProductId)
	}

	if s.SrcDesignID > 0 {
		db = db.Where("src_design_id = ?", s.SrcDesignID)
	}

	if s.SrcDesignType > 0 {
		db = db.Where("src_design_type = ?", s.SrcDesignType)
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	return db.Where("is_bind_product = ?", isBindProduct).First(s).Error
}

func (s *ShopProduct) QueryByPreload(preload ...string) error {

	db := mysql.NewConn().Table(s.TableName())
	if len(preload) != 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	err := db.Where("id = ?", s.ID).Where("is_bind_product = ?", isBindProduct).First(s).Error

	return err
}

// 查询一个商品模板信息
func (s *ShopProduct) QueryByIdArray(idList []uint, tx ...*gorm.DB) (list []*ShopProduct, err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	err = db.Model(s).Where("is_bind_product = ?", isBindProduct).Where("id IN (?)", idList).Find(&list).Error
	return
}

/*
_                                  = iota

	SHOP_PRODUCT_STATUS_SYNCED         // 1 已同步
	SHOP_PRODUCT_STATUS_SYNC_FAILED    // 2 同步失败
	SHOP_PRODUCT_STATUS_DRAFT          // 3 草稿
	SHOP_PRODUCT_STATUS_TO_BE_UPDATEED // 4 待更新
	SHOP_PRODUCT_STATUS_IGNORE         // 5 忽略
	SHOP_PRODUCT_STATUS_SYNCCHROMIZING // 6 同步中
*/
const (
	_                                  = iota
	SHOP_PRODUCT_STATUS_SYNCED         // 已同步
	SHOP_PRODUCT_STATUS_SYNC_FAILED    // 同步失败
	SHOP_PRODUCT_STATUS_DRAFT          // 草稿
	SHOP_PRODUCT_STATUS_TO_BE_UPDATEED // 待更新
	SHOP_PRODUCT_STATUS_IGNORE         // 忽略
	SHOP_PRODUCT_STATUS_SYNCHRONIZING  // 同步中
)

const (
	ShopProductRoleDefault = 0  // 商店商品模板，旧数据
	ShopProductRoleNormal  = 1  // 商店商品模板，普通
	ShopProductRoleMaster  = 10 // 商店商品模板，套装，主
	ShopProductRoleSlave   = 20 // 商店商品模板，套装，从
)

// 查询一个店铺所有模板信息
func (s *ShopProduct) QueryList(page, size int, brand_state ...int) (list []*ShopProduct, count, syncedCount, toBeUpdatedCount, ignoreCount, draftCount, synFailed int, err error) {
	db := mysql.NewConn().Table(s.TableName()).Where("deleted != ?", 1)

	if s.ShopId > 0 {
		db = db.Where("shop_id = ?", s.ShopId)
	}

	// 目前在商店商品列表中不展示 从设计
	db = db.Where("role_type != ? ", ShopProductRoleSlave)

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_SYNCED).Count(&syncedCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_TO_BE_UPDATEED).Count(&toBeUpdatedCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_IGNORE).Count(&ignoreCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_DRAFT).Count(&draftCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_SYNC_FAILED).Count(&synFailed).Error
	if err != nil {
		return
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if s.Status > 0 {
		db = db.Where("status = ?", s.Status)
	}

	if len(brand_state) > 0 {
		switch brand_state[0] {
		case 1:
			if s.PackDesignID != 0 {
				db = db.Where("pack_design_id = ?", s.PackDesignID)
			}
			if s.TagDesignID != 0 {
				db = db.Where("tag_design_id = ?", s.TagDesignID)
			}
		case 2:
			if s.PackDesignID != 0 {
				db = db.Where("pack_design_id != ?", s.PackDesignID)
			}
			if s.TagDesignID != 0 {
				db = db.Where("tag_design_id != ?", s.TagDesignID)
			}
		}
	}

	db = db.Preload("TagDesignInfo").Preload("PackDesignInfo")

	db = db.Preload("ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	db = db.Preload("SlaveShopProducts").Preload("SlaveShopProducts.ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	err = db.Where("is_bind_product = ?", isBindProduct).Offset((page - 1) * size).Limit(size).Order("id desc").Find(&list).Error
	return
}

// 查询一个店铺所有模板信息
func (s *ShopProduct) QueryListSearch(page, size int, brand_state ...int) (list []*ShopProduct, count, syncedCount, toBeUpdatedCount, ignoreCount, draftCount, synFailed int, err error) {
	db := mysql.NewConn().Table(s.TableName()).Where("deleted != ?", 1)

	if s.ShopId > 0 {
		db = db.Where("shop_id = ?", s.ShopId)
	}

	db = db.Where("title Like ? OR (title NOT Like ? AND sub_title like ?)", "%"+s.Title+"%", "%"+s.Title+"%", "%"+s.Title+"%")

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_SYNCED).Count(&syncedCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_TO_BE_UPDATEED).Count(&toBeUpdatedCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_IGNORE).Count(&ignoreCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_DRAFT).Count(&draftCount).Error
	if err != nil {
		return
	}

	err = db.Where("status = ?", SHOP_PRODUCT_STATUS_SYNC_FAILED).Count(&synFailed).Error
	if err != nil {
		return
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if s.Status > 0 {
		db = db.Where("status = ?", s.Status)
	}

	if len(brand_state) > 0 {
		switch brand_state[0] {
		case 1:
			if s.PackDesignID != 0 {
				db = db.Where("pack_design_id = ?", s.PackDesignID)
			}
			if s.TagDesignID != 0 {
				db = db.Where("tag_design_id = ?", s.TagDesignID)
			}
		case 2:
			if s.PackDesignID != 0 {
				db = db.Where("pack_design_id != ?", s.PackDesignID)
			}
			if s.TagDesignID != 0 {
				db = db.Where("tag_design_id != ?", s.TagDesignID)
			}
		}
	}

	db = db.Preload("TagDesignInfo").Preload("PackDesignInfo")

	db = db.Preload("ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	db = db.Preload("SlaveShopProducts").Preload("SlaveShopProducts.ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	isBindProduct := s.IsBindProduct
	if isBindProduct == nil {
		isBindProduct = &FALSE
	}

	// 如果是有商店商品名称筛选的话，就查两倍
	err = db.Where("is_bind_product = ?", isBindProduct).Offset((page - 1) * size).Limit(size * 2).Order("id desc").Find(&list).Error
	return
}

func (s *ShopProduct) QueryListByBrand() (list []*ShopProduct, err error) {
	db := mysql.NewConn().Table(s.TableName()).Where("deleted != ?", 1)

	// 去除套装的从设计
	db = db.Where("role_type != ?", ShopProductRoleSlave)

	if s.ShopId > 0 {
		db = db.Where("shop_id = ?", s.ShopId)
	}

	if s.PackDesignID != 0 {
		db = db.Where("pack_design_id != ?", s.PackDesignID)
	}

	if s.TagDesignID != 0 {
		db = db.Where("tag_design_id != ?", s.TagDesignID)
	}

	err = db.Find(&list).Error

	return
}

func (s *ShopProduct) GetList() (list []*ShopProduct, err error) {

	db := mysql.NewConn().Model(s)

	if s.MasterID > 0 {
		db = db.Where("master_id = ?", s.MasterID)
	}

	err = db.Find(&list).Error

	return
}

func (s *ShopProduct) GetListByShopIdList(shopIdList []uint) (list []*ShopProduct, err error) {

	db := mysql.NewConn().Model(s)

	db = db.Where("shop_id IN (?)", shopIdList)

	if s.ProductCollectionID > 0 {
		db = db.Where("product_collection_id >0 ")
	}

	err = db.Find(&list).Error

	return
}

// 删除商品模板
func (s *ShopProduct) Delete(tx ...*gorm2.DB) error {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(s).Select("Variants", "Images", "Options").Where("id = ?", s.ID).Delete(s).Error
}

// 软删除
func (s *ShopProduct) SoftDelete() error {
	return mysql.NewConn().Model(&ShopProduct{}).Where("id = ? OR master_id = ?", s.ID, s.ID).Where("user_id = ?", s.UserID).
		Update("deleted", 1).Update("updated_at", time.Now()).Preload("SlaveShopProducts").
		First(s).Error
}

func (c *ShopProduct) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ShopProduct, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *ShopProduct) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ShopProduct{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ShopProduct) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ShopProduct) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ShopProduct)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		if detailObj.PublishedAt != nil && detailObj.PublishedAt.Unix() < 1 {
			detailObj.PublishedAt = nil
		}
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		if detailObj.PublishedAt != nil && detailObj.PublishedAt.Unix() < 1 {
			detailObj.PublishedAt = nil
		}
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ShopProduct) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// GetByDesignID 根据 DesignID 和 shop_id 查找未删除的商品 count
func (c *ShopProduct) GetByDesignID() (count int, err error) {
	db := mysql.NewConn().Table(c.TableName()).Where("design_id = ?", c.DesignID).Where("shop_id = ?", c.ShopId).Where("deleted != ?", 1)
	err = db.Count(&count).Error
	return
}

// 根据map 更新设计模板
func (c *ShopProduct) UpdateMap(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if c.Versions != 0 {
		db = db.Where("versions = ?", c.Versions)
	}
	err = db.Model(&ShopProduct{}).Where("id = ?", c.ID).Updates(data).Error
	return
}

// 根据map 更新
func (c *ShopProduct) UpdateMapByDesignID(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if c.Versions != 0 {
		db = db.Where("versions = ?", c.Versions)
	}
	err = db.Model(&ShopProduct{}).Where("design_id = ?", c.DesignID).Updates(data).Error
	return
}

func (s *ShopProduct) GetFirstByInfo() (err error) {

	db := mysql.NewConn()
	db = db.Model(s)

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}
	if s.DesignID > 0 {
		db = db.Where("design_iD = ?", s.DesignID)
	}
	if s.Versions > 0 {
		db = db.Where("versions = ?", s.Versions)
	}

	if len(s.ThirdProductIDStr) > 0 {
		db = db.Where("third_product_id_str = ?", s.ThirdProductIDStr)
	}
	err = db.First(s).Error
	return
}

// QueryByIDAndVersion  根据id、versions 获取 digitization_status 字段
func (s *ShopProduct) QueryByIDAndVersion() (err error) {
	db := mysql.NewConn().Model(s).Where("id = ?", s.ID).Where("versions = ?", s.Versions)
	err = db.First(s).Error
	return
}

// 根据 DesignID 和 Versions 查找未删除的商店商品数量
func (c *ShopProduct) GetByDesignIDAndVersions() (count int, err error) {
	db := mysql.NewConn().Table(c.TableName()).Where("design_id = ?", c.DesignID).Where("versions = ?", c.Versions).Where("deleted != ?", 1)
	err = db.Count(&count).Error
	return
}

type shopIDList struct {
	ID uint `json:"id"`
}

// 根据DesignID 和 Versions 查找ID List
func (c *ShopProduct) GetByDesignIDAndVersionsList() (ids []shopIDList, err error) {
	db := mysql.NewConn().Table(c.TableName()).Where("design_id = ?", c.DesignID).Where("versions = ?", c.Versions).Where("deleted != ?", 1)
	err = db.Select("id").Find(&ids).Error
	return
}
