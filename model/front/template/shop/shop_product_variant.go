package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ShopProductVariant)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ShopProductVariant)(nil))
}

// type ShopifyVariant goshopify.Variant
type ShopProductVariant struct {
	ID               uint   `json:"id,omitempty"`
	ShopifyVariantID int64  `json:"shopify_variant_id"`
	ProductID        int64  `json:"product_id"`                // pod平台商店商品id
	ShopProductID    int64  `json:"shop_product_id,omitempty"` //第三方平台商品
	ShopifyIdStr     string `json:"shopify_id_str,omitempty"`  // shopify 商店商品 id

	UserID               uint       `json:"user_id"`
	ShopId               uint       `json:"shop_id"`
	Title                string     `json:"title,omitempty"`
	Sku                  string     `json:"sku,omitempty"` //etsy 也存
	Position             int        `json:"position,omitempty"`
	Grams                int        `json:"grams,omitempty"`
	InventoryPolicy      string     `json:"inventory_policy,omitempty"`
	Price                int64      `json:"price,omitempty"` //价格 美分  etsy也存
	CompareAtPrice       float64    `json:"compare_at_price,omitempty"`
	FulfillmentService   string     `json:"fulfillment_service,omitempty"`
	InventoryManagement  string     `json:"inventory_management,omitempty"`
	InventoryItemId      int64      `json:"inventory_item_id,omitempty"`
	Option1              string     `json:"option1,omitempty"`
	Option2              string     `json:"option2,omitempty"`
	Option3              string     `json:"option3,omitempty"`
	CreatedAt            *time.Time `json:"created_at,omitempty"`
	UpdatedAt            *time.Time `json:"updated_at,omitempty"`
	Taxable              bool       `json:"taxable,omitempty"`
	TaxCode              string     `json:"tax_code,omitempty"`
	Barcode              string     `json:"barcode,omitempty"`
	ImageID              int64      `json:"image_id,omitempty"`
	InventoryQuantity    int        `json:"inventory_quantity,omitempty"`
	Weight               float64    `json:"weight,omitempty"`
	WeightUnit           string     `json:"weight_unit,omitempty"`
	OldInventoryQuantity int        `json:"old_inventory_quantity,omitempty"`
	RequireShipping      bool       `json:"requires_shipping,omitempty"`
	AdminGraphqlAPIID    string     `json:"admin_graphql_api_id,omitempty"`
	//Metafields           []Metafield      `json:"metafields,omitempty"`
	//新增etsy变体相关信息
	ListingID       int64  `json:"listing_id"`                          //etsy listing ID (商品id)
	EtysProductID   int64  `json:"etys_product_id"`                     //etsy变体id
	PropertyValues  string `json:"property_values"  gorm:"type:BLOB;"`  //Etsy变体属性
	WixProductID    string `json:"wix_product_id"`                      // wix 商品 id
	WixVariantID    string `json:"wix_variant_id"`                      // wix 商品变体 id
	WixChoice       string `json:"wix_choice"`                          // wix 变体 choice 序列化
	WooProductID    int64  `json:"woo_product_id"`                      // woocommerce 商品 id
	WooVariantID    int64  `json:"woo_variant_id"`                      // woocommerce 变体 id
	WooVariantPrice string `json:"woo_variant_price"`                   // woocommerce 变体价格
	WooAttributes   string `json:"woo_variant_price" gorm:"type:BLOB;"` // woocommerce 变体价格
	WooPodColorID   string `json:"woo_pod_color_id"`                    // woocommerce 上的变体对应的 pod 平台的 color id，例如 Y01
	WooPodSizeID    string `json:"woo_pod_size_id"`                     // woocommerce 上的变体对应的 pod 平台的 size id，例如 M01

	SSProductID string `json:"ss_product_id"` // Squarespace 的商品 ID
	SSVariantID string `json:"ss_variant_id"` // Squarespace 的变体 ID
}

func (s *ShopProductVariant) TableName() string {
	return "shop_product_variant"
}

// 新增变体
func (s *ShopProductVariant) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 更新变体
func (s *ShopProductVariant) Update(tx ...*gorm.DB) error {
	return mysql.NewConn().Model(&ShopProductVariant{}).Where("id = ?", s.ID).Update(s).Error
}

// 根据 更新变体
func (s *ShopProductVariant) UpdateByVariantID(tx ...*gorm.DB) error {
	return mysql.NewConn().Model(&ShopProductVariant{}).Where("shopify_variant_id = ?", s.ShopifyVariantID).Update(s).Error
}

// 根据商店商品id删除旧数据
func (s *ShopProductVariant) DeleteImagesByShopProductId(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if s.ShopProductID < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("shop_product_id = ?", s.ShopProductID).Delete(s).Error
}

// 删除 etsy listing 关联的 Variant
func (s *ShopProductVariant) DeleteEtsyVariantByListingID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if s.ListingID < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("listing_id = ?", s.ListingID).Delete(s).Error
}

// 删除 etsy listing 关联的 Variant
func (s *ShopProductVariant) DeleteByWixID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if len(s.WixProductID) < 1 {
		return nil
	}
	return db.Table(s.TableName()).Where("wix_product_id = ?", s.WixProductID).Delete(s).Error
}

func (s *ShopProductVariant) ExistByShopProductID() (res bool, err error) {
	count := 0
	if s.ShopProductID < 1 {
		return false, nil
	}
	err = mysql.NewConn().Table(s.TableName()).Where("shop_product_id = ?", s.ShopProductID).Count(&count).Error
	res = count != 0
	return
}

func (s *ShopProductVariant) GetSKU() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("shopify_variant_id = ?", s.ShopifyVariantID).First(s).Error
}

func (s *ShopProductVariant) GetWixSku() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("wix_variant_id = ?", s.WixVariantID).First(s).Error
}

func (s *ShopProductVariant) GetWooVariants() (list []*ShopProductVariant, err error) {
	if s.WooProductID < 1 {
		return
	}
	err = mysql.NewConn().Table(s.TableName()).Where("woo_product_id = ?", s.WooProductID).Find(&list).Error
	return
}

func (s *ShopProductVariant) GetByProductID() (list []*ShopProductVariant, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("product_id = ?", s.ProductID).Find(&list).Error
	return
}

func (s *ShopProductVariant) GetSKUByEtsy() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("etys_product_id = ?", s.EtysProductID).First(s).Error
}

func (s *ShopProductVariant) GetBySku() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("sku = ?", s.Sku).First(s).Error
}

func (s *ShopProductVariant) GetWooSku() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("sku = ?", s.Sku).Where("shop_id = ?", s.ShopId).Where("user_id = ?", s.UserID).First(s).Error
}

func (s *ShopProductVariant) GetSKUByEtsySKU() (err error) {
	return mysql.NewConn().Table(s.TableName()).Where("sku = ?", s.Sku).First(s).Error
}

func (s *ShopProductVariant) GetList(timeS, timeE int64, spu string) (list []*ShopProductVariant, count int, err error) {

	db := mysql.NewConn().Table(s.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if len(spu) > 0 {
		db = db.Where("sku LIKE ?", "%"+spu+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	//err = db.Offset((pn - 1) * ps).Limit(ps).Order("created_at desc").Find(&list).Error
	err = db.Order("created_at desc").Find(&list).Error
	return
}

func (s *ShopProductVariant) GetListByColorAndSSPid() (list []*ShopProductVariant, err error) {

	db := mysql.NewConn().Table(s.TableName())
	if s.SSProductID != "" {
		db = db.Where("ss_product_id = ?", s.SSProductID)
	}
	if s.Option1 != "" {
		db = db.Where("option1 = ?", s.Option1)
	}

	err = db.Find(&list).Error
	return
}

func (c *ShopProductVariant) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ShopProductVariant, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *ShopProductVariant) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ShopProductVariant{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ShopProductVariant) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ShopProductVariant) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ShopProductVariant)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ShopProductVariant) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
