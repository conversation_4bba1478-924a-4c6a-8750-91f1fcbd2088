package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/model/design"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*PackDesign)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*PackDesign)(nil))
}

type PackDesign struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	UserID       uint      `json:"user_id"`                  // 所属用户
	State        int       `json:"state" gorm:"default:1"`   // 状态，默认 1：草稿  2：正常
	PackagingID  uint      `json:"packaging_id"`             // 款式 主键ID
	PackID       string    `json:"pack_id"`                  // 款式 标识ID
	TemplateName string    `json:"template_name"`            // 模板名称
	SingleSided  bool      `json:"single_sided"`             // 是否单面  默认false 双面、true 单面
	Version      int       `json:"version" gorm:"default:1"` // 当前设计模板的版本

	ModelImg    string `json:"model_img"   gorm:"type:BLOB;"` // 模特展示图信息
	Design      string `json:"design"      gorm:"type:BLOB;"` // 设计信息
	Name        string `json:"name"`                          // 款式名称
	Description string `json:"description" gorm:"type:BLOB;"` // 整体描述
	Price       int    `json:"price"`                         // 包装款式价格  美分
	ExtraPrice  int    `json:"extra_price"`                   // 额外的价格 美分
	TotalPrice  int    `json:"total_price"`                   // 使用包装需要的总价格 美分

	ShopBind  string `json:"shop_bind"`  // 同步记录  商店id|分割
	SyncCount int    `json:"sync_count"` // 绑定产品数

	PackagingInfo *design.PackagingInfo `json:"packaging_info"    gorm:"foreignkey:packaging_id;association_foreignkey:id"` // 自定义包装袋款式信息
}

func (t *PackDesign) TableName() string {
	return "pack_design"
}

// 新建一个
func (t *PackDesign) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(t.TableName()).Create(t).Error
}

func (t *PackDesign) Query() (err error) {
	return mysql.NewConn().Table(t.TableName()).Where("id = ?", t.ID).First(t).Error
}

func (t *PackDesign) GetListByUserID() (list []*PackDesign, err error) {

	db := mysql.NewConn().Table(t.TableName()).Where("user_id = ?", t.UserID)

	err = db.Order("id desc").Find(&list).Error

	return
}

// 联表查询，
func (t *PackDesign) GetCName(packDesignIDArr []uint) (list []*CNameInfo, err error) {

	db := mysql.NewConn().Table(t.TableName())

	db = db.Select("pack_design.id,packaging_info.cname")
	db = db.Where("pack_design.id IN (?)", packDesignIDArr)
	db = db.Joins("LEFT JOIN packaging_info ON pack_design.packaging_id=packaging_info.id")

	err = db.Find(&list).Error

	return
}

func (t *PackDesign) UpdatesByID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&PackDesign{}).Where("id = ?", t.ID).Updates(t).Update("extra_price", t.ExtraPrice).First(t).Error
}

func (t *PackDesign) CountByUID() (count int, err error) {
	err = mysql.NewConn().Table(t.TableName()).Where("user_id = ?", t.UserID).Count(&count).Error
	return
}

func (t *PackDesign) DeleteByID() error {
	return mysql.NewConn().Table(t.TableName()).Where("id = ?", t.ID).Delete(t).Error
}

// 更新绑定数量 state 1增加 2减少
func (t *PackDesign) UpdateSyncCount(count, state int, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&PackDesign{}).Where("id = ?", t.ID)

	if t.UserID != 0 {
		db = db.Where("user_id = ?", t.UserID)
	}

	if state == 1 {
		err = db.UpdateColumn("sync_count", gorm.Expr("sync_count + ?", count)).Error
		return err
	}
	if state == 2 {
		err = db.UpdateColumn("sync_count", gorm.Expr("sync_count - ?", count)).Error
		return err
	}

	return nil
}

func (t *PackDesign) UpdateShopBind(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&PackDesign{}).Where("id = ?", t.ID)
	return db.Update("shop_bind", t.ShopBind).Error
}

func (c *PackDesign) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*PackDesign, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *PackDesign) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&PackDesign{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *PackDesign) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *PackDesign) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(PackDesign)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *PackDesign) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (p *PackDesign) GetByIds(ids []int64) (list []*PackDesign, err error) {
	db := mysql.NewConn()

	db = db.Table(p.TableName()).Where("id IN (?)", ids)

	db = db.Preload("PackagingInfo")

	err = db.Find(&list).Error

	return
}

func (p *PackDesign) UpdateByMap(updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&PackDesign{})
	db = db.Where("id = ?", p.ID)

	err = db.Updates(updateMap).Error

	return
}
