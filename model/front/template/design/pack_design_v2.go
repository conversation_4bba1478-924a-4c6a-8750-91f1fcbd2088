package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*PackDesignV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// PackDesignV2 包装设计表 - GORM v2版本
type PackDesignV2 struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	UserID       uint      `json:"user_id"`                  // 所属用户
	State        int       `json:"state" gorm:"default:1"`   // 状态，默认 1：草稿 2：正常
	PackagingID  uint      `json:"packaging_id"`             // 款式主键ID
	PackID       string    `json:"pack_id"`                  // 款式标识ID
	TemplateName string    `json:"template_name"`            // 模板名称
	SingleSided  bool      `json:"single_sided"`             // 是否单面
	Version      int       `json:"version" gorm:"default:1"` // 当前设计模板的版本

	ModelImg    string `json:"model_img" gorm:"type:BLOB"`   // 模特展示图信息
	Design      string `json:"design" gorm:"type:BLOB"`      // 设计信息
	Name        string `json:"name"`                         // 款式名称
	Description string `json:"description" gorm:"type:BLOB"` // 整体描述
	Price       int    `json:"price"`                        // 包装款式价格 美分
	ExtraPrice  int    `json:"extra_price"`                  // 额外的价格 美分
	TotalPrice  int    `json:"total_price"`                  // 使用包装需要的总价格 美分

	ShopBind  string `json:"shop_bind"`  // 同步记录 商店id|分割
	SyncCount int    `json:"sync_count"` // 绑定产品数
}

func (p *PackDesignV2) TableName() string {
	return "pack_design"
}

// 统一数据库接口方法
func (p *PackDesignV2) Create(db mysql.DBInterface) error {
	return db.Create(p).Error()
}

func (p *PackDesignV2) Query(db mysql.DBInterface) error {
	return db.Where(p).First(p).Error()
}

func (p *PackDesignV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*PackDesignV2, int64, error) {
	var list []*PackDesignV2
	var count int64

	query := db.Model(&PackDesignV2{})
	for key, value := range conditions {
		if key == "template_name_like" {
			query = query.Where("template_name LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		} else if key == "name_like" {
			query = query.Where("name LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		} else {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Order("id desc").Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (p *PackDesignV2) Update(db mysql.DBInterface) error {
	return db.Save(p).Error()
}

func (p *PackDesignV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(p).Error()
}

func (p *PackDesignV2) Delete(db mysql.DBInterface) error {
	return db.Delete(p).Error()
}

// 根据用户ID查询
func (p *PackDesignV2) GetListByUserID(db mysql.DBInterface, userID uint) ([]*PackDesignV2, error) {
	var list []*PackDesignV2
	err := db.Where("user_id = ?", userID).Order("id desc").Find(&list).Error
	return list, err()
}

// 根据状态查询
func (p *PackDesignV2) GetListByState(db mysql.DBInterface, state int) ([]*PackDesignV2, error) {
	var list []*PackDesignV2
	err := db.Where("state = ?", state).Find(&list).Error
	return list, err()
}

// 更新绑定数量
func (p *PackDesignV2) UpdateSyncCount(db mysql.DBInterface, increment int) error {
	return db.Model(p).Update("sync_count", gorm.Expr("sync_count + ?", increment)).Error()
}

// 更新商店绑定信息
func (p *PackDesignV2) UpdateShopBind(db mysql.DBInterface, shopBind string) error {
	return db.Model(p).Update("shop_bind", shopBind).Error()
}

// 创建或更新 - 工厂同步
func (p *PackDesignV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	detailObj := new(PackDesignV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingRecord PackDesignV2
	err = db.Where("id = ?", detailObj.ID).First(&existingRecord).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建PackDesign失败: ", err)
			return err()
		}
		log.Info("创建PackDesign成功: ", detailObj.ID)
	} else {
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新PackDesign失败: ", err)
			return err()
		}
		log.Info("更新PackDesign成功: ", detailObj.ID)
	}

	return nil
}
