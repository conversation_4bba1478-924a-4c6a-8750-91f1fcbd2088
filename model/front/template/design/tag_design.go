package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/model/design"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*TagDesign)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*TagDesign)(nil))
}

// 用户设计的吊牌表
type TagDesign struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	UserID       uint      `json:"user_id"`                  // 所属用户
	State        int       `json:"state" gorm:"default:1"`   // 状态，默认 1：草稿  2：正常
	HangTagID    uint      `json:"hang_tag_id"`              // 款式 主键ID，款式对应的数据库主键 ID
	TagID        string    `json:"tag_id"`                   // 款式 标识ID，款式的标识 ID，示例：S001
	TemplateName string    `json:"template_name"`            // 模板名称
	Version      int       `json:"version" gorm:"default:1"` // 当前设计模板的版本

	ModelImg    string `json:"model_img"   gorm:"type:BLOB;"` // 模特展示图信息
	Design      string `json:"design"      gorm:"type:BLOB;"` // 设计信息
	Name        string `json:"name"`                          // 款式名称
	Description string `json:"description" gorm:"type:BLOB;"` // 整体描述
	Price       int    `json:"price"`                         // 吊牌款式价格  美分
	ExtraPrice  int    `json:"extra_price"`                   // 额外的价格 美分
	TotalPrice  int    `json:"total_price"`                   // 使用吊牌需要的总价格 美分

	ShopBind  string `json:"shop_bind"`  // 同步记录  商店id|分割
	SyncCount int    `json:"sync_count"` // 绑定产品数

	HangTagInfo *design.HangTagInfo `json:"hang_tag_info"    gorm:"foreignkey:hang_tag_id;association_foreignkey:id"` // 自定义吊牌款式信息
}

func (t *TagDesign) TableName() string {
	return "tag_design"
}

// 新建一个
func (t *TagDesign) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(t.TableName()).Create(t).Error
}

type CNameInfo struct {
	ID    uint   `json:"id"`
	Cname string `json:"cname"`
}

// 联表查询
func (t *TagDesign) GetCName(tagDesignIDArr []uint) (list []*CNameInfo, err error) {

	db := mysql.NewConn().Table(t.TableName())

	db = db.Select("tag_design.id,hangtag_info.cname")
	db = db.Where("tag_design.id IN (?)", tagDesignIDArr)
	db = db.Joins("LEFT JOIN hangtag_info ON tag_design.hang_tag_id=hangtag_info.id")

	err = db.Find(&list).Error

	return
}

func (t *TagDesign) Query() (err error) {
	return mysql.NewConn().Table(t.TableName()).Where("id = ?", t.ID).First(t).Error
}

func (t *TagDesign) GetListByUserID() (list []*TagDesign, err error) {

	db := mysql.NewConn().Table(t.TableName()).Where("user_id = ?", t.UserID)

	err = db.Order("id desc").Find(&list).Error

	return
}

func (t *TagDesign) UpdatesByID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&TagDesign{}).Where("id = ?", t.ID).Updates(t).Update("extra_price", t.ExtraPrice).First(t).Error
}

func (t *TagDesign) CountByUID() (count int, err error) {
	err = mysql.NewConn().Table(t.TableName()).Where("user_id = ?", t.UserID).Count(&count).Error
	return
}

func (t *TagDesign) DeleteByID() error {
	return mysql.NewConn().Table(t.TableName()).Where("id = ?", t.ID).Delete(t).Error
}

// 更新绑定数量 state 1增加 2减少
func (t *TagDesign) UpdateSyncCount(count, state int, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&TagDesign{}).Where("id = ?", t.ID)

	if t.UserID != 0 {
		db = db.Where("user_id = ?", t.UserID)
	}

	if state == 1 {
		err = db.UpdateColumn("sync_count", gorm.Expr("sync_count + ?", count)).Error
		return err
	}
	if state == 2 {
		err = db.UpdateColumn("sync_count", gorm.Expr("sync_count - ?", count)).Error
		return err
	}

	return nil
}

func (t *TagDesign) UpdateShopBind(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&TagDesign{}).Where("id = ?", t.ID)
	return db.Update("shop_bind", t.ShopBind).Error
}

func (c *TagDesign) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*TagDesign, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *TagDesign) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&TagDesign{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *TagDesign) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *TagDesign) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(TagDesign)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *TagDesign) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (t *TagDesign) GetByIds(ids []int64) (list []*TagDesign, err error) {
	db := mysql.NewConn()

	db = db.Table(t.TableName()).Where("id IN (?)", ids)

	db = db.Preload("HangTagInfo")

	err = db.Find(&list).Error

	return
}

func (t *TagDesign) UpdateByMap(updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&TagDesign{})
	db = db.Where("id = ?", t.ID)

	err = db.Updates(updateMap).Error

	return
}

func (t *TagDesign) QueryByIds(ids []int64) (list []*TagDesign, err error) {
	db := mysql.NewConn()
	db = db.Table(t.TableName()).Where("id IN (?)", ids)
	err = db.Find(&list).Error
	return
}
