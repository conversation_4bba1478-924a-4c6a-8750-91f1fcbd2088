package template

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"strconv"
	"strings"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Design)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Design)(nil))
}

const (
	ModelTagHanging = "hanging"  // 挂牌图
	ModelTagModel   = "model"    // 上身图
	ModelTagFlatLay = "flat lay" // 平铺图
)

type Design struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Deleted   int       `json:"deleted"       gorm:"default:0"`
	Versions  int       `json:"versions" gorm:"default:1"` // 版本号  1 2 3 4累加
	SkuTag    string    `json:"sku_tag"`                   // sku标签 ABCDEFG,手动标记

	ProductID    uint   `json:"product_id"    gorm:"not null"` //productID 商品id
	SPU          string `json:"spu"`                           //平台spu
	UserID       uint   `json:"user_id"`                       //用户id
	TemplateName string `json:"template_name"`                 //模板名称
	Title        string `json:"title"`                         //商品名称
	ColorIdList  string `json:"color_id_list"`                 //颜色id 使用|分割
	SizeIdList   string `json:"size_id_list"`                  //尺码id 使用|分割
	Description  string `json:"description"`                   //描述
	ExtraPrice   int    `json:"extra_price"`                   //额外的价格

	Design       string `json:"design"  gorm:"type:BLOB;"`        //设计信息
	BrandLabelID uint   `json:"brand_label_id"  gorm:"default:0"` //品牌领标ID  为0是未使用
	CustomCount  int    `json:"custom_count"  gorm:"default:0"`   //半自定义图层数量 0代表没有  范围 0-3
	EmbCount     int    `json:"emb_count" gorm:"default:0"`       // 刺绣框数量  0代表没有

	ModelImg     string `json:"model_img"  gorm:"type:BLOB;"` //模特展示图信息
	SyncRecord   string `json:"sync_record"`                  //同步记录  商店id|分割
	MaterialPath string `json:"material_path"`                //素材地址  暂时弃用
	ShowColorNum string `json:"show_color_num"`               // 最有一次选中的颜色色号
	SizeImgUrl   string `json:"size_img_url"`                 // 尺码表图片 url
	//新增吊牌、包装绑定, 只记录 ID ，通过 Preload 获取数据
	TagDesignID    uint        `json:"tag_design_id" gorm:"default:0"`                                               // 与模板绑定的 吊牌设计ID
	TagDesignInfo  *TagDesign  `json:"tag_design_info"  gorm:"foreignkey:tag_design_id;association_foreignkey:id"`   // 吊牌信息
	PackDesignID   uint        `json:"pack_design_id"  gorm:"default:0"`                                             // 包装设计ID
	PackDesignInfo *PackDesign `json:"pack_design_info"  gorm:"foreignkey:pack_design_id;association_foreignkey:id"` // 吊牌信息

	IsBlank    bool `json:"is_blank"`    // 是否空白模板
	MinusPrice int  `json:"minus_price"` // 空白模板折扣价

	LightColorSet string `json:"light_color_set"  gorm:"type:BLOB;"` // 自定义打印设置浅色
	DarkColorSet  string `json:"dark_color_set"   gorm:"type:BLOB;"` // 自定义打印设置深色

	ModelTag    string               `json:"model_tag"  gorm:"size:64"` // 模型标签
	ProductSize []*model.ProductSize `json:"product_size" gorm:"foreignkey:product_id;association_foreignkey:product_id"`

	AuditState int `json:"audit_state"` // 审核状态 1 待审核  2 审核通过  3 审核不通过

	DigitizationList   string `json:"digitization_list"  gorm:"type:BLOB;"` // 制版任务信息
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态   0 无需制版 1 待制版 2 制版中 3 待确认 4 已完成
	DigitizationCount  int    `json:"digitization_count"  gorm:"default:0"` // 制版任务数量
	DigitizationPrice  int    `json:"digitization_price" gorm:"default:0"`  // 制版费用，如果不为0，说明在订单中付款了
	DigitizationIDList string `json:"digitization_id_list"`                 // 将制版任务ID 放在这里，用于快速筛选
}

func (s *Design) TableName() string {
	return "design"
}

// 新增商品模板
func (d *Design) Create(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(d.TableName()).Create(d).Error
}

// 更新设计模板
func (d *Design) Update(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(d)
	if d.ID > 0 {
		db = db.Where("id = ?", d.ID)
	}
	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}

	return db.Updates(d).First(d).Error
}

// 强制更新sync_record
func (d *Design) UpdateSyncRecord() error {
	db := mysql.NewConn().Model(&Design{})
	if d.ID > 0 {
		db = db.Where("id = ?", d.ID)
	}
	return db.Updates(map[string]interface{}{
		"sync_record": d.SyncRecord,
	}).First(d).Error
}

func (d *Design) UpdateBrandLabelID(brandLabelID uint, extraPrice int) error {
	db := mysql.NewConn().Model(&Design{})

	if d.ID > 0 {
		db = db.Where("id = ?", d.ID)
	}
	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}
	return db.Updates(map[string]interface{}{
		"brand_label_id": brandLabelID,
		"extra_price":    extraPrice,
		"custom_count":   d.CustomCount,
	}).First(d).Error
}

// 强制更新 tagDesignID 传0为解绑
func (d *Design) UpdateTagDesign(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&Design{})

	if d.ID > 0 {
		db = db.Where("id = ?", d.ID)
	}
	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}

	return db.Updates(map[string]interface{}{
		"tag_design_id": d.TagDesignID,
	}).First(d).Error
}

// 强制更新 pack_design_id 传0为解绑
func (d *Design) UpdatePackDesign(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&Design{})

	if d.ID > 0 {
		db = db.Where("id = ?", d.ID)
	}
	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}

	return db.Updates(map[string]interface{}{
		"pack_design_id": d.PackDesignID,
	}).First(d).Error
}

// 更新设计模板
func (d *Design) UpdateDesignSyncRecord(productId int64, tx ...*gorm2.DB) error {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(d)

	//先将已经同步的 productID 取出来
	err := db.Where("id = ?", d.ID).First(d).Error
	if err != nil {
		return err
	}
	//整理为slice
	syncRecord := make([]int64, 0)
	if len(d.SyncRecord) > 0 {
		syncRecord = Strings2Slice(d.SyncRecord)
	}
	//添加新的 product id，并转 string 更新
	syncRecord = append(syncRecord, productId)
	d.SyncRecord = fmt.Sprintf("%v", syncRecord)

	//return db.Table(s.TableName()).Where("product_id = ?", s.ProductID).Updates(s).Error
	return db.Model(d).Where("id = ?", d.ID).Updates(d).Error
}

// 查询一个设计模板信息
func (d *Design) Query(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Preload("ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})
	db = db.Preload("TagDesignInfo").Preload("PackDesignInfo")

	return db.Model(d).Where("id = ?", d.ID).First(d).Error
}

// 简单查询，不进行 preload
func (d *Design) QuerySimple(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(d).Where("id = ?", d.ID).Preload("TagDesignInfo").Preload("PackDesignInfo").First(d).Error
}

// 根据 spu 查询一个设计模板信息
func (d *Design) QueryBySpu(timeS, timeE int64, tx ...*gorm.DB) (list []*Design, err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(d)
	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if len(d.SPU) > 0 {
		db = db.Where("spu LIKE ?", "%"+d.SPU+"%")
	}

	err = db.Find(&list).Error
	return
}

func (d *Design) GetCountByUserId() (count int64, err error) {
	db := mysql.NewConn().Table(d.TableName())

	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}

	err = db.Count(&count).Error
	return
}

// 查询一个用户所有模板信息
func (d *Design) QueryList(page, size int, brand_state ...int) (list []*Design, count int64, err error) {
	db := mysql.NewConn().Table(d.TableName()).Where("deleted != ?", 1)

	if d.UserID > 0 {
		db = db.Where("user_id = ?", d.UserID)
	}

	if len(brand_state) > 0 {
		switch brand_state[0] {
		case 1:
			if d.PackDesignID != 0 {
				db = db.Where("pack_design_id = ?", d.PackDesignID)
			}
			if d.TagDesignID != 0 {
				db = db.Where("tag_design_id = ?", d.TagDesignID)
			}
		case 2:
			if d.PackDesignID != 0 {
				db = db.Where("pack_design_id != ?", d.PackDesignID)
			}
			if d.TagDesignID != 0 {
				db = db.Where("tag_design_id != ?", d.TagDesignID)
			}
		}
	}

	if d.TemplateName != "" {
		db = db.Where("template_name Like ? OR title like ?", "%"+d.TemplateName+"%", "%"+d.TemplateName+"%")
	}

	if d.SyncRecord != "" {
		db = db.Where("sync_record like ?", "%"+d.SyncRecord+"%")
	}

	if d.SPU != "" {
		db = db.Where("spu like ?", "%"+d.SPU+"%")
	}

	if d.DigitizationStatus != 0 {
		db = db.Where("digitization_status = ?", d.DigitizationStatus)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	//添加关联
	db = db.Preload("ProductSize", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})
	db = db.Preload("TagDesignInfo").Preload("PackDesignInfo")
	db = db.Order("id desc")
	err = db.Offset((page - 1) * size).Limit(size).Find(&list).Error

	return
}

// 删除设计模板
func (d *Design) Delete(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&Design{}).Where("id = ?", d.ID).Where("user_id = ?", d.UserID).Update("deleted", 1).Update("updated_at", time.Now()).First(d).Error
}

func Strings2Slice(s string) []int64 {

	splitArr := strings.Split(s[1:len(s)-1], " ")

	revesoleArr := make([]int64, 0)
	for _, v := range splitArr {
		value, _ := strconv.ParseInt(v, 10, 64)
		revesoleArr = append(revesoleArr, value)
	}

	return revesoleArr
}

// 获取用户的模板数量
func (d *Design) GetCountByUserID() (count int, err error) {
	db := mysql.NewConn().Table(d.TableName()).Where("user_id = ?", d.UserID)
	err = db.Count(&count).Error
	return
}

// 获取用户的模板数量
func (d *Design) GetCountByUserIDSubDeleted() (count int, err error) {
	db := mysql.NewConn().Table(d.TableName()).Where("user_id = ?", d.UserID).Where("deleted != ?", 1)
	err = db.Count(&count).Error
	return
}

// 查询一个设计模板信息
func (d *Design) QueryAll(tx ...*gorm.DB) (list []*Design, err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(d).Find(&list).Error
	return
}

func (c *Design) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Design, 0)

	err := db.Find(&list).Error

	return list, err
}

func (d *Design) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Design{}).Where("id IN (?)", ids).Delete(d).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Design) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Design) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Design)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Design) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// UpdateModelTag 更新一个用户下所有的模板的 ModelTag
func (c *Design) UpdateModelTag(modelTag string) (err error) {
	db := mysql.NewConn()
	err = db.Model(&Design{}).Where("user_id = ?", c.UserID).Update("model_tag", modelTag).Error
	return
}

// 根据ID 获取数据
func (c *Design) GetDetailByID() (err error) {
	db := mysql.NewConn()
	err = db.Model(&Design{}).Where("id = ?", c.ID).First(c).Error
	return
}

// 根据字段获取一条数据
func (c *Design) GetFirstByInfo() (err error) {
	db := mysql.NewConn()
	db = db.Model(&Design{})
	if c.ID != 0 {
		db = db.Where("id = ?", c.ID)
	}
	if c.Versions != 0 {
		db = db.Where("versions = ?", c.Versions)
	}
	if c.UserID != 0 {
		db = db.Where("user_id = ?", c.UserID)
	}
	err = db.First(c).Error
	return
}

// GetSpuListByUserID 根据userID 获取 spu List
func (c *Design) GetSpuListByUserID() (list []string, err error) {
	db := mysql.NewConn()
	err = db.Model(&Design{}).Where("user_id = ?", c.UserID).Pluck("spu", &list).Error
	return
}

// 根据map 更新设计模板
func (c *Design) UpdateMap(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if c.Versions != 0 {
		db = db.Where("versions = ?", c.Versions)
	}
	err = db.Model(&Design{}).Where("id = ?", c.ID).Updates(data).Error
	return
}

// QueryByIDAndVersion 根据id、versions 获取 digitization_status 字段
func (c *Design) QueryByIDAndVersion() (err error) {
	db := mysql.NewConn()
	err = db.Model(&Design{}).Where("id = ?", c.ID).Where("versions = ?", c.Versions).First(c).Error
	return
}

// 根据user_id and digitization_status = 3 查询数量
func (c *Design) GetCountByUserIDAndDigitizationStatus() (count int, err error) {
	db := mysql.NewConn()
	err = db.Model(&Design{}).Where("user_id = ?", c.UserID).Where("digitization_status = ?", 3).Count(&count).Error
	return
}
