package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/model/front/order"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*UserInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*UserInfo)(nil))
}

const GOOGLE_TYPE = 1
const FACEBOOK_TYPE = 2

const (
	FirstCreateTemplate = 1
	FirstOrderSample    = 1 << 1
	FirstConnectStore   = 1 << 2
)

const (
	DEFAULTRISKTATE = 0 //正常用户
	HIGHRISKSTATE   = 1 //高风险
	WHITERISKSTATE  = 2 //白名单
	BLACKRISKSTATE  = 3 //黑名单
)

const (
	DEFAULTRISKREASON      = 0 // 默认
	BLACKLISTIMGRISKREASON = 1 // 黑名单图片
	REPEATRISKREASON       = 2 // 重复图片
	IPRISKREASON           = 3 // ip跨城市（原意思为IP异常）
	MANUALMARKRISKREASON   = 4 // 人工标记
	IPREPEATRISKREASON     = 5 // IP地址相同
	IPUNABLERISKREASON     = 6 // IP无法获取
	ShortMultiplePayment   = 7 // 短时多次支付
	DifferentAddress       = 8 // 短期内支付多笔地址不同的样品订单
)

type UserInfo struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"` //注册时间
	UpdatedAt time.Time `json:"updated_at"` //更新时间

	Email        string `json:"email"           gorm:"unique;not null"`       //邮箱地址
	Account      string `json:"account"         gorm:"unique;not null;index"` //登录账户
	Password     string `json:"password"        gorm:"not null"`              //密码
	Salt         string `json:"salt"            gorm:"not null"`              //盐（随机生产）
	ContactEmail string `json:"contact_email"`                                //联系邮箱（默认注册邮箱）
	Oauth        *bool  `json:"oauth"`                                        //是否第三方授权
	OauthType    int    `json:"oauth_type"`                                   //第三方授权平台

	Company             string `json:"company"`                          //企业名称
	Name                string `json:"name"            gorm:""`          //全名（姓名）
	Phone               string `json:"phone"           gorm:""`          //联系电话（非必填）
	SizeType            int    `json:"size_type"       gorm:""`          //默认尺码格式, 英制 - 1, 公制 - 2
	PersonId            string `json:"person_id"`                        //绑定销售账号（默认为空）
	SalesTerritory      string `json:"sales_territory"`                  //默认销售地区 | 就是选择的币种
	NeedVedio           *bool  `json:"need_vedio"`                       //样衣视频（默认需要）
	AffiliatingTimeZone string `json:"affiliating_time_zone"`            //所属时区
	IsSub               *bool  `json:"is_sub"`                           //是否订阅平台更新内容（默认是） | 是否进行邮箱确认
	EmailSubscribe      uint32 `json:"email_subscribe" gorm:"default:0"` //邮件订阅选项

	Collect         string `json:"collect" gorm:"type:BLOB;"`           //商品收藏 string|string
	VipDiscount     int    `json:"vip_discount" gorm:"default:0"`       // vip折扣
	Level           string `json:"level"`                               // 用户等级，根据每个月销售额确定
	LevelLimit      int    `json:"level_limit" gorm:"default:5"`        // 用户等级限制 表示用户可以达到的最大等级
	ActionState     int32  `json:"action_state" gorm:"default:0"`       // 用户行为状态信息，目前记录用户是否进行样品订单下单等第一次操作的数据
	TwoMonthAgoSale int64  `json:"two_month_ago_sale" gorm:"default:0"` // N-2 月份的销售额
	OneMonthAgoSale int64  `json:"one_month_ago_sale" gorm:"default:0"` // N-1 月份的销售额
	CurMonthAgoSale int64  `json:"cur_month_ago_sale" gorm:"default:0"` // 当前月份的销售额

	Classify string `json:"classify"`                  //客户等级（手动划分） A B C
	Remark   string `json:"remark"  gorm:"type:BLOB;"` //备注

	Wallet         *Wallet            `json:"wallet" gorm:"foreignkey:user_id;association_foreignkey:id"`       //对应的钱包
	TradeRecord    *TradeRecord       `json:"trade_record" gorm:"foreignkey:user_id;association_foreignkey:id"` //对应的消费记录
	AssociatedShop []*AssociatedShop  `json:"associated_shop" gorm:"foreignkey:user_id;association_foreignkey:id"`
	OrderInfo      []*order.OrderInfo `json:"order_info" gorm:"foreignkey:user_id;association_foreignkey:id"`
	DesignCount    int                `json:"design_count"` // 设计数量统计
	OrderCount     int64              `json:"order_count"`  // 所有订单数量统计，样品订单，商店订单，同步订单

	InviteCode     string `json:"invite_code"`                  // 用户邀请码
	IdentifierCode string `json:"identifier_code"`              // 用户标识码  如果为空说明是自然注册
	PopupFlags     uint64 `json:"popup_flags" gorm:"default:0"` // 弹窗标记，bit位为1代表不再弹出，用于标识哪些弹框不再弹出（换了浏览器期依然有效）

	ModelTag string `json:"model_tag"  gorm:"type:BLOB;"` // 模型标签
	VatID    string `json:"vat_id"`                       // VAT税号

	RechargeRichTextId uint `json:"recharge_rich_text_id"`          // 充值富文本ID
	SpecialPopup       int  `json:"special_popup" gorm:"default:0"` // 特殊弹窗标识  =2 代表弹出特殊弹窗，0和1 不弹出

	RiskState  int    `json:"risk_state" gorm:"default:0"`   // 风险状态  0：正常，1：高风险，2：白名单，3：黑名单
	RiskReason int    `json:"risk_reason" gorm:"default:0"`  // 风险原因  0：正常，1：黑名单图片，2：重复图片，3：IP跨城市，4：人工标记，5：IP地址相同，6：IP无法获取
	RiskTime   int64  `json:"risk_time"  gorm:"default:0"`   // 触发RiskState 变更的时间戳
	RiskRemake string `json:"risk_remake" gorm:"type:BLOB;"` // 风控备注

	ShowWallet int `json:"show_wallet"` // 是否展示钱包功能 0，1不展示  2展示
	IsWallet   int `json:"is_wallet"`   // 是否可使用钱包功能 （审核是否通过）0，1 不通过 2通过
}

type UserResp struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"created_at"` //注册时间
	Email     string    `json:"email"`      //邮箱地址
	Account   string    `json:"account"`    //登录账户
	Name      string    `json:"name"`       //全名（姓名）
	PersonId  string    `json:"person_id"`  //绑定销售账号（默认为空）
	Level     string    `json:"level"`      // 客户等级，会员优惠的根据
	Classify  string    `json:"classify"`   //客户等级（手动划分） A B C
	Remark    string    `json:"remark"`     //备注
}

func (u *UserInfo) TableName() string {
	return "user_info"
}

// 用户支付一个订单成功后，就加1
func (u *UserInfo) AddOrderCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserInfo{}).Where("id = ?", u.ID).UpdateColumn("order_count", gorm.Expr("order_count + ?", 1)).Error
}

// 用户将已支付的订单退款后，就减1
func (u *UserInfo) SubOrderCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserInfo{}).Where("id = ?", u.ID).UpdateColumn("order_count", gorm.Expr("order_count - ?", 1)).Error
}

// 用户新增一个设计或复制一个设计成功后，就加1
func (u *UserInfo) AddDesignCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserInfo{}).Where("id = ?", u.ID).UpdateColumn("design_count", gorm.Expr("design_count + ?", 1)).Error
}

// 用户删除一个设计后，就减1
func (u *UserInfo) SubDesignCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserInfo{}).Where("id = ?", u.ID).UpdateColumn("design_count", gorm.Expr("design_count - ?", 1)).Error
}

// 新增
func (u *UserInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Table(u.TableName()).Create(u).Error
	if err != nil {
		return err
	}

	//discountDB := Discount{
	//	AdminName:     "system",
	//	DiscountValue: 70,
	//	Account:       u.Account,
	//	ValidDays:     3,
	//	Remark:        "New users are automatically assigned",
	//	UserID:        u.ID,
	//	ExpirationAt:  time.Now().AddDate(0, 0, 3).Unix(),
	//	IsNot:         0,
	//	RemindCount:   0,
	//}
	//err = discountDB.Create()
	//if err != nil {
	//	return err
	//}

	return nil
}

// 判断邮箱是否注册
func (u *UserInfo) IsExisted() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(u.TableName()).Where(u).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 1, nil
}

// 查询具体用户
func (u *UserInfo) GetInstance() error {
	db := mysql.NewConn().Model(&UserInfo{}).Where("account = ?", u.Account).First(u)
	return db.Error
}

func (u *UserInfo) GetInstanceByID() error {
	return mysql.NewConn().Table(u.TableName()).Where("id = ?", u.ID).First(u).Error
}

func (u *UserInfo) GetListByUserIds(userID []uint) (list []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("id IN (?)", userID).Find(&list).Error
	return
}

// 查询用户等级<2 的正常用户
func (u *UserInfo) GetNormalUserList(userID []uint) (list []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("id IN (?)", userID).Where("level = ?", "1").
		Where("risk_state = ?", 0).Find(&list).Error
	return
}

// 获取列表
func (u *UserInfo) GetList() (List []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Find(&List).Error
	if err != nil {
		return
	}
	return
}

// 获取列表
func (u *UserInfo) GetByAccount() (err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("account = ?", u.Account).Find(u).Error
	if err != nil {
		return
	}
	return
}

// 根据账户列表获取用户信息
func (u *UserInfo) GeByAccounts(accounts []string) (List []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("account IN (?)", accounts).Find(&List).Error
	if err != nil {
		return
	}
	return
}

// 查询
func (u *UserInfo) Query(pn, ps int, timeS, timeE int64, DesignCountMin, DesignCountMax int) (List []*UserInfo, count int, err error) {
	db := mysql.NewConn().Table(u.TableName())

	if u.ID != 0 {
		db = db.Where("id = ?", u.ID)
	}

	if u.Name != "" {
		db = db.Where("name like ?", "%"+u.Name+"%")
	}

	if u.Email != "" {
		db = db.Where("email like ?", "%"+u.Email+"%")
	}

	if u.Classify != "" {
		db = db.Where("classify = ?", u.Classify)
	}

	if u.Level != "" {
		db = db.Where("level = ?", u.Level)
	}

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if u.RiskState >= 0 {
		db = db.Where("risk_state = ?", u.RiskState)
	}

	// 设计数量，如果两个都为 0，则是查找所有
	if DesignCountMin > 0 || DesignCountMax > 0 {
		// 两个都填了
		if DesignCountMin > 0 && DesignCountMax > 0 {
			db = db.Where("design_count >= ?", DesignCountMin).Where("design_count <= ?", DesignCountMax)
		} else if DesignCountMin > 0 && DesignCountMax < 1 {
			db = db.Where("design_count >= ?", DesignCountMin)
		} else if DesignCountMin < 1 && DesignCountMax > 0 {
			db = db.Where("design_count <= ?", DesignCountMax)
		}
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	db = db.Preload("AssociatedShop").Preload("OrderInfo", "pay_code != ? AND order_state != ?", "", "12")

	err = db.Offset((pn - 1) * ps).Limit(ps).Order("created_at desc").Find(&List).Error

	return
}

// 获取列表
func (u *UserInfo) GetLevelRiskInfoById() (err error) {
	err = mysql.NewConn().Select("id,level,risk_state,risk_reason,risk_time,risk_remake").
		Where("id = ?", u.ID).
		Table(u.TableName()).First(u).Error
	if err != nil {
		return
	}
	return
}

// 根据ID查询
func (u *UserInfo) QueryByID() (err error) {
	return mysql.NewConn().Table(u.TableName()).Where("id = ?", u.ID).First(u).Error
}

type UserIdAndAccount struct {
	ID      uint   `gorm:"PRIMARY_KEY" json:"id"`
	Account string `json:"account"         gorm:"unique;not null;index"` //登录账户
}

func (u *UserInfo) QueryByUserIDs(userIDs []uint) (list []*UserIdAndAccount, err error) {
	db := mysql.NewConn().Table(u.TableName())
	err = db.Where("id IN (?)", userIDs).Find(&list).Error
	return
}

type IDAndEmail struct {
	ID      uint   `json:"id"`
	Account string `json:"account"` //登录账户
}

func (u *UserInfo) QueryEmailByIDs(ids []uint) (res map[uint]string, err error) {

	iDAndEmail := make([]*IDAndEmail, 0)
	err = mysql.NewConn().Table(u.TableName()).Where("id IN (?)", ids).Find(&iDAndEmail).Error

	if err == nil {
		res = make(map[uint]string)
		for _, one := range iDAndEmail {
			res[one.ID] = one.Account
		}
	}

	return
}

// 查询所有数据
func (u *UserInfo) QueryAll() (List []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Find(&List).Error
	return
}

type UserIDList struct {
	ID uint `gorm:"PRIMARY_KEY" json:"id"`
}

func (u *UserInfo) QueryByAccount() (List []*UserIDList, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("account like ?", "%"+u.Account+"%").Find(&List).Error
	return
}

func (u *UserInfo) QueryByAccounts(accounts []string) (List []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("account IN (?)", accounts).Find(&List).Error
	return
}

func (u *UserInfo) PutPass() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Update("password", u.Password).Error
}

func (u *UserInfo) PutCollect() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Update("collect", u.Collect).Error
}

func (u *UserInfo) PutPopupFlags() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Update("popup_flags", u.PopupFlags).Error
}

// PutModelTag 修改model_tag字段
func (u *UserInfo) PutModelTag() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Update("model_tag", u.ModelTag).Error
}

func (u *UserInfo) Update() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(u).Error
}

func (u *UserInfo) UpdateSales() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(map[string]interface{}{
		"two_month_ago_sale": u.TwoMonthAgoSale,
		"one_month_ago_sale": u.OneMonthAgoSale,
		"cur_month_ago_sale": u.CurMonthAgoSale,
		"level":              u.Level,
	}).Error
}

func (u *UserInfo) UpdateOrderAndDesignCount(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserInfo{}).Where("id = ?", u.ID).Updates(map[string]interface{}{
		"design_count": u.DesignCount,
		"order_count":  u.OrderCount,
	}).Error
}

func (u *UserInfo) UpdateSalesAndActionState() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(map[string]interface{}{
		"two_month_ago_sale": u.TwoMonthAgoSale,
		"one_month_ago_sale": u.OneMonthAgoSale,
		"cur_month_ago_sale": u.CurMonthAgoSale,
		"level":              u.Level,
		"action_state":       u.ActionState,
	}).Error
}

func (u *UserInfo) AddCurSale(saleCount int, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&UserInfo{}).Where("id = ?", u.ID).
		UpdateColumn("cur_month_ago_sale", gorm.Expr("cur_month_ago_sale + ?", saleCount)).
		First(u).Error
}

func (u *UserInfo) SubCurSale(saleCount int, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Table(u.TableName()).Where("id = ?", u.ID).
		UpdateColumn("cur_month_ago_sale", gorm.Expr("cur_month_ago_sale - ?", saleCount)).
		First(u).Error
}

func (u *UserInfo) PutEmailSubscribe() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(
		map[string]interface{}{
			"email_subscribe": u.EmailSubscribe,
		}).Error
}

func (u *UserInfo) PutEmailIsSub() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(
		map[string]interface{}{
			"is_sub": u.IsSub,
		}).Error
}

// 更新标识符
func (u *UserInfo) PutIdentifierCode() error {
	return mysql.NewConn().Model(&UserInfo{}).Where("id = ?", u.ID).Updates(
		map[string]interface{}{
			"identifier_code": u.IdentifierCode,
		}).Error
}

func (u *UserInfo) GetDistribution() (list []*UserInfo, err error) {
	err = mysql.NewConn().Table(u.TableName()).Where("identifier_code = ?", u.InviteCode).
		Preload("OrderInfo", "pay_code != ? AND order_state != ?", "", "12").
		Find(&list).Error

	return
}

func (c *UserInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*UserInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (u *UserInfo) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&UserInfo{}).Where("id IN (?)", ids).Delete(u).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *UserInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *UserInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(UserInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *UserInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 根据map interface更新用户信息
func (c *UserInfo) UpdateByMapData(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&UserInfo{})
	err = db.Where("id = ?", c.ID).Updates(data).First(c).Error
	return
}

type RiskUserInfo struct {
	ID                uint      `json:"id"`
	CreatedAt         time.Time `json:"created_at"`      //注册时间
	CreatedAtUinx     int64     `json:"created_at_uinx"` //注册时间
	Account           string    `json:"account" sensitive:"true"`
	RiskState         int       `json:"risk_state" gorm:"default:0"`   // 风险状态  0：正常，1：高风险，2：白名单，3：黑名单
	RiskReason        int       `json:"risk_reason" gorm:"default:0"`  // 风险原因  0：正常，1：黑名单图片，2：重复图片，3：IP异常，4：人工标记
	RiskTime          int64     `json:"risk_time"  gorm:"default:0"`   // 触发RiskState 变更的时间戳
	RiskRemake        string    `json:"risk_remake" gorm:"type:BLOB;"` // 风控备注
	OrderCount        int64     `json:"order_count"`                   // 所有订单数量统计，样品订单，商店订单，同步订单
	AllOrderCount     int       `json:"all_order_count"`               // 已支付订单总数
	SuspendOrderCount int       `json:"suspend_order_count"`           // 暂停订单数量
}

// 高风险列表查询
func (u *UserInfo) QueryHighRiskList(pn, ps int, isSuspend, isRemark int) (List []*RiskUserInfo, count int, err error) {
	// isSuspend 有无暂停 1 有 2无  0 全部
	// 连表查询 根据isSuspend条件查询有暂停订单、无暂停订单的用户列表
	// SuspendOrderCount 赋值

	var tempResults []struct {
		ID uint `json:"id"`
	}

	var userIds []uint
	db := mysql.NewConn().Table(u.TableName())

	// 首先，建立一个针对user_info表的初始查询
	initialQuery := db.Select("user_info.id")
	if u.RiskState >= 0 {
		initialQuery = initialQuery.Where("user_info.risk_state = ?", u.RiskState)
	}

	if u.RiskReason >= 0 {
		initialQuery = initialQuery.Where("user_info.risk_reason = ?", u.RiskReason)
	}
	if u.ID > 0 {
		initialQuery = initialQuery.Where("user_info.id = ?", u.ID)
	}

	if isRemark == 1 {
		initialQuery = initialQuery.Where("user_info.risk_remake != ?", "")
	} else if isRemark == 2 {
		initialQuery = initialQuery.Where("user_info.risk_remake = ? OR user_info.risk_remake is null", "")
	}

	if isSuspend == 1 {
		// 查询有暂停订单的用户ID
		initialQuery = initialQuery.Joins("JOIN suspend_order ON suspend_order.user_id = user_info.id AND suspend_order.resume_time <= ?", 10)
	} else if isSuspend == 2 {
		var suspendedUsers []struct {
			UserID uint `gorm:"column:user_id"`
		}
		db.Table("suspend_order").
			Select("user_id").
			Where("resume_time <= ?", 10).
			Group("user_id").
			Find(&suspendedUsers)
		var suspendedUserIds []uint
		for _, u := range suspendedUsers {
			suspendedUserIds = append(suspendedUserIds, u.UserID)
		}
		// 然后，从user_info中选择不在上述列表中的用户
		if len(suspendedUserIds) > 0 {
			initialQuery = initialQuery.Where("user_info.id NOT IN (?)", suspendedUserIds)
		}

		/*	// 查询没有暂停订单的用户ID
			initialQuery = initialQuery.Joins("LEFT JOIN suspend_order ON suspend_order.user_id = user_info.id")
			//合计暂停订单数量为0
			initialQuery = initialQuery.Having("sum(if(suspend_order.id is null or suspend_order.resume_time > 10,0,1)) = 0")*/
	}

	// 获取符合条件的用户ID和总数
	var counts struct {
		Count int `json:"count"`
	}
	initialQuery.Order("user_info.id ASC").Select("user_info.id").Group("user_info.id").Offset((pn - 1) * ps).Limit(ps).Find(&tempResults)
	initialQuery.Select("COUNT(DISTINCT user_info.id) as count").Scan(&counts)

	count = counts.Count

	// 提取用户ID
	for _, tempResult := range tempResults {
		userIds = append(userIds, tempResult.ID)
	}

	// 如果没有符合条件的用户，直接返回
	if len(userIds) == 0 {
		return []*RiskUserInfo{}, 0, nil
	}

	// 子查询用于统计每个用户已支付且符合特定状态的订单数量
	paidOrdersSubQuery := mysql.NewConn().Table("order_info").
		Select("user_id, COUNT(*) as all_order_count").
		//Where("pay_time > 0 AND state != ?", 12).
		Group("user_id").
		SubQuery()

	// 使用用户ID进行完整查询
	db = mysql.NewConn().Table("user_info").
		Joins("LEFT JOIN (SELECT user_id, COUNT(*) as suspend_order_count FROM suspend_order WHERE resume_time <= 10 GROUP BY user_id) as so ON so.user_id = user_info.id").
		Joins("LEFT JOIN ? as po ON po.user_id = user_info.id", paidOrdersSubQuery).
		Where("user_info.id IN (?)", userIds).
		Order("user_info.id ASC").
		Select("user_info.id, user_info.created_at, user_info.account, user_info.risk_state, user_info.risk_reason, user_info.risk_time, user_info.risk_remake,user_info.order_count, COALESCE(so.suspend_order_count, 0) as suspend_order_count,COALESCE(po.all_order_count, 0) as all_order_count").
		Find(&List)

	return
}

// 获取用户邮箱
type UserEmail struct {
	ID    uint   `json:"id"`
	Email string `json:"email"`
}

func (u *UserInfo) GetUserEmailByUserIds(userID []uint) (list []*UserEmail, err error) {
	db := mysql.NewConn().Table(u.TableName())
	db = db.Select("id,email").Where("id IN (?)", userID)
	if len(u.Email) > 0 {
		db = db.Where("email LIKE ?", "%"+u.Email+"%")
	}
	err = db.Find(&list).Error
	return
}

// QueryUserListByPaypal
func (u *UserInfo) QueryUserListByPaypal(idList []uint) (List []*UserEmail, err error) {
	db := mysql.NewConn().Table(u.TableName())
	db = db.Select("id,email").Where("id NOT IN (?)", idList)
	err = db.Scan(&List).Error
	return
}

// GetBlackUserById 获取是否有黑名单用户
func (u *UserInfo) GetBlackUserById(userID []uint) (user *UserInfo, err error) {
	user = &UserInfo{}
	err = mysql.NewConn().Table(u.TableName()).
		Select("id").
		Where("id IN (?) and risk_state = ?", userID, BLACKRISKSTATE).
		First(user).Error
	return
}
