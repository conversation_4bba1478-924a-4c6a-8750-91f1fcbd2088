package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*SampleAddress)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*SampleAddress)(nil))
}

type SampleAddress struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      uint      `json:"user_id"    gorm:"not null"` //用户id
	FirstName   string    `json:"first_name" gorm:"not null"` //名字
	LastName    string    `json:"last_name"  gorm:"not null"` //姓氏
	Phone       string    `json:"phone"      gorm:"not null"` //电话
	Email       string    `json:"email"      gorm:"not null"` //邮箱
	CountryCode string    `json:"country_code"`               //国家（国际二字码 标准ISO 3166-2）
	Country     string    `json:"country"    gorm:"not null"` //国家
	State       string    `json:"state"      gorm:"not null"` //州、省
	Address1    string    `json:"street"  gorm:"not null"`    //地址1
	Address2    string    `json:"district"`                   //地址2
	City        string    `json:"city"       gorm:"not null"` //城市
	PostCode    string    `json:"post_code"  gorm:"not null"` //邮编
	IsDefault   *bool     `json:"-"`                          //是否默认地址

	//	税务相关
	RFC  string `json:"rfc"`  // 巴西CPF税号  BR
	CURP string `json:"curp"` // 墨西哥 18+   MX
	CPF  string `json:"cpf"`  // 墨西哥 18-   MX
}

func (s *SampleAddress) TableName() string {
	return "sample_address"
}

// 新增一个样衣地址
func (s *SampleAddress) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

func (s *SampleAddress) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&SampleAddress{}).Where("id = ?", s.ID).Where("user_id = ?", s.UserID).Updates(map[string]interface{}{
		"first_name":   s.FirstName,
		"phone":        s.Phone,
		"email":        s.Email,
		"country":      s.Country,
		"country_code": s.CountryCode,
		"state":        s.State,
		"address1":     s.Address1,
		"address2":     s.Address2,
		"city":         s.City,
		"post_code":    s.PostCode,
		"rfc":          s.RFC,
		"curp":         s.CURP,
		"cpf":          s.CPF,
	}).Error
}

type SampleAddressList struct {
	SampleAddress
	Available bool   `json:"available"`
	ErrorInfo string `json:"error_info"`
}

// 查询用户所有的样衣收货地址
func (s *SampleAddress) FullList() (list []*SampleAddressList, err error) {
	err = mysql.NewConn().Model(&SampleAddress{}).Where("user_id = ?", s.UserID).Find(&list).Error
	return
}

// 更改默认地址
func (s *SampleAddress) PutIsDefault() error {
	//1. 根据userID 找到原先的默认地址，is_default改为false
	//2. 根据主键ID 更改此条的is_default为true
	return nil
}

func (s *SampleAddress) Delete() error {
	err := mysql.NewConn().Table(s.TableName()).Where("id = ?", s.ID).Where("user_id = ?", s.UserID).
		Delete(s).Error
	return err
}

func (s *SampleAddress) GetInfo() error {
	return mysql.NewConn().Table(s.TableName()).Where("id = ?", s.ID).First(s).Error
}

func (s *SampleAddress) GetFirstByInfo() error {
	db := mysql.NewConn().Table(s.TableName())
	if s.UserID != 0 {
		db = db.Where("user_id = ?", s.UserID)
	}
	if s.ID != 0 {
		db = db.Where("id = ?", s.ID)
	}

	return db.First(s).Error
}

func (c *SampleAddress) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*SampleAddress, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *SampleAddress) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&SampleAddress{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *SampleAddress) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *SampleAddress) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(SampleAddress)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *SampleAddress) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
