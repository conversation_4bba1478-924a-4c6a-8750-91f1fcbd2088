package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Discount)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Discount)(nil))
}

type Discount struct {
	ID            uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AdminName     string    `json:"admin_name"` //发放人名称
	UserID        uint      `json:"user_id"`
	Account       string    `json:"account"`        //用户登录邮箱
	ValidDays     int       `json:"valid_days"`     //有效天数
	ExpirationAt  int64     `json:"expiration_at"`  //过期时间
	DiscountValue int       `json:"discount_value"` //折扣
	IsNot         int       `json:"is_not"`         //是否使用  0未使用 1已使用
	OrderCode     string    `json:"order_code"`     //使用的订单号
	Remark        string    `json:"remark"`         //发放原因备注
	RemindCount   int       `json:"remind_count"`   // 是否已经提醒过用户优惠券快过期

	MaxCount int `json:"max_count" gorm:"default:0"` //最大可使用件数
	MinCount int `json:"min_count" gorm:"default:0"` //最小可使用件数
}

type DiscountResp struct {
	ID            uint  `gorm:"PRIMARY_KEY" json:"id"`
	DiscountValue int   `json:"discount_value"` //折扣
	ExpirationAt  int64 `json:"expiration_at"`  //过期时间
	MaxCount      int   `json:"max_count"`      //最大可使用件数
	MinCount      int   `json:"min_count"`      //最小可使用件数
}

func (d *Discount) TableName() string {
	return "discount"
}

// 新增sku
func (d *Discount) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(d.TableName()).Create(d).Error
}

func (d *Discount) GetDiscountInfo() error {
	return mysql.NewConn().Model(d).Where("id = ?", d.ID).First(d).Error
}

func (d *Discount) GetList() (list []*DiscountResp, err error) {
	db := mysql.NewConn().Table(d.TableName())
	err = db.Where("user_id = ?", d.UserID).Where("is_not = ?", 0).
		Where("expiration_at > ?", time.Now().Unix()).
		Find(&list).Error
	return
}

func (d *Discount) AdminGet(pn, ps, state int) (list []*Discount, count int, err error) {

	db := mysql.NewConn().Table(d.TableName())

	if d.ID != 0 {
		db = db.Where("id = ?", d.ID)
	}
	if d.Account != "" {
		db = db.Where("account LIKE ?", "%"+d.Account+"%")
	}
	if d.DiscountValue != 0 {
		db = db.Where("discount_value = ?", d.DiscountValue)
	}
	log.Info(state, "state")
	if state == 0 {
		db = db.Where("is_not = ?", 0)
	}
	if state == 1 {
		db = db.Where("is_not = ?", 1)
	}
	if state == 2 {
		db = db.Where("expiration_at <= ?", time.Now().Unix()).Where("is_not = ?", 0)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (d *Discount) UpdateUse(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(d).Where("id = ?", d.ID).Updates(d).Error
}

func (d *Discount) BackDiscount() error {
	return mysql.NewConn().Model(&Discount{}).Where("id = ?", d.ID).Update("is_not", 0).Error
}

func (d *Discount) QueryCouponExpired() (list []*Discount, err error) {
	err = mysql.NewConn().Table(d.TableName()).Where("remind_count < 1").Where("is_not = ?", 0).
		Where("expiration_at <= ?", time.Now().Add(time.Hour*48).Unix()).Where("expiration_at > ?", time.Now().Unix()).Find(&list).Error
	return
}

func (d *Discount) UpdateRemindCount() error {
	return mysql.NewConn().Model(&Discount{}).Where("id = ?", d.ID).Update("remind_count", d.RemindCount).Error
}

func (c *Discount) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Discount, 0)

	err := db.Find(&list).Error

	return list, err
}

func (d *Discount) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Discount{}).Where("id IN (?)", ids).Delete(d).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Discount) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Discount) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Discount)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Discount) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
