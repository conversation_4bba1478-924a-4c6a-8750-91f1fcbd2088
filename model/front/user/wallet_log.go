package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*WalletLog)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*WalletLog)(nil))
}

const (
	TransactionStatusDefault = iota
	TransactionStatusApproved
	TransactionStatusPending
	TransactionStatusComplete  = 10
	TransactionStatusRefunding = 11
	TransactionStatusFailed    = 20
)

var WalletLogStatusDesc = map[int]string{
	TransactionStatusDefault:   "Completed",
	TransactionStatusApproved:  "Pending",
	TransactionStatusPending:   "Delayed Transaction",
	TransactionStatusComplete:  "Completed",
	TransactionStatusRefunding: "Refunding",
	TransactionStatusFailed:    "Failed",
}

const (
	ShowTypeDefault = 0
	ShowTypeHidden  = 1
	ShowTypeDisplay = 2
)

type WalletLog struct {
	ID                    uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt             time.Time `json:"created_at"` //创建时间
	UpdatedAt             time.Time `json:"updated_at"` //更新时间
	UserID                uint      `json:"user_id"`
	PrePaid               int       `json:"pre_paid"`                   //充值方式
	TradeSum              int       `json:"trade_sum"`                  //交易金额,单位美分
	TradeType             int       `json:"trade_type"`                 //交易类型  支出 退款 充值
	Balance               int       `json:"balance"`                    //当前余额
	Other                 string    `json:"other"`                      //备注 预留字段
	OrderCode             string    `json:"order_code"`                 // pod 订单号
	MoneyOrder            string    `json:"money_order"`                // paypal order id
	TransactionID         string    `json:"transaction_id"`             // paypal 交易流水 id
	TransactionStatus     int       `json:"transaction_status"`         // 交易状态，0（适配旧数据）或10：completed，1：pending，2：Delayed Transaction，20：failed
	TransactionStatusDesc string    `json:"order_pay_state_desc"`       // 充值状态说明，目前来说：触发风控（PENDING_REVIEW），电子支票（ECHECK）
	ShowType              int       `json:"show_type" gorm:"default:0"` // 展示状态，目前用于钱海，0：展示（适配旧数据），1：不展示，2：展示
	OceanPaymentId        string    `json:"ocean_payment_id"`           // 钱海，支付ID，Oceanpayment的支付唯一单号
}

type WalletResp struct {
	ID                uint   `json:"id"`
	CreatedAt         int64  `json:"created_at"`         //创建时间
	PrePaid           int    `json:"pre_paid"`           //充值方式
	TradeSum          int    `json:"trade_sum"`          //交易金额,单位美分
	TradeType         int    `json:"trade_type"`         //交易类型  支出 退款 充值
	Balance           int    `json:"balance"`            //当前余额
	Other             string `json:"other"`              //备注 预留字段
	OrderCode         string `json:"order_code"`         // pod 订单号
	TransactionStatus int    `json:"transaction_status"` // 交易状态，0（适配旧数据）或10：completed，1：pending，2：Delayed Transaction，20：failed
}

func (w *WalletLog) TableName() string {
	return "wallet_log"
}

// 新增
func (w *WalletLog) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

func (w *WalletLog) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(w.TableName()).Where("id = ?", w.ID)
	return db.First(w).Error
}

func (w *WalletLog) QueryUserPre() (list []*WalletLog, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("id < ?", w.ID)
	if w.UserID > 0 {
		db = db.Where("user_id = ?", w.UserID)
	}
	err = db.Order("id desc").Limit(40).Find(&list).Error
	return
}

func (w *WalletLog) QueryListByOrderCode(tx ...*gorm.DB) (list []*WalletLog, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(w.TableName()).Where("order_code = ?", w.OrderCode)
	err = db.Order("id asc").Find(&list).Error
	return
}

// 根据交易流水查询
func (w *WalletLog) QueryByTransactionID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Where("transaction_id = ?", w.TransactionID).First(w).Error
}

// 根据 paypal order 查询
func (w *WalletLog) QueryByMoneyOrder(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Where("money_order = ?", w.MoneyOrder).First(w).Error
}

// 根据交易流水更新
func (w *WalletLog) UpdateByTransactionID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&WalletLog{}).Where("transaction_id = ?", w.TransactionID).Update(w).Error
}

// 根据主键 id 更新
func (w *WalletLog) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&WalletLog{}).Where("id = ?", w.ID).Update(w).Error
}

func (w *WalletLog) UpdateFailedRecharge() (err error) {

	db := mysql.NewConn().Model(&WalletLog{})

	// 充值状态=已授权
	db = db.Where("transaction_status = ?", TransactionStatusApproved)
	// 更新时间和当前时间差一个小时
	db = db.Where("updated_at <= FROM_UNIXTIME(?)", time.Now().Add(time.Hour*-6).Unix())

	err = db.Updates(map[string]interface{}{
		"transaction_status": TransactionStatusFailed,
		"updated_at":         time.Now(),
	}).Error
	return
}

func (w *WalletLog) GetLogList(pn, ps int, timeS, timeE int64) (list []*WalletLog, count int, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("user_id = ?", w.UserID)

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if w.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+w.OrderCode+"%")
	}
	if w.TradeType != 0 {
		if w.TradeType == 1 {
			db = db.Where("trade_type = ? OR trade_type = ?", 1, 5)
		} else {
			db = db.Where("trade_type = ?", w.TradeType)
		}
	}

	db = db.Where("show_type != ?", ShowTypeHidden)

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	//err = db.Offset((pn - 1) * ps).Limit(ps).Order("id desc").Find(&list).Error
	err = db.Offset((pn - 1) * ps).Limit(ps).Order("UNIX_TIMESTAMP(updated_at) DESC,id DESC").Find(&list).Error
	return
}

func (w *WalletLog) QueryToShippedPaypal() (list []*WalletLog, count int, err error) {

	db := mysql.NewConn().Model(w) //.Where("trade_type = ?", define.RECHARGE)
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("user_id asc,id asc"). /*.Offset(10).Limit(10)*/ Find(&list).Error

	return
}

func (c *WalletLog) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*WalletLog, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *WalletLog) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&WalletLog{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *WalletLog) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *WalletLog) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(WalletLog)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *WalletLog) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 查询48小时内的充值记录
func (c *WalletLog) GetRechargeList() (list []*WalletLog, err error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("user_id = ?", c.UserID)
	db = db.Where("trade_type = ?", define.RECHARGE).Where("show_type != ?", ShowTypeHidden)
	db = db.Where("created_at >= FROM_UNIXTIME(?)", time.Now().Add(time.Hour*-48).Unix())

	err = db.Find(&list).Error

	return
}

type userIDItem struct {
	UserID uint `json:"user_id"`
}

// 查找至少包含一条钱包流水的 user_id List
func (c *WalletLog) GetUserIdList() (list []*userIDItem, err error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Select("DISTINCT user_id")

	err = db.Scan(&list).Error

	return
}
