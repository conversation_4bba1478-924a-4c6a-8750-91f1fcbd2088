package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/model/front/order"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*TradeRecord)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*TradeRecord)(nil))
}

type TradeRecord struct {
	ID                 uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt          time.Time `json:"created_at"`                  //创建时间
	UpdatedAt          time.Time `json:"updated_at"`                  //更新时间
	UserId             uint      `json:"user_id" gorm:"not null"`     //用户ID
	OrderCode          string    `json:"order_code"  gorm:"not null"` //订单号
	TradeType          int       `json:"trade_type"`                  //交易类型  支付|退款 1|2
	Payment            int       `json:"payment"`                     //支付方式   参照const
	TradeSum           int       `json:"trade_sum"`                   //交易金额,单位美分
	TransactionStatus  int       `json:"transaction_status"`          // 交易状态，0（适配旧数据）或10：completed，1：pending，2：Delayed Transaction，20：failed
	OceanPaymentId     string    `json:"ocean_payment_id"`            // 钱海，支付ID，Oceanpayment的支付唯一单号
	OceanRefundID      string    `json:"ocean_refund_id"`             // 钱海，退款id
	OceanRefundResults string    `json:"ocean_refund_results"`        // 钱海，退款结果
	Other              string    `json:"other"`                       // 备注
}

type TradeResp struct {
	ID                uint   `json:"id"`
	CreatedAt         int64  `json:"created_at"`              //创建时间
	UserId            uint   `json:"user_id" gorm:"not null"` //用户ID
	OrderCode         string `json:"order"  gorm:"not null"`  //订单号
	TradeType         int    `json:"trade_type"`              //交易类型  支付|退款 1|2
	Payment           int    `json:"payment"`                 //支付方式   参照const
	TradeSum          int    `json:"trade_sum"`               //交易金额,单位美分
	TransactionStatus int    `json:"transaction_status"`      // 交易状态，0（适配旧数据）或10：completed，1：pending，2：Delayed Transaction，20：failed
	Other             string `json:"other"`                   //备注
}

func (t *TradeRecord) TableName() string {
	return "trade_record"
}

// 新增
func (t *TradeRecord) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(t.TableName()).Create(t).Error
}

// 获取列表
func (t *TradeRecord) GetList(pn, ps int, timeS, timeE int64) (list []*TradeRecord, count int, err error) {
	db := mysql.NewConn().Table(t.TableName()).Where("user_id = ?", t.UserId)

	if timeS != 0 {

		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if t.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+t.OrderCode+"%")
	}

	if t.TradeType != 0 {
		if t.TradeType == 1 {
			db = db.Where("trade_type = ? OR trade_type = ?", t.TradeType, 5)
		} else {
			db = db.Where("trade_type = ?", t.TradeType)
		}
	}

	if t.Payment != 0 {
		if t.Payment == 2 {
			db = db.Where("payment = ? OR payment = ?", t.Payment, 3)
		} else {
			db = db.Where("payment = ?", t.Payment)
		}
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Offset((pn - 1) * ps).Limit(ps).Order("id desc").Find(&list).Error
	return
}

// 查询
func (t *TradeRecord) Query(pn, ps int) (List []*TradeRecord, count int, err error) {
	db := mysql.NewConn().Table(t.TableName())
	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}
	if t.UserId > 0 {
		db = db.Where("user_id = ?", t.UserId)
	}
	if t.OrderCode != "" {
		db = db.Where("order like ?", "%"+t.OrderCode+"%")
	}
	if t.TradeType > 0 {
		db = db.Where("trade_type = ?", t.TradeType)
	}
	err = db.Find(&List).Error
	return
}

// 查询钱海支付的订单流水
func (t *TradeRecord) QueryOcean() (err error) {
	db := mysql.NewConn().Table(t.TableName())
	if len(t.OrderCode) > 0 {
		db = db.Where("order_code = ?", t.OrderCode)
	}
	if len(t.OceanPaymentId) > 0 {
		db = db.Where("ocean_payment_id = ?", t.OceanPaymentId)
	}
	err = db.First(t).Error
	return
}

// 统计数量
func (t *TradeRecord) Count() (count int, err error) {
	db := mysql.NewConn().Table(t.TableName())
	if len(t.OrderCode) > 0 {
		db = db.Where("order_code = ?", t.OrderCode)
	}
	err = db.Count(&count).Error
	return
}

// 更新
func (t *TradeRecord) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(t).Where("id = ?", t.ID).Updates(t).Error
}

// 根据 钱海 的支付 id 更新
func (t *TradeRecord) UpdateRefundByPaymentID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(t).Where("ocean_payment_id = ?", t.OceanPaymentId).Where("order_code = ?", t.OrderCode).
		Where("trade_type = ?", t.TradeType).Updates(t).Error
}

func (t *TradeRecord) UpdateFailedRecord(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 交易类型为支付，支付方式为 paypal，交易状态为
	db = db.Model(t).Where("trade_type = ?", 1).Where("payment = ?", 1).Where("transaction_status = ? OR transaction_status = ?", 1, 2)

	// 更新时间和当前时间差一个小时
	db = db.Where("updated_at <= FROM_UNIXTIME(?)", time.Now().Add(time.Hour*-6).Unix())

	err = db.Updates(map[string]interface{}{
		"transaction_status": order.PayStateFailed,
		"updated_at":         time.Now(),
	}).Error

	return
}

func (c *TradeRecord) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*TradeRecord, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *TradeRecord) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&TradeRecord{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *TradeRecord) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *TradeRecord) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(TradeRecord)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *TradeRecord) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 查找近一小时 支付的订单数量
func (c *TradeRecord) GetPayCountInOneHour() (count int, err error) {

	db := mysql.NewConn().Table(c.TableName()).Where("user_id = ?", c.UserId)

	db = db.Where("trade_type = ?", 1).
		Where("transaction_status != ?", 20)

	// 更新时间和当前时间差一个小时
	db = db.Where("updated_at >= FROM_UNIXTIME(?)", time.Now().Add(time.Hour*-1).Unix())

	err = db.Count(&count).Error

	return
}
