package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	design "zx/zx-consistency/model/front/template/design"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*AssociatedShop)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*AssociatedShop)(nil))
}

const (
	_ = iota
	SHOP_TYPE_SHOPIFY
	SHOP_TYPE_ETSY
	SHOP_TYPE_WIX
	SHOP_TYPE_WOO
	SHOP_TYPE_SQUARE
)

type AssociatedShop struct {
	ID                     uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`
	UserID                 uint      `json:"user_id"    gorm:"not null"`
	ShopID                 string    `json:"shop_id"`                             //商店id
	ShopName               string    `json:"shop_name"  gorm:"unique;not null,"`  //店铺名称
	ShopType               int       `json:"shop_type"  gorm:"not null"`          //1 shopify  2etsy 3wix 4 woo 5 square
	Token                  string    `json:"token"      gorm:"not null"`          //授权token
	TokenOvertime          int64     `json:"token_overtime"`                      //token的过期时间
	RefreshToken           string    `json:"refresh_token"`                       //刷新token
	Other                  string    `json:"other"`                               //备注
	OrderImportSet         int       `json:"order_import_set"`                    //1 只同步由PODPartner生产的订单， 2 同步所有订单
	TransferSet            int       `json:"transfer_set"`                        //运输设置， 1 最便宜的方式， 2 最快的方式  3 中间的运输方式
	AutoPay                *bool     `json:"auto_pay" gorm:"default:0"`           //订单自动支付，只有开通POD钱包才可以选择此功能
	PwpPay                 *bool     `json:"pwp_pay"  gorm:"default:0"`           // p卡 pwp 自动支付开关
	RemoteAutoPay          *bool     `json:"remote_auto_pay" gorm:"default:0"`    // 偏远订单自动支付，只有开通POD钱包才可以选择此功能
	CustomCollection       string    `json:"custom_collection" gorm:"type:BLOB;"` // 序列化后的 shopify collection
	IsIgnor                *bool     `json:"is_ignor" gorm:"default:0"`           // 自动忽略非PODPartner 的产品
	FulfillmentServiceID   int64     `json:"fulfillment_service_id"`              // 创建的 fulfillment service 的 id
	LocationID             int64     `json:"location_id"`                         // 创建的 fulfillment service 的 location_id
	AdminGraphqlAPIID      string    `json:"admin_graphql_api_id"`                // shopify 商店的 admin_graphql_api_id
	FulfillmentServiceName string    `json:"fulfillment_service_name"`            // 创建的 fulfillment service 的 name
	WebhooksID             string    `json:"webhooks_id"`                         // 注册的回调 id ，一维数组 | 也用于squarespace商店 注册WebHook 的id | woocommerce 也使用
	ShopNameText           string    `json:"shop_name_text"`                      // 店铺名称 显示文本
	ThirdID                int64     `json:"third_id"`                            // 第三方平台该店铺的 ID
	SaleItemCount          int       `json:"sale_item_count"`                     // 第三方店铺总订单数，包括非 pod 平台的订单
	ShopUrl                string    `json:"shop_url"`                            // 目前用于 etsy，也用于 wix 和 squarespace
	InstanceId             string    `json:"instance_id"`                         // 目前用于 wix，用于唯一标识一个 wix 商店 | 也用于squarespace商店，标识一个站点 | shopify GraphQL店铺 location id
	ChannelId              string    `json:"channel_id"`                          // shopify 在线店铺的 channel_id

	EtsyShippingProfile string `json:"etsy_shipping_profile"  gorm:"type:BLOB;"` //etsy商店的 ShippingProfile 记录 | 也用于squarespace商店的StorePages
	OrderLastSyncTime   int64  `json:"order_last_sync_time"`                     //etsy商店的 最后订单同步时间

	// woocommerce 独有的配置
	WooDomain      string `json:"woo_domain"`      // woo 域名，连接时使用
	ConsumerKey    string `json:"consumer_key"`    // woo 授权 key
	ConsumerSecret string `json:"consumer_secret"` // woo 授权 secret
	KeyID          int64  `json:"key_id"`          // woo 授权 key id
	KeyPermissions string `json:"key_permissions"` // woo 权限说明
	SyncCount      int    `json:"sync_count"`      // 同步数量，第一次同步成功后加1，删除后减1
	LastSync       int64  `json:"last_sync"`       // 最后一次同步的时间戳

	SquarespaceWebhookSecret string `json:"squarespace_webhook_secret"`        // squarespace 商店 webhook 签名 key
	SSCurrency               string `json:"ss_currency"  gorm:"default:'USD'"` // squarespace 商店 币种

	// 商店绑定吊牌和包装袋的新增数据  2022-9-14 17:35:01 zc
	TagDesignID  uint `json:"tag_design_id" gorm:"default:0"`   // 与模板绑定的 吊牌设计ID
	PackDesignID uint `json:"pack_design_id"  gorm:"default:0"` // 包装设计ID

	ShopState int    `json:"shop_state" gorm:"default:1"` // 商店状态 1 正常、2 失效
	ShopErr   string `json:"shop_err"  gorm:"type:BLOB;"` // 商店失效err 原因

	/* 时间戳，同步店铺的 tiktok 订单时，判断 >=10 即可
	0: 默认值
	10: 标记为需要同步 tiktok 订单的店铺
	10<: 最近一次的拉取订单信息的时间
	*/
	PullOrderTime int64  `json:"pull_order_time" gorm:"default:0"` // shopify 同步 tiktok 订单的时间
	WooUserAgent  string `json:"woo_user_agent"`                   // woo店铺详情请求头标识
}

type DesignShopList struct {
	ID             string             `json:"id"`                               //商店id
	ShopName       string             `json:"shop_name"  gorm:"not null"`       //店铺名称
	ShopNameText   string             `json:"shop_name_text"`                   // 店铺名称 显示文本
	ShopType       int                `json:"shop_type"  gorm:"not null"`       //1 shopify 2etsy
	AutoPay        *bool              `json:"auto_pay"`                         //订单自动支付，只有开通POD钱包才可以选择此功能
	PwpPay         *bool              `json:"pwp_pay"  gorm:"default:0"`        // p卡 pwp 自动支付开关
	RemoteAutoPay  *bool              `json:"remote_auto_pay" gorm:"default:0"` // 偏远订单自动支付，只有开通POD钱包才可以选择此功能
	IsIgnor        *bool              `json:"is_ignor"`                         // 自动忽略非PODPartner 的产品
	TransferSet    int                `json:"transfer_set"`                     //运输设置， 1 最便宜的方式， 2 最快的方式
	ShopUrl        string             `json:"shop_url"`                         // 目前用于 etsy，也用于 wix
	ConsumerKey    string             `json:"consumer_key"`                     // woo 授权 key
	ConsumerSecret string             `json:"consumer_secret"`                  // woo 授权 secret
	SyncCount      int                `json:"sync_count"`                       // 同步数量，第一次同步成功后加1，删除后减1
	TagDesignID    uint               `json:"tag_design_id"`
	PackDesignID   uint               `json:"pack_design_id"`                                                               // 包装设计ID
	ShopState      int                `json:"shop_state" gorm:"default:1"`                                                  // 商店状态 1 正常、2 失效
	ShopErr        string             `json:"shop_err"  gorm:"type:BLOB;"`                                                  // 商店失效err 原因
	TagDesignInfo  *design.TagDesign  `json:"tag_design_info"  gorm:"foreignkey:tag_design_id;association_foreignkey:id"`   // 吊牌信息
	PackDesignInfo *design.PackDesign `json:"pack_design_info"  gorm:"foreignkey:pack_design_id;association_foreignkey:id"` // 吊牌信息
	SaleItemCount  int                `json:"sale_item_count"`                                                              //第三方店铺总订单数，包括非 pod 平台的订单
	LastSync       int64              `json:"last_sync"`                                                                    //最后一次同步的时间戳
	UserId         uint               `json:"user_id"`                                                                      //用户ID
	CreatedAt      time.Time          `json:"create_at"`
}

type ShippingProfile struct {
	ShippingProfileID int64  `json:"shipping_profile_id"`
	Title             string `json:"title"`
}

type SStorePages struct {
	ID        string `json:"id"`        //唯一存储页面id
	Title     string `json:"title"`     //存储页面标题;标题显示在商户网站上。
	IsEnabled bool   `json:"isEnabled"` //指定是否启用Store Page(即网站访问者可以访问)。
}

func (a *AssociatedShop) TableName() string {
	return "associated_shop"
}

// 新增一个绑定商店
func (a *AssociatedShop) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(a.TableName()).Create(a).Error
}

// 解除一个绑定商店
func (a *AssociatedShop) Delete(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(a.TableName()).Where("shop_name = ?", a.ShopName).Delete(a).Error
}

// 解除一个绑定商店
func (a *AssociatedShop) DeleteOne(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(a.TableName())
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	if len(a.WooDomain) > 0 {
		db = db.Where("woo_domain = ?", a.WooDomain)
	}

	count := 0
	err := db.Count(&count).Error
	if err != nil {
		return err
	}

	if count > 1 {
		return errors.New("Unknow error.")
	}

	return db.Delete(a).Error
}

// 每个商品第一次同步到第三方店铺成功后，增加同步数量记录
func (a *AssociatedShop) AddSyncCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&AssociatedShop{}).Where("id = ?", a.ID).UpdateColumn("last_sync", a.LastSync).
		UpdateColumn("sync_count", gorm.Expr("sync_count + ?", 1)).Error
}

// 删除商店商品后，减少同步数量记录
func (a *AssociatedShop) SubSyncCount(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&AssociatedShop{}).Where("id = ?", a.ID).UpdateColumn("sync_count", gorm.Expr("sync_count - ?", 1)).Error
}

// 刷新token及RefreshToken
func (a *AssociatedShop) PutToken(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&AssociatedShop{}).Where("id = ?", a.ID).Select("token", "refresh_token").Updates(a).Error
}

func (a *AssociatedShop) GetListByIDList(idList []int) (list []*DesignShopList, err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("id IN (?)", idList).Find(&list).Error
	return
}

// 根据店铺名查询是否已经绑定过
func (a *AssociatedShop) ShopExist() (count int, err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.ThirdID != 0 {
		db = db.Where("third_id = ?", a.ThirdID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if len(a.WooDomain) > 0 {
		db = db.Where("woo_domain = ?", a.WooDomain)
	}
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	if a.ShopType != 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}
	err = db.Count(&count).Error
	return
}

// 获取一个用户下所有已绑定的店铺
func (a *AssociatedShop) QueryShopsByUserID(preload ...string) (list []*DesignShopList, err error) {

	db := mysql.NewConn().Table(a.TableName())
	if len(preload) != 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}
	err = db.Where("user_id = ?", a.UserID).Find(&list).Error
	return
}

// 根据 id 获取商店信息
func (a *AssociatedShop) QueryByShopID() (err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("id = ?", a.ID).First(a).Error
	return
}

// 根据 id 获取商店信息
func (a *AssociatedShop) Query() (err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	if len(a.WooDomain) > 0 {
		db = db.Where("woo_domain = ?", a.WooDomain)
	}
	if a.ShopType != 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}
	err = db.First(a).Error
	return
}

// 在连接店铺时进行检索店铺的操作
func (a *AssociatedShop) QueryAllByInstance() (err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	} else {
		return err
	}
	err = db.First(a).Error
	return
}

// 查询所有店铺数据
func (a *AssociatedShop) QueryAll() (list []*AssociatedShop, err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if a.ShopType > 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}
	err = db.Find(&list).Error
	return
}

// 查询所有店铺数据
func (a *AssociatedShop) QueryAllSpe(shopMin, shopMax int) (list []*AssociatedShop, err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if a.ShopType > 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}
	if shopMin > 0 {
		db = db.Where("id >= ?", shopMin)
	}
	if shopMax > 0 {
		db = db.Where("id <= ?", shopMax)
	}
	err = db.Order("id desc").Find(&list).Error
	return
}

// 根据 id 获取商店信息
func (a *AssociatedShop) QueryByShopName() (err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("shop_name = ?", a.ShopName).First(a).Error
	return
}

// 根据 ThirdID 获取etsy 商店信息
func (a *AssociatedShop) QueryByThirdID() (err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("third_id = ?", a.ThirdID).First(a).Error
	return
}

// 根据 id 获取商店信息
func (a *AssociatedShop) AddSaleItemCount() (err error) {
	err = mysql.NewConn().Model(&AssociatedShop{}).Where("instance_id = ?", a.InstanceId).
		UpdateColumn("sale_item_count", gorm.Expr("sale_item_count + ?", 1)).Error
	return err
}

// 根据 店铺名和用户id 更新商店信息
func (a *AssociatedShop) Update() (err error) {

	db := mysql.NewConn().Model(&AssociatedShop{})
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	err = db.Update(a).First(a).Error
	return
}

// 根据 店铺名和用户id 更新商店信息
func (a *AssociatedShop) UpdateWithTx(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&AssociatedShop{})
	if len(a.ShopName) > 0 {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	if len(a.InstanceId) > 0 {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	err = db.Update(a).First(a).Error
	return
}

func (a *AssociatedShop) UpdateSyncCount(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&AssociatedShop{}).Where("id = ?", a.ID).Updates(map[string]interface{}{
		"sync_count": a.SyncCount,
		"last_sync":  a.LastSync,
	}).Error
}

// 根据店铺 id 更新
func (a *AssociatedShop) UpdateByID() (err error) {

	db := mysql.NewConn().Model(&AssociatedShop{})
	if a.ID > 0 {
		db = db.Where("id = ?", a.ID)
	}
	err = db.Update(a).Error
	return
}

// 根据商店名称查找商店信息
func (a *AssociatedShop) QueryShopCollectionsByShopName() (resp AssociatedShop, err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("shop_name = ?", a.ShopName).First(&resp).Error
	return
}

func (a *AssociatedShop) UpDateByThirdID() error {
	return mysql.NewConn().Model(&AssociatedShop{}).Where("third_id = ?", a.ThirdID).
		Update("etsy_shipping_profile", a.EtsyShippingProfile).First(a).Error
}

func (a *AssociatedShop) UpDateByInstanceID() error {
	return mysql.NewConn().Model(&AssociatedShop{}).Where("instance_id = ?", a.InstanceId).
		Update("etsy_shipping_profile", a.EtsyShippingProfile).First(a).Error
}

// 更新 etsy | Squarespace 商店的RefreshToken
func (a *AssociatedShop) UpdateRefreshToken() error {
	db := mysql.NewConn().Model(&AssociatedShop{})
	if a.ShopName != "" {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.InstanceId != "" {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	if a.ShopType != 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}

	err := db.Update("refresh_token", a.RefreshToken).Update("shop_state", 1).Error
	return err
}

func (a *AssociatedShop) UpdateShopState() (err error) {
	db := mysql.NewConn().Model(&AssociatedShop{})
	if a.ShopName != "" {
		db = db.Where("shop_name = ?", a.ShopName)
	}
	if a.InstanceId != "" {
		db = db.Where("instance_id = ?", a.InstanceId)
	}
	if a.ShopType != 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}
	err = db.Updates(map[string]interface{}{
		"shop_state": a.ShopState,
		"shop_err":   a.ShopErr,
	}).Error
	return err
}

// 根据商店类型，获取一类商店
func (a *AssociatedShop) GetListByShopType() (list []*AssociatedShop, err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("shop_type = ?", a.ShopType).Find(&list).Error
	return
}

func (a *AssociatedShop) GetList() (list []*AssociatedShop, err error) {

	db := mysql.NewConn().Table(a.TableName())

	if a.ShopType > 0 {
		db = db.Where("shop_type = ?", a.ShopType)
	}

	err = db.Find(&list).Error
	return
}

// 获取所有需要订单拉取订单的店铺
func (a *AssociatedShop) GetPullOrderShops() (list []*AssociatedShop, err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("pull_order_time >= 10").Find(&list).Error
	return
}

// 更新 店铺绑定的吊牌信息
func (a *AssociatedShop) UpdateTagDesign(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&AssociatedShop{})

	if a.ID != 0 {
		db = db.Where("id = ?", a.ID)
	}

	if a.UserID != 0 {
		db = db.Where("user_id = ?", a.UserID)
	}

	return db.Updates(map[string]interface{}{
		"tag_design_id": a.TagDesignID,
	}).First(a).Error
}

// 更新 店铺绑定的包装信息
func (a *AssociatedShop) UpdatePackDesign(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&AssociatedShop{})

	if a.ID != 0 {
		db = db.Where("id = ?", a.ID)
	}

	if a.UserID != 0 {
		db = db.Where("user_id = ?", a.UserID)
	}

	return db.Updates(map[string]interface{}{
		"pack_design_id": a.PackDesignID,
	}).First(a).Error
}

func (c *AssociatedShop) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*AssociatedShop, 0)

	err := db.Find(&list).Error

	return list, err
}

func (a *AssociatedShop) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&AssociatedShop{}).Where("id IN (?)", ids).Delete(a).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *AssociatedShop) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *AssociatedShop) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(AssociatedShop)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *AssociatedShop) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 获取多个用户下的所有已绑定的店铺
func (a *AssociatedShop) QueryShopsByUserIDs(userIds []uint, preload ...string) (list []*DesignShopList, err error) {
	db := mysql.NewConn().Table(a.TableName())
	if len(preload) != 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}
	err = db.Where("user_id in (?)", userIds).Find(&list).Error
	return
}

// 根据user_id 更新店铺信息
func (a *AssociatedShop) UpdateByUserID() (err error) {
	db := mysql.NewConn().Model(&AssociatedShop{})
	if a.UserID > 0 {
		db = db.Where("user_id = ?", a.UserID)
	}
	err = db.Update(a).Error
	return
}

// 获取shopify 店铺的名称，不带[.myshopify.com]，例如 shopName = "linxb-2024-12-20.myshopify.com"，返回 "linxb-2024-12-20"
func (a *AssociatedShop) GetSimpleShopifyShopName() string {

	if strings.HasSuffix(a.ShopName, ".myshopify.com") {
		return a.ShopName[:len(a.ShopName)-14] // Remove the ".myshopify.com" suffix
	}
	return a.ShopName
}

func (a *AssociatedShop) QueryListById(ids []uint) (list []*AssociatedShop, err error) {

	db := mysql.NewConn().Model(&AssociatedShop{})

	err = db.Where("id IN (?)", ids).Find(&list).Error

	return
}
