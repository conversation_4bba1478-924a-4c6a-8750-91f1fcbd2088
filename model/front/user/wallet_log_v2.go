package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*WalletLogV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// 注意：交易状态常量和显示状态常量在wallet_log.go中已定义，这里直接使用

// WalletLogV2 钱包日志V2版本，支持充值、支出、退款等钱包交易记录管理
type WalletLogV2 struct {
	ID                    uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt             time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt             time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	UserID                uint      `json:"user_id" gorm:"not null;index"`             // 用户ID
	PrePaid               int       `json:"pre_paid" gorm:"default:0;index"`           // 充值方式
	TradeSum              int       `json:"trade_sum" gorm:"default:0"`                // 交易金额，单位美分
	TradeType             int       `json:"trade_type" gorm:"default:1;index"`         // 交易类型 支出|退款|充值
	Balance               int       `json:"balance" gorm:"default:0"`                  // 当前余额
	Other                 string    `json:"other" gorm:"type:text"`                    // 备注预留字段
	OrderCode             string    `json:"order_code" gorm:"size:64;index"`           // POD订单号
	MoneyOrder            string    `json:"money_order" gorm:"size:128;index"`         // PayPal订单ID
	TransactionID         string    `json:"transaction_id" gorm:"size:128;index"`      // PayPal交易流水ID
	TransactionStatus     int       `json:"transaction_status" gorm:"default:0;index"` // 交易状态
	TransactionStatusDesc string    `json:"order_pay_state_desc" gorm:"size:256"`      // 充值状态说明
	ShowType              int       `json:"show_type" gorm:"default:0;index"`          // 展示状态
	OceanPaymentId        string    `json:"ocean_payment_id" gorm:"size:128;index"`    // 钱海支付ID
}

// WalletRespV2 钱包日志响应结构V2
type WalletRespV2 struct {
	ID                uint   `json:"id"`
	CreatedAt         int64  `json:"created_at"`
	PrePaid           int    `json:"pre_paid"`
	TradeSum          int    `json:"trade_sum"`
	TradeType         int    `json:"trade_type"`
	Balance           int    `json:"balance"`
	Other             string `json:"other"`
	OrderCode         string `json:"order_code"`
	TransactionStatus int    `json:"transaction_status"`
}

// UserIDItem 用户ID结构
type UserIDItem struct {
	UserID uint `json:"user_id"`
}

func (w *WalletLogV2) TableName() string {
	return "wallet_log"
}

// Create 创建钱包日志记录
func (w *WalletLogV2) Create(db ...mysql.DBInterface) error {
	if len(db) > 0 {
		return db[0].Create(w).Error()
	}
	return mysql.NewUnifiedDB().Create(w).Error()
}

// Update 更新钱包日志记录
func (w *WalletLogV2) Update(db ...mysql.DBInterface) error {
	if len(db) > 0 {
		return db[0].Model(w).Where("id = ?", w.ID).Updates(w).Error()
	}
	return mysql.NewUnifiedDB().Model(w).Where("id = ?", w.ID).Updates(w).Error()
}

// Query 根据ID查询钱包日志
func (w *WalletLogV2) Query(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	return dbConn.Model(w).Where("id = ?", w.ID).First(w).Error()
}

// QueryUserPre 查询用户历史记录（限制40条）
func (w *WalletLogV2) QueryUserPre(db ...mysql.DBInterface) (list []*WalletLogV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(w).Where("id < ?", w.ID)
	if w.UserID > 0 {
		query = query.Where("user_id = ?", w.UserID)
	}

	err = query.Order("id desc").Limit(40).Find(&list).Error()
	return
}

// QueryListByOrderCode 根据订单号查询钱包日志列表
func (w *WalletLogV2) QueryListByOrderCode(db ...mysql.DBInterface) (list []*WalletLogV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	err = dbConn.Model(w).Where("order_code = ?", w.OrderCode).Order("id asc").Find(&list).Error()
	return
}

// QueryByTransactionID 根据交易流水ID查询
func (w *WalletLogV2) QueryByTransactionID(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	return dbConn.Model(w).Where("transaction_id = ?", w.TransactionID).First(w).Error()
}

// QueryByMoneyOrder 根据PayPal订单查询
func (w *WalletLogV2) QueryByMoneyOrder(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	return dbConn.Model(w).Where("money_order = ?", w.MoneyOrder).First(w).Error()
}

// UpdateByTransactionID 根据交易流水ID更新
func (w *WalletLogV2) UpdateByTransactionID(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	return dbConn.Model(&WalletLogV2{}).Where("transaction_id = ?", w.TransactionID).Updates(w).Error()
}

// UpdateFailedRecharge 更新失败的充值记录
func (w *WalletLogV2) UpdateFailedRecharge(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	// 充值状态=已授权，更新时间超过6小时
	cutoffTime := time.Now().Add(-6 * time.Hour)
	query := dbConn.Model(&WalletLogV2{}).
		Where("transaction_status = ?", TransactionStatusApproved).
		Where("updated_at <= ?", cutoffTime)

	updateData := map[string]interface{}{
		"transaction_status": TransactionStatusFailed,
		"updated_at":         time.Now(),
	}

	return query.Updates(updateData).Error()
}

// GetLogList 获取用户钱包日志列表
func (w *WalletLogV2) GetLogList(pn, ps int, timeS, timeE int64, db ...mysql.DBInterface) (list []*WalletLogV2, count int64, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(w).Where("user_id = ?", w.UserID)

	if timeS != 0 {
		query = query.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		query = query.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if w.OrderCode != "" {
		query = query.Where("order_code LIKE ?", "%"+w.OrderCode+"%")
	}
	if w.TradeType != 0 {
		if w.TradeType == 1 {
			query = query.Where("trade_type = ? OR trade_type = ?", 1, 5)
		} else {
			query = query.Where("trade_type = ?", w.TradeType)
		}
	}

	// 排除隐藏记录
	query = query.Where("show_type != ?", ShowTypeHidden)

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	if pn > 0 && ps > 0 {
		offset := (pn - 1) * ps
		query = query.Offset(offset).Limit(ps)
	}

	err = query.Order("UNIX_TIMESTAMP(updated_at) DESC, id DESC").Find(&list).Error()
	return
}

// QueryToShippedPaypal 查询PayPal相关记录
func (w *WalletLogV2) QueryToShippedPaypal(db ...mysql.DBInterface) (list []*WalletLogV2, count int64, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(w)

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	err = query.Order("user_id asc, id asc").Find(&list).Error()
	return
}

// GetDetailList 批量获取钱包日志详情
func (w *WalletLogV2) GetDetailList(ids []uint, db ...mysql.DBInterface) (interface{}, error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	if len(ids) == 0 {
		return []*WalletLogV2{}, nil
	}

	var list []*WalletLogV2
	err := dbConn.Model(w).Where("id IN ?", ids).Find(&list).Error()
	return list, err
}

// DeleteByIds 批量删除钱包日志
func (w *WalletLogV2) DeleteByIds(ids []uint, db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	if len(ids) == 0 {
		return nil
	}

	return dbConn.Where("id IN ?", ids).Delete(&WalletLogV2{}).Error()
}

// UpdateWithZero 工厂本地专用，不更新updated_at字段的更新方法
func (w *WalletLogV2) UpdateWithZero(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	// 首先更新除updated_at外的所有字段
	err := dbConn.Model(w).
		Session(&mysql.Session{SkipHooks: true}).
		Select("*").
		Omit("updated_at").
		Where("id = ?", w.ID).
		Updates(w).Error()
	if err != nil {
		return err
	}

	// 然后单独更新updated_at字段
	return dbConn.Model(w).Where("id = ?", w.ID).Update("updated_at", w.UpdatedAt).Error()
}

// CreateOrUpdate 创建或更新钱包日志（用于同步）
func (w *WalletLogV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("CreateOrUpdate detail data:", string(detailData))

	detailObj := new(WalletLogV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		return ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
	}

	switch idMapType[detailObj.ID] {
	case 1:
		// 创建新记录
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	case 2:
		// 更新现有记录
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
	}

	return nil
}

// GetVerifyHead 获取校验头信息
func (w *WalletLogV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64, db ...mysql.DBInterface) (list []*model.VerifyHead, count int64, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(w).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		query = query.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		query = query.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		query = query.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		query = query.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 && pn > 0 && ps > 0 {
		offset := (pn - 1) * ps
		query = query.Offset(offset).Limit(ps)
	}

	err = query.Find(&list).Error()
	return
}

// GetRechargeList 查询48小时内的充值记录
func (w *WalletLogV2) GetRechargeList(db ...mysql.DBInterface) (list []*WalletLogV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	// 48小时前的时间点
	cutoffTime := time.Now().Add(-48 * time.Hour)

	err = dbConn.Model(w).
		Where("user_id = ?", w.UserID).
		Where("trade_type = ?", define.RECHARGE).
		Where("show_type != ?", ShowTypeHidden).
		Where("created_at >= ?", cutoffTime).
		Find(&list).Error()
	return
}

// GetUserIdList 查找至少包含一条钱包流水的用户ID列表
func (w *WalletLogV2) GetUserIdList(db ...mysql.DBInterface) (list []*UserIDItem, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	err = dbConn.Model(w).Select("DISTINCT user_id").Scan(&list).Error()
	return
}

// IsExist 检查钱包日志是否存在
func (w *WalletLogV2) IsExist(db ...mysql.DBInterface) (bool, error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	var count int64
	query := dbConn.Model(w)

	if w.ID > 0 {
		query = query.Where("id = ?", w.ID)
	} else if w.TransactionID != "" {
		query = query.Where("transaction_id = ?", w.TransactionID)
	} else if w.MoneyOrder != "" {
		query = query.Where("money_order = ?", w.MoneyOrder)
	} else {
		return false, fmt.Errorf("insufficient conditions for existence check")
	}

	err := query.Count(&count).Error()
	return count > 0, err
}

// GetUserBalance 获取用户当前余额（最新记录的余额）
func (w *WalletLogV2) GetUserBalance(userID uint, db ...mysql.DBInterface) (balance int, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	var latestLog WalletLogV2
	err = dbConn.Model(&WalletLogV2{}).
		Where("user_id = ?", userID).
		Order("id desc").
		First(&latestLog).Error()

	if err != nil {
		return 0, err
	}

	return latestLog.Balance, nil
}

// GetTransactionsByStatus 根据交易状态查询记录
func (w *WalletLogV2) GetTransactionsByStatus(status int, limit int, db ...mysql.DBInterface) (list []*WalletLogV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(w).Where("transaction_status = ?", status)

	if limit > 0 {
		query = query.Limit(limit)
	}

	err = query.Order("id desc").Find(&list).Error()
	return
}
