package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
)

func init() {
	mysql.RegisterTable((*UserInfoV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*UserInfoV2)(nil))
}

// UserInfoV2 - 用户信息V2模型，处理高风险API迁移
type UserInfoV2 struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Email       string    `json:"email" gorm:"unique;not null"`
	Account     string    `json:"account" gorm:"unique;not null"`
	Password    string    `json:"password" gorm:"not null"`
	PhoneNumber string    `json:"phone_number" gorm:"default:''"`
	Avatar      string    `json:"avatar" gorm:"default:''"`
	FirstName   string    `json:"first_name" gorm:"default:''"`
	LastName    string    `json:"last_name" gorm:"default:''"`
	PayPalEmail string    `json:"pay_pal_email" gorm:"default:''"`
	// 业务字段
	OrderCount  int64   `json:"order_count" gorm:"default:0"`
	DesignCount int64   `json:"design_count" gorm:"default:0"`
	SalesCount  int64   `json:"sales_count" gorm:"default:0"`
	TotalSales  float64 `json:"total_sales" gorm:"default:0"`
	// 风险控制字段
	RiskState  int    `json:"risk_state" gorm:"default:0"`
	RiskReason string `json:"risk_reason" gorm:"default:''"`
	RiskRemake string `json:"risk_remake" gorm:"default:''"`
	// 邮件订阅字段
	EmailSubscribe     int `json:"email_subscribe" gorm:"default:1"`
	MarketingSubscribe int `json:"marketing_subscribe" gorm:"default:1"`
	// 其他业务字段
	Language   string `json:"language" gorm:"default:'en'"`
	TimeZone   string `json:"time_zone" gorm:"default:'UTC'"`
	Currency   string `json:"currency" gorm:"default:'USD'"`
	Country    string `json:"country" gorm:"default:''"`
	State      string `json:"state" gorm:"default:''"`
	City       string `json:"city" gorm:"default:''"`
	Address    string `json:"address" gorm:"default:''"`
	PostalCode string `json:"postal_code" gorm:"default:''"`
	// 统计字段
	LastLoginTime time.Time `json:"last_login_time"`
	LoginCount    int64     `json:"login_count" gorm:"default:0"`
	// 关联字段
	ReferralCode string `json:"referral_code" gorm:"default:''"`
	ReferredBy   uint   `json:"referred_by" gorm:"default:0"`
}

// 实现SyncInterface接口
func (u *UserInfoV2) TableName() string {
	return "user_info"
}

func (u *UserInfoV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	db := mysql.NewUnifiedDB().Table(u.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error()
	return
}

func (u *UserInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(UserInfoV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return
}

func (u *UserInfoV2) GetDetailList(ids []uint) (list interface{}, err error) {
	db := mysql.NewUnifiedDB().Table(u.TableName())
	db = db.Where("id IN (?)", ids)

	userList := make([]*UserInfoV2, 0)
	err = db.Find(&userList).Error()

	return userList, err
}

// 使用正确的gorm.DB类型
func (u *UserInfoV2) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewUnifiedDB()

	if len(param) > 0 {
		db = param[0].(mysql.DBInterface)
	}
	err = db.Model(&UserInfoV2{}).Where("id IN (?)", ids).Delete(u).Error()
	return
}

// 创建用户 - 使用统一接口
func (u *UserInfoV2) Create(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(u.TableName()).Create(u).Error()
}

// 增加订单数量 - 使用统一接口
func (u *UserInfoV2) AddOrderCount(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["order_count"] = u.OrderCount + 1

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 减少订单数量 - 使用统一接口
func (u *UserInfoV2) SubOrderCount(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["order_count"] = u.OrderCount - 1

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 更新用户信息 - 使用统一接口
func (u *UserInfoV2) UpdateDetails(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(u).Updates(u).Error()
}

// 更新用户风险状态 - 使用统一接口
func (u *UserInfoV2) UpdateRiskState(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["risk_state"] = u.RiskState
	updateMap["risk_reason"] = u.RiskReason
	updateMap["risk_remake"] = u.RiskRemake

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 支持零值更新 - 使用统一接口，移除Session调用
func (u *UserInfoV2) UpdateWithZero(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 直接使用Updates方法，不使用Session
	return db.Model(u).Where("id = ?", u.ID).Updates(u).Error()
}

// 根据邮箱查询用户 - 使用统一接口
func (u *UserInfoV2) GetByEmail(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(u.TableName()).Where("email = ?", u.Email).First(u).Error()
}

// 根据账户查询用户 - 使用统一接口
func (u *UserInfoV2) GetByAccount(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(u.TableName()).Where("account = ?", u.Account).First(u).Error()
}

// 根据ID查询用户 - 使用统一接口
func (u *UserInfoV2) GetByID(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(u.TableName()).Where("id = ?", u.ID).First(u).Error()
}

// 批量获取用户列表 - 使用统一接口
func (u *UserInfoV2) GetListByIds(ids []uint, tx ...mysql.DBInterface) (list []*UserInfoV2, err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	err = db.Table(u.TableName()).Where("id IN (?)", ids).Find(&list).Error()
	return
}

// 获取分页用户列表 - 使用统一接口
func (u *UserInfoV2) GetPageList(page, pageSize int, tx ...mysql.DBInterface) (list []*UserInfoV2, count int64, err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	queryDB := db.Table(u.TableName())

	// 添加查询条件
	if u.RiskState > 0 {
		queryDB = queryDB.Where("risk_state = ?", u.RiskState)
	}
	if len(u.Email) > 0 {
		queryDB = queryDB.Where("email LIKE ?", "%"+u.Email+"%")
	}
	if len(u.Account) > 0 {
		queryDB = queryDB.Where("account LIKE ?", "%"+u.Account+"%")
	}

	// 获取总数
	err = queryDB.Count(&count).Error()
	if err != nil {
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = queryDB.Offset(offset).Limit(pageSize).Order("id DESC").Find(&list).Error()

	return
}

// 更新用户登录信息 - 使用统一接口
func (u *UserInfoV2) UpdateLoginInfo(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["last_login_time"] = time.Now()
	updateMap["login_count"] = u.LoginCount + 1

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 获取黑名单用户列表 - 使用统一接口，引用原始文件中的BLACKRISKSTATE
func (u *UserInfoV2) GetBlacklistUsers(userID []uint, tx ...mysql.DBInterface) (list []*UserInfoV2, err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	err = db.Table(u.TableName()).
		Where("id IN (?) and risk_state = ?", userID, BLACKRISKSTATE).
		Find(&list).Error()
	return
}

// 更新用户统计数据 - 使用统一接口
func (u *UserInfoV2) UpdateStatistics(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["design_count"] = u.DesignCount
	updateMap["sales_count"] = u.SalesCount
	updateMap["total_sales"] = u.TotalSales

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 批量更新用户风险状态 - 使用统一接口
func (u *UserInfoV2) BatchUpdateRiskState(userIds []uint, riskState int, reason string, tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["risk_state"] = riskState
	updateMap["risk_reason"] = reason
	updateMap["updated_at"] = time.Now()

	return db.Model(&UserInfoV2{}).Where("id IN (?)", userIds).Updates(updateMap).Error()
}

// 软删除用户 - 使用统一接口
func (u *UserInfoV2) SoftDelete(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	updateMap := make(map[string]interface{})
	updateMap["deleted_at"] = time.Now()

	return db.Model(u).Where("id = ?", u.ID).Updates(updateMap).Error()
}

// 验证用户邮箱唯一性 - 使用统一接口
func (u *UserInfoV2) IsEmailUnique(tx ...mysql.DBInterface) (bool, error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	var count int64
	err := db.Table(u.TableName()).Where("email = ?", u.Email).Count(&count).Error()
	return count == 0, err
}

// 验证用户账户唯一性 - 使用统一接口
func (u *UserInfoV2) IsAccountUnique(tx ...mysql.DBInterface) (bool, error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	var count int64
	err := db.Table(u.TableName()).Where("account = ?", u.Account).Count(&count).Error()
	return count == 0, err
}
