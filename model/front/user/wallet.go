package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Wallet)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Wallet)(nil))
}

type Wallet struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`              //创建时间
	UpdatedAt time.Time `json:"updated_at"`              //更新时间
	UserId    uint      `json:"user_id" gorm:"not null"` //用户ID
	Account   string    `json:"account"`                 //绑定付款账号
	Balance   int       `json:"balance"`                 //当前余额
}

func (w *Wallet) TableName() string {
	return "wallet"
}

func (w *Wallet) PutWalletBalance(tx *gorm.DB, totalMoney int) error {
	return tx.Model(&Wallet{}).Where("user_id = ?", w.UserId).
		Update("balance", gorm.Expr("balance - ?", totalMoney)).First(w).Error
}

func (w *Wallet) RechageWalletBalance(tx *gorm.DB, totalMoney int) error {
	return tx.Model(&Wallet{}).Where("user_id = ?", w.UserId).
		Update("balance", gorm.Expr("balance + ?", totalMoney)).First(w).Error
}

func (w *Wallet) GetBalance(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Table(w.TableName()).Where("user_id = ?", w.UserId).First(w).Error
	if err != nil {
		if err.Error() == "record not found" {
			err = mysql.NewConn().Table(w.TableName()).Create(w).Error
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 新增
func (w *Wallet) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

// 获取列表
func (w *Wallet) GetList() (List []*UserInfo, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("user_id = ?", w.UserId).Find(&List).Error
	if err != nil {
		return
	}
	return
}

// 查询
func (w *Wallet) Query() (List []*Wallet, err error) {
	db := mysql.NewConn().Table(w.TableName())
	if w.Account != "" {
		db = db.Where("account like ?", "%"+w.Account+"%")
	}
	if w.UserId > 0 {
		db = db.Where("user_id = ?", w.UserId)
	}
	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}
	err = db.Find(&List).Error
	return
}

func (c *Wallet) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Wallet, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *Wallet) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Wallet{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Wallet) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Wallet) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Wallet)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Wallet) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
