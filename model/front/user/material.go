package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Material)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Material)(nil))
}

type Material struct {
	ID         uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	UserID     uint      `json:"user_id"`
	Folder     string    `json:"folder"`                       //文件夹名称
	FolderID   int       `json:"folder_id"`                    //文件夹ID
	ImgUrl     string    `json:"img_url"      gorm:"not null"` //图片url
	PreviewUrl string    `json:"preview_url"`                  //缩略图URL
	ImgName    string    `json:"img_name"     gorm:"not null"` //图片名称
	ImgType    string    `json:"img_type"`                     //图片格式
	ImgSize    int64     `json:"img_size"`                     //图片大小
	Width      int       `json:"width"        gorm:""`         //宽度 单位像素
	Height     int       `json:"height"       gorm:""`         //高度 单位像素
	LastUse    int64     `json:"last_use"`                     //最近使用，记录时间戳，记录节点待定
	Position   int       `json:"position"`                     //该素材在设计中的位置，预留！！！
	HitCount   int       `json:"hit_count" gorm:"default:0"`   // 命中次数
	HitCause   int       `json:"hit_cause" gorm:"default:0"`   // 命中原因 1 命中黑名单、2 重复
}

func (m *Material) TableName() string {
	return "material"
}

// 新增一个素材
func (m *Material) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(m.TableName()).Create(m).Error
}

// 根据user_id 和 文件夹 分页获取列表
func (m *Material) GetListByFolder(page, size int) (list []*Material, count int, err error) {
	db := mysql.NewConn().Model(&Material{}).Where("user_id = ?", m.UserID).Where("folder_id = ?", m.FolderID)

	if m.ImgName != "" {
		db = db.Where("img_name like ?", "%"+m.ImgName+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Order("id desc").Offset((page - 1) * size).Limit(size).Find(&list).Error
	return
}

// 根据用户id 分页获取素材列表
func (m *Material) GetListByUserIDPaging(page, size int) (list []*Material, count int, err error) {
	db := mysql.NewConn().Model(&Material{}).Where("user_id = ?", m.UserID)

	if m.ImgName != "" {
		db = db.Where("img_name like ?", "%"+m.ImgName+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Order("id desc").Offset((page - 1) * size).Limit(size).Find(&list).Error
	return
}

// 根据主键id 更新最近使用时间
func (m *Material) PutLastUse() error {
	return mysql.NewConn().Model(&Material{}).Where("id = ?", m.ID).Update("last_use", m.LastUse).Error
}

func (m *Material) GetLastImg(count int) (list []*Material, err error) {
	err = mysql.NewConn().Table(m.TableName()).Where("user_id = ?", m.UserID).Where("last_use != 0").
		Order("last_use desc").Limit(count).Find(&list).Error
	return
}

func (m *Material) GetCountImg() (count int64, err error) {
	err = mysql.NewConn().Table(m.TableName()).Where("user_id = ?", m.UserID).
		Count(&count).Error
	return
}

// 根据主键id 更新素材所属文件夹
func (m *Material) PutPreview(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&Material{}).Where("id = ?", m.ID).Updates(m).First(m).Error
}

// 根据 主键idList ，批量更新这批素材所属文件夹
func (m *Material) BatchPutFolder(idList []int, folderID int) error {
	return mysql.NewConn().Model(&Material{}).Where("id IN (?)", idList).Update("folder_id", folderID).Error
}

// 根据主键idList，批量删除素材文件
func (m *Material) DeleteByIDList(idList []int, tx ...*gorm.DB) (list []*Material, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Material{}).Where("id IN (?)", idList).Find(&list).Delete(&Material{}).Error
	return
}

// 根据文件夹ID，批量删除文件
func (m *Material) DeleteByFolderID(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Material{}).Where("folder_id = ?", m.FolderID).Delete(&Material{}).Error
	return
}

func (m *Material) BatchList() (list []*Material, err error) {
	err = mysql.NewConn().Table(m.TableName()).Find(&list).Error
	return
}

func (c *Material) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Material, 0)

	err := db.Find(&list).Error

	return list, err
}

func (m *Material) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Material{}).Where("id IN (?)", ids).Delete(m).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Material) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Material) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Material)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Material) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 获取一个用户下的所有素材
func (c *Material) GetListByUserID() (list []*Material, err error) {
	err = mysql.NewConn().Table(c.TableName()).Where("user_id = ?", c.UserID).Find(&list).Error
	return
}

// 获取一个用户下 hit_count > 0 的素材
func (c *Material) GetListByUserIDAndHitCount() (list []*Material, err error) {
	db := mysql.NewConn().Table(c.TableName()).Where("user_id = ?", c.UserID).Where("hit_count > 0")
	if c.HitCause != 0 {
		db = db.Where("hit_cause = ?", c.HitCause)
	}
	err = db.Find(&list).Error
	return
}

type RepetitionMaterialItem struct {
	ImgUrl        string `json:"img_url"`
	ImgName       string `json:"img_name"     gorm:"not null"` //图片名称
	ImgType       string `json:"img_type"`                     //图片格式
	UserID        uint   `json:"user_id"`
	UserRiskState int    `json:"user_risk_state"` // 用户风控等级 根据user_id 连表查询user_info.risk_state
}

// 查询图片格式宽高大小相同的非此用户的素材
func (c *Material) GetListByImgInfo() (list []*RepetitionMaterialItem, err error) {
	db := mysql.NewConn().Table(c.TableName())

	// 连表查询以获取用户风控等级
	db = db.Select("material.img_url, material.img_name, material.img_type, material.user_id, user_info.risk_state as user_risk_state").
		Joins("left join user_info on user_info.id = material.user_id").
		Where("material.user_id != ?", c.UserID).
		Where("material.img_size = ?", c.ImgSize).
		Where("material.width = ?", c.Width).
		Where("material.height = ?", c.Height)

	err = db.Find(&list).Error
	return list, nil
}

type RepetitionMaterialItemGroup struct {
	ImgSize int64  `json:"img_size"`                        // 图片大小
	Width   int    `json:"width"`                           // 宽度 单位像素
	Height  int    `json:"height"`                          // 高度 单位像素
	UserIDS string `json:"user_ids" gorm:"column:user_ids"` // 用户ID 使用,分隔
	IDS     string `json:"ids"      gorm:"column:ids"`      // 主键ID 使用,分割
}

// 查询所有img_size、width、height都相同的素材，按照(img_size、width、height)分组，每组的用户ID使用,分隔，主键ID使用,分隔
func (c *Material) GetListByImgInfoGroupByUser() (list []*RepetitionMaterialItemGroup, err error) {
	db := mysql.NewConn().Table(c.TableName())

	// 构建 SQL 查询
	query := `
        SELECT img_size, width, height,
               CONVERT(GROUP_CONCAT(DISTINCT user_id SEPARATOR ','), CHAR) as user_ids,
               CONVERT(GROUP_CONCAT(DISTINCT id SEPARATOR ','), CHAR) as ids
        FROM material
        GROUP BY img_size, width, height
        HAVING COUNT(*) > 1 AND COUNT(DISTINCT user_id) > 1
    `

	// 执行查询
	result := db.Raw(query).Scan(&list)
	if result.Error != nil {
		return nil, result.Error
	}

	return
}

// UpdateMaterialStatus
func (c *Material) UpdateMaterialStatus(idList []uint) (err error) {
	err = mysql.NewConn().Model(&Material{}).Where("id IN (?)", idList).Where("hit_cause != 1").
		Updates(map[string]interface{}{
			"hit_cause": 2,
			"hit_count": 1,
		}).Error
	return
}
