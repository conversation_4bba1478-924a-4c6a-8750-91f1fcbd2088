package order

import (
	"encoding/json"
	"github.com/jinzhu/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

const (
	StatusPending = 0 // 待处理
	StatusSuc     = 1 // 成功
	StatusFail    = 2 // 失败
)

func init() {
	mysql.RegisterTable((*OrderExportLog)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OrderExportLog)(nil))
}

type OrderExportLog struct {
	ID        int64     `gorm:"primaryKey;autoIncrement;comment:'自增ID'"`
	UserID    int64     `gorm:"column:user_id;default:0;comment:'用户id'"`
	ShopIDs   string    `gorm:"type:varchar(255);default:'';comment:'来源店铺ID'"`
	PayState  string    `gorm:"type:varchar(255);default:'';comment:'支付状态，0其他1已支付'"`
	StartTime time.Time `gorm:"type:datetime;not null;index:idx_time;comment:'订单开始日期'"`
	EndTime   time.Time `gorm:"type:datetime;not null;index:idx_time;comment:'订单结束日期'"`
	Email     string    `gorm:"type:varchar(255);default:'';comment:'接收邮箱'"`
	Status    int       `gorm:"type:tinyint unsigned;default:0;comment:'0正在导出1导出成功2导出失败'"`
	Reason    string    `gorm:"type:varchar(255);default:'';comment:'失败原因'"`
	URL       string    `gorm:"type:varchar(255);default:'';comment:'下载地址'"`
}

func (o *OrderExportLog) TableName() string {
	return "order_export_log"
}

// 这个方法不需要实现
func (o *OrderExportLog) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	return list, count, err
}

func (o *OrderExportLog) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(OrderInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return
}

func (o *OrderExportLog) GetDetailList(ids []uint) (list interface{}, err error) {
	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	list = make([]*OrderInfo, 0)

	err = db.Find(&list).Error

	return list, err
}

func (o *OrderExportLog) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&OrderInfo{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

func (o *OrderExportLog) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(o.TableName()).Create(o).Error
}

func (o *OrderExportLog) Query() (err error) {
	db := mysql.NewConn().Table(o.TableName())

	if o.ID > 0 {
		db = db.Where("id = ?", o.ID)
	}

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	if o.ShopIDs != "" {
		db = db.Where("shop_ids = ?", o.ShopIDs)
	}

	db = db.Where("pay_state = ?", o.PayState)

	if !o.StartTime.IsZero() {
		db = db.Where("start_time = ?", o.StartTime)
	}

	if !o.EndTime.IsZero() {
		db = db.Where("end_time = ?", o.EndTime)
	}

	err = db.First(o).Error
	return
}

func (o *OrderExportLog) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&OrderExportLog{}).Where("id = ?", o.ID).Updates(o).First(o).Error
}

func (o *OrderExportLog) GetPendingData() (list []*OrderExportLog, err error) {
	err = mysql.NewConn().Table(o.TableName()).Where("status = ?", StatusPending).Find(&list).Error
	return
}
