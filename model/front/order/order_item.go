package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/global"
	"zx/zx-consistency/model"
	template "zx/zx-consistency/model/front/template/design"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*OrderItem)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OrderItem)(nil))
}

type OrderItem struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	OrderCode string `json:"order_code"`                // 平台订单号 关联
	Source    string `json:"source"`                    // 商品来源，template|shop
	DesignID  int    `json:"design_id"`                 // 设计模板id，用来展示模特效果图 如果是商店过来的查商店表的设计，如果是样品订单，查模板的id
	Versions  int    `json:"versions" gorm:"default:1"` // 下单时选中设计的版本号
	SkuTag    string `json:"sku_tag"`                   // sku标签 ABCDEFG,手动标记

	BrandLabelID uint `json:"brand_label_id"  gorm:"default:0"` //品牌领标ID  为0是未使用

	SKU         string `json:"sku"`       //平台sku
	ThirdSKU    string `json:"third_sku"` //用户商店对应的sku
	Price       int    `json:"price"`     //美分
	Title       string `json:"title"`     //商品名称
	ColorName   string `json:"color_name"`
	ColorNum    string `json:"color_num"`
	Size        string `json:"size"`
	DesignInfo  string `json:"design_info"  gorm:"type:BLOB;"` //设计信息 素材和展示图  过滤下，只记录设计的面和素材，用来生产工单
	CustomCount int    `json:"custom_count" gorm:"default:0"`  //半自定义图层数量 0代表没有
	EmbCount    int    `json:"emb_count" gorm:"default:0"`     // 刺绣框数量  0代表没有

	ModelImg      string `json:"model_img"  gorm:"type:BLOB;"` // 模特展示图信息
	IndexImg      string `json:"index_img"`                    // 列表首图
	Weight        string `json:"weight"`                       // 面料克重
	RealityWeight string `json:"reality_weight"`               // 真实克重

	ItemCount          int                `json:"item_count"`                            //属于订单第几项
	IsPodSKU           bool               `json:"is_pod_sku"`                            //是否pod平台内sku
	IsIgnore           bool               `json:"is_ignore"`                             //是否忽略  当IsPodSKU为false，此值为true才可继续进行
	InnerLabelIsWhite  *bool              `json:"inner_label_is_white" gorm:"default:0"` // 内领标是否使用白色打印，默认为黑色
	SkuInfo            model.SkuInventory `json:"sku_info"  gorm:"foreignkey:sku;association_foreignkey:sku"`
	ThirdProductID     int64              `json:"third_product_id"`     // shopify 平台商品 id，用于后面绑定
	ThirdProductIDStr  string             `json:"third_product_id_str"` // shopify 平台商品 GraphQL id，用于后面绑定
	FulfillmentService string             `json:"fulfillment_service"`  // 履约方数据，用于从订单子项进行数据绑定时，重新生成订单子项
	OtherSku           string             `json:"other_sku"`            // 用于存储其他平台的sku

	TagDesignID   uint   `json:"tag_design_id" gorm:"default:0"`  // 吊牌设计ID
	TagName       string `json:"tag_name"`                        // 吊牌名称
	TagModel      string `json:"tag_model"     gorm:"type:BLOB;"` // 吊牌款式模特
	TagPrice      int    `json:"tag_price"     gorm:"default:0"`  // 美分
	TagDesign     string `json:"tag_design"    gorm:"type:BLOB;"` // 吊牌设计信息
	HangTagInfoID int64  `json:"hang_tag_info_id"`                // 吊牌款式 id，主键id
	TagVersions   int    `json:"tag_versions"`                    // 吊牌版本号

	PackDesignID uint   `json:"pack_design_id" gorm:"default:0"`  // 包装设计ID
	PackPrice    int    `json:"pack_price"     gorm:"default:0"`  // 美分
	PackDesign   string `json:"pack_design"    gorm:"type:BLOB;"` // 包装设计信息
	PackName     string `json:"pack_name"`                        // 包装名称
	PackModel    string `json:"pack_model"     gorm:"type:BLOB;"` // 包装模特
	PackID       int64  `json:"pack_id"`                          // 包装袋款式 id，主键 id
	PackVersions int    `json:"pack_versions"`                    // 包装版本号

	IsBlank    bool `json:"is_blank"`    // 是否空白模板
	AuditState int  `json:"audit_state"` // 审核状态 1 待审核  2 审核通过  3 审核不通过

	CopyrightRisk int    `json:"copyright_risk"`                // 订单版权风控状态  0 1 正常 2风控
	RiskReason    string `json:"risk_reason" gorm:"type:BLOB;"` // 侵权描述
	DigitizationPrice  int32  `json:"digitization_price" gorm:"default:0"`  // 制版费用，如果不为0，说明在订单中付款了 2025年3月18日17:55:19 zc 新增 记录折扣后的金额
	DigitizationCount  int    `json:"digitization_count"  gorm:"default:0"` // 制版任务数量
	DigitizationList   string `json:"digitization_list"  gorm:"type:BLOB;"` // 制版任务信息
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态
	DigitizationIDList string `json:"digitization_id_list"`                 // 将制版任务ID 放在这里，用于快速筛选

	TagDesignInfo  *template.TagDesign  `json:"tag_design_info"  gorm:"foreignkey:tag_design_id;association_foreignkey:id"`
	PackDesignInfo *template.PackDesign `json:"pack_design_info"   gorm:"foreignkey:pack_design_id;association_foreignkey:id"`
}

// design_info 的子项
type OutDesign struct {
	SurfaceName  string `json:"surface_name"`  //对应可打印面名称
	CanvasName   string `json:"canvas_name"`   //画板名称
	OutputImg    string `json:"output_img"`    //输出素材图
	CanvasWidth  int    `json:"canvas_width"`  // 画板的宽度
	CanvasHeight int    `json:"canvas_height"` // 画板的高度
	RealWidth    int    `json:"real_width"`    //真实宽
	RealHeight   int    `json:"real_height"`   //真实高
	Layers       string `json:"layers"`        //图层信息  直接存储字符串BLOB，前端自行处理
	CustomCount  int    `json:"custom_count"`  //半自定义图层数量 0代表没有

	MaskImage    string  `json:"mask_image"`    //图片URL
	CanvasLeft   int     `json:"canvas_left"`   // 画板相对容器的左边的位置
	CanvasTop    int     `json:"canvas_top"`    // 画板相对容器的左边的位置
	OuterWidth   int     `json:"outer_width"`   //容器宽度
	OuterHeight  int     `json:"outer_height"`  //容器高度
	CanvasZoom   float32 `json:"canvas_zoom"`   //缩放系数
	CanvasRotate int     `json:"canvas_rotate"` //旋转系数
	EmbZoom      float32 `json:"emb_zoom"`      // 绣框缩放系数 type!=2 时忽略该参数

	LightColorSet string `json:"light_color_set"` // 自定义打印设置浅色
	DarkColorSet  string `json:"dark_color_set"`  // 自定义打印设置深色
	//ModelImg    string `json:"model_img"`    //模型展示图
	Type    int    `json:"type"`    // 0/1DTG 2 EMB
	Platens string `json:"platens"` // emb 刺绣的绣框  直接存储字符串BLOB
}

type TagModelInfo struct {
	ID          int64   `json:"id"`
	SurfaceName string  `json:"surface_name"`
	Name        string  `json:"name"`
	MaskImage   string  `json:"mask_image"`
	OuterWidth  float64 `json:"outer_width"`
	OuterHeight float64 `json:"outer_height"`
	MaskColor   string  `json:"mask_color"`
	MaskOpacity float32 `json:"mask_opacity"`
	MappingTop  bool    `json:"mapping_top"`
	Mapping     string  `json:"mapping"`
}

type TagModelMappingInfo struct {
	CanvasName    string  `json:"canvas_name"`
	VariantDeform float64 `json:"variant_deform"`
	VariantFactor float64 `json:"variant_factor"`
	Height        float64 `json:"height"`
	Width         float64 `json:"width"`
	Rotate        float64 `json:"rotate"`
	Clip          string  `json:"clip"`
	X             float64 `json:"x"`
	Y             float64 `json:"y"`
}

func (o *OrderItem) TableName() string {
	return "order_item"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (o *OrderItem) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (o *OrderItem) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(OrderItem)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (o *OrderItem) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(o.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error

	return
}

func (o *OrderItem) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*OrderItem, 0)

	err := db.Find(&list).Error

	return list, err
}

func (o *OrderItem) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&OrderItem{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

// 新建订单子项
func (o *OrderItem) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(o.TableName()).Create(o).Error
}

func (o *OrderItem) GetItemByOrderIsExist(orderCode []string) (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(o.TableName()).
		Where("source = ?", "template").
		Where("design_id = ?", o.DesignID).
		Where("order_code IN (?)", orderCode).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 0, nil
}

func (o *OrderItem) GetItemByShopOrderIsExist(orderCode []string) (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(o.TableName()).
		Where("source = ?", "shop").
		Where("design_id = ?", o.DesignID).
		Where("order_code IN (?)", orderCode).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 0, nil
}

// 查询订单子项数据
func (o *OrderItem) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(o.TableName())
	if o.ID > 0 {
		db = db.Where("id = ?", o.ID)
	}
	return db.First(o).Error
}

type ItemInfo struct {
	OrderCode      string `json:"order_code"`
	Total          int64  `json:"total"`
	TagPriceTotal  int64  `json:"tag_price_total"`
	PackPriceTotal int64  `json:"pack_price_total"`
}

// 根据订单号查询对应的订单子项数量
func (o *OrderItem) GetItemCountByOrderCodes(orderCodes []string) (itemInfos []*ItemInfo, err error) {
	itemInfos = make([]*ItemInfo, 0)
	db := mysql.NewConn().Table(o.TableName())

	err = db.Select("order_code, COUNT(created_at) as total,SUM(tag_price) as tag_price_total,SUM(pack_price) as pack_price_total").
		Where("order_code IN (?)", orderCodes).Group("order_code").Scan(&itemInfos).Error
	if err != nil {
		return nil, err
	}

	return
}

// 根据订单获取所有数据
func (o *OrderItem) GetListByOrderCodes(orderCodes []string) (list []*OrderItem, err error) {
	db := mysql.NewConn().Table(o.TableName())

	err = db.Where("order_code IN (?)", orderCodes).Find(&list).Error
	if err != nil {
		return nil, err
	}

	return
}

// 获取一个订单下订单子项的个数
func (o *OrderItem) GetPodListByOrderCode() (list []*OrderItem, err error) {
	db := mysql.NewConn().Table(o.TableName()).Where("order_code = ?", o.OrderCode).Where("source != ?", "no")
	err = db.Find(&list).Error
	return
}

type OrderItemSku struct {
	OrderCode string `json:"order_code"`
	Sku       string `json:"sku"`
	SkuCount  int    `json:"sku_count"`
}

func (o *OrderItem) GetOrderSkus(orderCodes []string) (list []*OrderItemSku, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("order_code, sku, COUNT(sku) as sku_count").Where("order_code IN (?)", orderCodes).Group("order_code,sku")
	err = db.Find(&list).Error

	return
}

func (o *OrderItem) GetListByIds(ids []uint) (list []*OrderItem, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("id IN (?)", ids)
	err = db.Find(&list).Error

	return
}

// 忽略一个非我们平台的商品
func (o *OrderItem) PutIgnore(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Model(&OrderItem{}).Where("id = ?", o.ID).
		Update("is_ignore", true).First(o).Error
	return err
}

func (o *OrderItem) PutIgnoreByThirdSKU(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Model(&OrderItem{}).Where("source = ?", "no").Where("third_sku = ?", o.ThirdSKU).
		Update("is_ignore", true).First(o).Error

	return err
}

func (o *OrderItem) GetItemIgnoreCount() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(o.TableName()).
		Where("is_pod_sku = ?", false).
		Where("is_ignore = ?", false).
		Where("order_code = ?", o.OrderCode).Count(&count)
	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	log.Info(count)
	return count == 1, nil

}

func (o *OrderItem) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderItem{}).Where("id = ?", o.ID).Updates(o).First(o).Error
}

type OrderItemSimple struct {
	ID        uint   `gorm:"PRIMARY_KEY" json:"id"`
	OrderCode string `json:"order_code"` //平台订单号 关联
}

func (o *OrderItem) GetAllThirdProductIdLessOne() (list []*OrderItemSimple, err error) {
	db := mysql.NewConn().Table(o.TableName())

	db = db.Select("id,order_code").Where("source = 'shop'").Where("third_product_id < 1").Where("order_code LIKE 'T%'")

	err = db.Find(&list).Error
	return
}

func (o *OrderItem) GetItemInfo() error {
	return mysql.NewConn().Table(o.TableName()).Where("id = ?", o.ID).Preload("SkuInfo").First(o).Error
}

func (o *OrderItem) DeleteItem(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Table(o.TableName()).Where("order_code = ?", o.OrderCode).Delete(&OrderItem{}).Error
}

type FinanceList struct {
	Total      int    `json:"total"`
	SKU        string `json:"sku"`   //平台sku
	Price      int    `json:"price"` //美分
	ColorName  string `json:"color_name"`
	Size       string `json:"size"`
	TotalMoney int    `json:"total_money"`
	TagPrice   int    `json:"tag_price"`
	PackPrice  int    `json:"pack_price"`
	OrderCode  string `json:"order_code"`
}

func (o *OrderItem) GetFinanceByOrderCode(orderCodeList []string) (list []*FinanceList, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("count(*) as total,order_code,sku,price,color_name,size,sum(price) as total_money,sum(tag_price) as tag_price,sum(pack_price) as pack_price")
	db = db.Where("order_code IN (?)", orderCodeList)
	err = db.Group("sku").Scan(&list).Error

	return
}

func (o *OrderItem) GetListByOrderCode(orderCodeList []string) (list []*FinanceList, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("count(*) as total,sku,price,color_name,size,sum(price) as total_money,sum(tag_price) as tag_price,sum(pack_price) as pack_price")
	db = db.Where("order_code IN (?)", orderCodeList).Where("price > 0")
	err = db.Group("sku").Scan(&list).Error

	return
}

// UpdateAuditState 根据DeignID、Source、Versions 更新符合条件的订单子项的审核状态
// 再查找这些订单中所有子项都通过的订单号 返回
func (o *OrderItem) UpdateAuditState() (orderCodes []string, err error) {
	db := mysql.NewConn().Table(o.TableName())

	// 执行更新操作
	err = db.Model(&OrderItem{}).Where("design_id = ?", o.DesignID).
		Where("source = ?", o.Source).
		Where("versions = ?", o.Versions).
		Update("audit_state", o.AuditState).Error
	if err != nil {
		return
	}

	// 获取不重复的订单号
	err = db.Model(&OrderItem{}).Where("design_id = ?", o.DesignID).
		Where("source = ?", o.Source).
		Where("versions = ?", o.Versions).
		Pluck("DISTINCT order_code", &orderCodes).Error
	if err != nil {
		return
	}

	return
}

// 查找所有子项都审核通过的订单号
func (o *OrderItem) GetOrderCodeByAuditStateYes(orderList []string) (orderCodes []string, err error) {
	db := mysql.NewConn().Table(o.TableName())

	err = db.Model(&OrderItem{}).Where("order_code IN (?)", orderList).Where("emb_count > ?", 0).
		Group("order_code").
		Having("SUM(CASE WHEN audit_state != ? THEN 1 ELSE 0 END) = 0", 2).
		Pluck("order_code", &orderCodes).Error
	return
}

// 根据订单号获取订单子项详情
func (o *OrderItem) GetListDetailByOrderCode(codes []string) (list []*FinanceList, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("order_code IN (?)", codes)

	err = db.Find(&list).Error
	return
}

type WorkSkuItems struct {
	Location string `json:"location"`
	Count    int    `json:"count"`
}

// GetUnPaidCountsByLocationList
func (o *OrderItem) GetUnPaidCountsByLocationList(locationList []string) (list map[string]int, err error) {
	// 查询未支付订单中订单子项的数量
	//   - 订单的创建时间，晚于当前日期往前推 10 天的 00:00:00
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	tenDaysAgo := today.AddDate(0, 0, -9)
	respList := make([]*WorkSkuItems, 0)

	cTime := tenDaysAgo.Unix()

	db := mysql.NewConn().Table("order_item as oi").
		Joins("JOIN order_info as o ON oi.order_code = o.order_code").
		Where("o.created_at >= FROM_UNIXTIME(?)", cTime).Where("o.order_state = ?", 1)

	// 使用 LIKE 结合 Location 查询
	if len(locationList) > 0 {
		likeConditions := make([]string, 0, len(locationList))
		for _, location := range locationList {
			likeConditions = append(likeConditions, fmt.Sprintf("oi.sku LIKE '%%%s'", location))
		}
		db = db.Where(strings.Join(likeConditions, " OR "))
	}

	// 查询并按 Location 分组统计数量
	err = db.Select("SUBSTRING(oi.sku, -6) as location, COUNT(*) as count").
		Group("location").
		Find(&respList).Error
	if err != nil {
		return nil, err
	}

	// 将结果转换为 map
	list = make(map[string]int)
	for _, wo := range respList {
		list[wo.Location] = wo.Count
	}

	return list, err
}

// GetPaidCountsByLocationList
func (o *OrderItem) GetPaidCountsByLocationList(locationList []string) (list map[string]int, err error) {
	//	获取支付时间在今日的 数量 （pay_time）
	// 获取今天 00:00:00 的时间
	// 获取当前时间
	now := time.Now()

	// 今日 0:00:00
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartTimestamp := todayStart.Unix()

	// 今日 23:59:59
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	todayEndTimestamp := todayEnd.Unix()

	respList := make([]*WorkSkuItems, 0)

	db := mysql.NewConn().Table("order_item as oi").
		Joins("JOIN order_info as o ON oi.order_code = o.order_code").
		Where("o.pay_time >= ?", todayStartTimestamp).Where("o.pay_time <= ?", todayEndTimestamp).Where("o.order_state != ?", 12)

	// 使用 LIKE 结合 Location 查询
	if len(locationList) > 0 {
		likeConditions := make([]string, 0, len(locationList))
		for _, location := range locationList {
			likeConditions = append(likeConditions, fmt.Sprintf("oi.sku LIKE '%%%s'", location))
		}
		db = db.Where(strings.Join(likeConditions, " OR "))
	}

	// 查询并按 Location 分组统计数量
	err = db.Select("SUBSTRING(oi.sku, -6) as location, COUNT(*) as count").
		Group("location").
		Find(&respList).Error
	if err != nil {
		return nil, err
	}

	// 将结果转换为 map
	list = make(map[string]int)
	for _, wo := range respList {
		list[wo.Location] = wo.Count
	}

	return list, err

}

// 查询结果结构
type QueryResult struct {
	Week      string `json:"week"`
	OrderType string `json:"order_type"`
	Count     int    `json:"count"`
}

func (o *OrderItem) TrendDataBySKU(sku string, startDate, endDate int64) (list []QueryResult, err error) {
	db := mysql.NewConnV2().Table("order_info as o").
		Joins("JOIN order_item as oi ON o.order_code = oi.order_code").
		Select(`
			DATE(FROM_UNIXTIME(o.pay_time)) as week,
			o.order_type,
			COUNT(*) as count
		`).
		Where("RIGHT(oi.sku, 6) = ?", sku).
		Where("o.pay_time > 0 AND o.order_state != ?", 12).
		Where("o.pay_time BETWEEN ? AND ?", startDate, endDate).
		Group("week, o.order_type").Order("week ASC")

	// 执行查询
	if err := db.Find(&list).Error; err != nil {
		log.Error("数据库查询失败:", err)
		return nil, err
	}

	return
}

func (o *OrderItem) TrendDataByColorID(colorID string, startDate, endDate int64) (list []QueryResult, err error) {
	db := mysql.NewConnV2().Table("order_info as o").
		Joins("JOIN order_item as oi ON o.order_code = oi.order_code").
		Select(`
            DATE(FROM_UNIXTIME(o.pay_time)) as week,
            o.order_type,
            COUNT(*) as count
        `).
		Where("SUBSTRING(oi.sku, 8, 3) = ?", colorID).
		Where("o.pay_time > 0 AND o.order_state != ?", 12).
		Where("o.pay_time BETWEEN ? AND ?", startDate, endDate).
		Group("week, o.order_type").Order("week ASC")

	// Execute the query
	if err := db.Find(&list).Error; err != nil {
		log.Error("Database query failed:", err)
		return nil, err
	}

	return
}


// OrderItem 订单商品项
type InvoiceItem struct {
	OrderCode string `json:"order_code"`                      // 平台订单号
	Title     string `json:"title"`                           // 商品名称
	Size      string `json:"size"`                            // 尺码
	ColorName string `json:"color_name"`                      // 颜色
	Price     int    `json:"price"`                           // 美分, 单价
	TagPrice  int    `json:"tag_price"     gorm:"default:0"`  // 美分
	PackPrice int    `json:"pack_price"     gorm:"default:0"` // 美分
	SKU       string `json:"sku"`                             //平台sku
	Source    string `json:"source"`                          // 商品来源，template|shop
	DesignID  int    `json:"design_id"`                       // 设计模板id，用来展示模特效果图 如果是商店过来的查商店表的设计，如果是样品订单，查模板的id
	Versions  int    `json:"versions" gorm:"default:1"`       // 下单时选中设计的版本号
	IsBlank   bool   `json:"is_blank"`                        // 是否空白模板
}

func (o *OrderItem) QueryInvoiceData(orderCodes []string, param ...interface{}) (list []*InvoiceItem, err error) {

	fields := global.GetJSONFieldNames(InvoiceItem{}, "")

	db := mysql.NewConn().Table(o.TableName()).Where("order_code IN (?)", orderCodes)

	if len(fields) < 1 {
		return nil, errors.New("没有查询字段")
	}

	// 查询字段
	db = db.Select(fields)

	// 预加载关联数据
	for _, one := range param {
		db = db.Preload(one.(string))
	}

	err = db.Find(&list).Error
	return
}

// OrderItem 订单商品项
type InvoiceGroupItem struct {
	OrderCode string  `json:"order_code"`                           // 平台订单号
	Title     string  `json:"title"`                                // 商品名称
	Size      string  `json:"size"`                                 // 尺码
	ColorName string  `json:"color_name"`                           // 颜色
	Quantity  int     `json:"quantity" zxdb:"COUNT(*) as quantity"` // 数量
	Price     int     `json:"price"`                                // 美分, 单价
	Total     float64 `json:"total" zxdb:"SUM(price) as total"`     // 小计金额
	TagPrice  int     `json:"tag_price"     gorm:"default:0"`       // 美分
	PackPrice int     `json:"pack_price"     gorm:"default:0"`      // 美分
	SKU       string  `json:"sku"`                                  //平台sku
	Source    string  `json:"source"`                               // 商品来源，template|shop
	DesignID  int     `json:"design_id"`                            // 设计模板id，用来展示模特效果图 如果是商店过来的查商店表的设计，如果是样品订单，查模板的id
	Versions  int     `json:"versions" gorm:"default:1"`            // 下单时选中设计的版本号
}

func (o *OrderItem) QueryInvoiceGroupData(orderCodes []string, param ...interface{}) (list []*InvoiceGroupItem, err error) {

	fields := global.GetJSONFieldNames(InvoiceItem{}, "")

	db := mysql.NewConn().Table(o.TableName()).Where("order_code IN (?)", orderCodes)

	if len(fields) < 1 {
		return nil, errors.New("没有查询字段")
	}

	// 查询字段
	db = db.Select(fields)

	// 预加载关联数据
	for _, one := range param {
		db = db.Preload(one.(string))
	}

	err = db.Group("size,color_name,source,design_id,versions,tag_price,pack_price").Find(&list).Error
	return
}

// OrderItem 订单商品项
type InvoiceBrandItem struct {
	TagPrice       int `json:"tag_price"     gorm:"default:0"`                              // 美分
	TagPriceTotal  int `json:"tag_price_total" zxdb:"SUM(tag_price) as tag_price_total"`    // 美分
	PackPrice      int `json:"pack_price"     gorm:"default:0"`                             // 美分
	PackPriceTotal int `json:"pack_price_total" zxdb:"SUM(pack_price) as pack_price_total"` // 美分
}

func (o *OrderItem) QueryInvoiceBrandData(orderCodes []string, param ...interface{}) (list []*InvoiceItem, err error) {

	fileds := global.GetJSONFieldNames(InvoiceItem{}, "")

	db := mysql.NewConn().Table(o.TableName()).Where("order_code IN (?)", orderCodes)

	// 筛选包装吊牌的数据
	db = db.Where("tag_price > 0 OR pack_price > 0")

	if len(fileds) < 1 {
		return nil, errors.New("没有查询字段")
	}

	// 查询字段
	db = db.Select(fileds)

	// 预加载关联数据
	for _, one := range param {
		db = db.Preload(one.(string))
	}

	err = db.Group("tag_price, pack_price").Find(&list).Error
	return
}

// map 更新
func (o *OrderItem) UpdateMap(maps map[string]interface{}) error {
	db := mysql.NewConn()
	return db.Model(&OrderItem{}).Where("id = ?", o.ID).Updates(maps).Error
}

// 根据map 更新内容
func (o *OrderItem) UpdateByMap(data map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderItem{}).Where("id = ?", o.ID).Updates(data).Error
}

// 根据map 更新内容 筛选条件为 designID 和 versions 还有 source
func (o *OrderItem) UpdateByMapWithDesignID(data map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&OrderItem{}).Where("design_id = ?", o.DesignID).
		Where("versions = ?", o.Versions).
		Where("source = ?", o.Source).
		Updates(data).Error
}

type OrderItemListItem struct {
	ID uint `gorm:"PRIMARY_KEY" json:"id"`

	OrderCode string `json:"order_code"`                // 平台订单号 关联
	Source    string `json:"source"`                    // 商品来源，template|shop
	DesignID  int    `json:"design_id"`                 // 设计模板id，用来展示模特效果图 如果是商店过来的查商店表的设计，如果是样品订单，查模板的id
	Versions  int    `json:"versions" gorm:"default:1"` // 下单时选中设计的版本号

	SKU        string `json:"sku"`                            //平台sku
	Price      int    `json:"price"`                          //美分
	DesignInfo string `json:"design_info"  gorm:"type:BLOB;"` //设计信息 素材和展示图  过滤下，只记录设计的面和素材，用来生产工单

	DigitizationPrice  int32  `json:"digitization_price" gorm:"default:0"`  // 制版费用，如果不为0，说明在订单中付款了 2025年3月18日17:55:19 zc 新增 记录折扣后的金额
	DigitizationCount  int    `json:"digitization_count"  gorm:"default:0"` // 制版任务数量
	DigitizationList   string `json:"digitization_list"  gorm:"type:BLOB;"` // 制版任务信息
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态
	DigitizationIDList string `json:"digitization_id_list"`                 // 将制版任务ID 放在这里，用于快速筛选
}

// 根据designID 和 versions 还有 source，筛选list
func (o *OrderItem) GetListByDesignID() (list []*OrderItemListItem, err error) {
	db := mysql.NewConn().Table(o.TableName())

	err = db.Where("design_id = ?", o.DesignID).
		Where("versions = ?", o.Versions).
		Where("source = ?", o.Source).Find(&list).Error

	if err != nil {
		return nil, err
	}

	return
}

