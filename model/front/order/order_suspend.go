package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*SuspendOrder)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*SuspendOrder)(nil))
}

// 暂停方式  work_order.suspend_state
const (
	OrderSuspendStateDelivery = 100 // 发货暂停
	OrderSuspendStateAll      = 2   // 全部暂停
)

var OrderSuspendStateDescMap = map[int]string{
	OrderSuspendStateDelivery: "发货暂停",
	OrderSuspendStateAll:      "全部暂停",
}

// 全部发货暂停原因  对应 work_order.suspend_reason  suspend_state = 2 时
const (
	AllSuspendReasonOrderCancel = 1  // 订单可能取消
	AllSuspendReasonDesign      = 2  // 设计问题
	AllSuspendReasonsuspicious  = 3  // 顾客身份存疑
	AllSuspendReasonOther       = 10 // 其他
)

var AllSuspendReasonDescMap = map[int]string{
	AllSuspendReasonOrderCancel: "订单可能取消",
	AllSuspendReasonDesign:      "设计问题",
	AllSuspendReasonsuspicious:  "顾客身份存疑",
	AllSuspendReasonOther:       "其他",
}

// 发货暂停原因   对应 work_order.suspend_reason  suspend_state = 100 时
const (
	DeliverySuspendReasonChangeAdress = 1  // 需要修改地址
	DeliverySuspendReasonOther        = 10 // 其他
)

var DeliverySuspendReasonDescMap = map[int]string{
	DeliverySuspendReasonChangeAdress: "需要修改地址",
	DeliverySuspendReasonOther:        "其他",
}

// SuspendOrder
type SuspendOrder struct {
	ID              uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	OrderCode       string    `json:"order_code"`                      // 订单号
	UserId          uint      `json:"user_id"`                         // 用户 id
	OrderTotalMoney int32     `json:"order_total_money"`               // 订单总金额，包括运费及税费等
	PayTime         int64     `json:"pay_time"`                        // 支付时间
	OrderState      int       `json:"order_state" gorm:"default:0"`    // 订单状态，！！！注意，这里的状态是暂停订单时的状态，用于优化筛选
	SuspendState    int       `json:"suspend_state" gorm:"default:0"`  // 暂停方式，2：全部暂停，100：发货暂停
	SuspendReason   int       `json:"suspend_reason" gorm:"default:0"` // 暂停原因，详见对应 全部发货暂停原因 和 发货暂停原因
	ResumeTime      int64     `json:"resume_time"  gorm:"default:0"`   // 订单恢复时间
	Remark          string    `json:"remark" gorm:"type:BLOB;"`        // 备注
}

// TableName 表名
func (s *SuspendOrder) TableName() string {
	return "suspend_order"
}

func (s *SuspendOrder) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(s.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*SuspendOrder, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *SuspendOrder) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&SuspendOrder{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (s *SuspendOrder) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(s.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", s.ID).Updates(s).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", s.ID).Update("updated_at", s.UpdatedAt).Error

	return
}

func (s *SuspendOrder) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(SuspendOrder)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (s *SuspendOrder) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(s.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 新建一个
func (s *SuspendOrder) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 查询
func (s *SuspendOrder) Query(hasResumedOrder bool, tx ...*gorm.DB) error {

	db := mysql.NewConn().Table(s.TableName())

	if s.ID > 0 {
		db = db.Where("id = ?")
	}

	if len(s.OrderCode) > 0 {
		db = db.Where("order_code = ?", s.OrderCode)
	}

	// 是否查询已经恢复的暂停订单记录，一般为 false
	if !hasResumedOrder {
		db = db.Where("resume_time < 10")
	}

	return db.First(s).Error
}

// 更新
func (s *SuspendOrder) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&SuspendOrder{})

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}

	if len(s.OrderCode) > 0 {
		db = db.Where("order_code = ?", s.OrderCode)
	}

	// 只更新没有恢复生产的
	db = db.Where("resume_time < 10")

	err = db.Updates(s).Error

	if err != nil {
		return err
	}

	return
}

// 根据map 更新
func (s *SuspendOrder) UpdateWithMap(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&SuspendOrder{})

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}

	if len(s.OrderCode) > 0 {
		db = db.Where("order_code = ?", s.OrderCode)
	}

	err = db.Updates(data).Error

	return
}

// 获取用户暂停订单数量
func (s *SuspendOrder) QueryCountByUserId() (total int64, err error) {
	db := mysql.NewConn().Table(s.TableName())
	err = db.Where("user_id = ?", s.UserId).
		Where("resume_time < 10").
		Count(&total).Error
	return total, err
}

// 查询列表
func (s *SuspendOrder) QueryList(pn, ps int, createdAtS, createdAtE int64, suspendReasons []int, orderState []string) (list []*SuspendOrder, total int64, err error) {

	db := mysql.NewConn().Table(s.TableName())

	if s.ID > 0 {
		db = db.Where("id = ?", s.ID)
	}

	if len(s.OrderCode) > 0 {
		db = db.Where("order_code LIKE ?", "%"+s.OrderCode+"%")
	}

	// 这里的订单状态是暂停时的订单状态，所以筛选时必须要大于等于暂停时的订单状态
	if s.OrderState > 0 {
		db = db.Where("order_state >= ?", s.OrderState)
	} else if len(orderState) > 0 {
		db = db.Where("order_state in (?)", orderState)
	}

	if s.SuspendState > 0 {
		db = db.Where("suspend_state = ?", s.SuspendState)
	}

	//if s.SuspendReason > 0 {
	//	db = db.Where("suspend_reason = ?", s.SuspendReason)
	//}
	if len(suspendReasons) > 0 {
		db = db.Where("suspend_reason IN (?)", suspendReasons)
	}

	if s.UserId > 0 {
		db = db.Where("user_id = ?", s.UserId)
	}

	// 根据暂停订单时间范围筛选
	if createdAtS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createdAtS)
	}
	if createdAtE > 0 {
		db = db.Where("created_at < FROM_UNIXTIME(?)", createdAtE)
	}

	// 不筛选已经恢复生产的
	db = db.Where("resume_time < 10")

	err = db.Count(&total).Error
	if err != nil {
		log.Error(err)
		return
	}

	// 如果不根据订单状态进行筛选的话，就可以进行分页了
	if s.OrderState < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Order("id asc").Find(&list).Error
	return
}

func (s *SuspendOrder) GetList() (list []*SuspendOrder, err error) {

	db := mysql.NewConn().Table(s.TableName())

	if s.ResumeTime > 0 {
		db = db.Where("resume_time < ?", s.ResumeTime)
	}

	err = db.Order("id asc").Find(&list).Error

	return
}
