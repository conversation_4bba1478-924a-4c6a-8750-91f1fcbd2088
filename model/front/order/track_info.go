package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

var StatusCn = map[string]string{
	"": "",
}

func init() {
	mysql.RegisterTable((*TrackInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*TrackInfo)(nil))
}

const (
	LogisticsProgressUnprocessed = 1   // 物流进度，待运输方操作
	LogisticsProgressInTransit   = 100 // 物流进度，运输中
	LogisticsProgressInDelivery  = 200 // 物流进度，已签收
	LogisticsProgressAbnormal    = 300 // 物流进度，单号异常

	LogisticsStateNormal        = 1 // 物流状态，正常
	LogisticsStateTimeout       = 2 // 物流状态，超时
	LogisticsStateSevereTimeout = 3 // 物流状态，严重超时
	LogisticsStateAbnormal      = 4 // 物流状态，异常

	ProcessStateNotProcess = 1 // 订单处理，未处理
	ProcessStateDealing    = 2 // 订单处理，处理中
	ProcessStateComplete   = 3 // 订单处理，处理完成
)

// 订单物流详细信息
type TrackInfo struct {
	ID             uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	UserID         uint      `json:"user_id"`
	OrderCode      string    `json:"order_code"`                                     // 订单号  同物流信息的tag
	OrderIndex     int       `json:"order_index"`                                    // 代表订单的第几个运单，通常为1，拆包的会有1/2/3/4/5之类的，只有1的追踪状态会更改订单状态
	TrackState     int       `json:"track_state"`                                    // 调用接口的状态   1成功、2失败
	TrackErrMsg    string    `json:"track_err_msg"`                                  // 调用接口报错的信息
	RefNo          string    `json:"ref_no"     gorm:"unique;not null"`              // 运单号码
	OldRefNo       string    `json:"old_ref_no" gorm:"type:BLOB;" gorm:"default:[]"` // 旧的运输单号和运输方 json
	Event          string    `json:"event"`                                          // 通知类型 状态
	Param          string    `json:"param"`                                          // 物流单号附加参数，请参考【注册物流单号】入参说明
	Carrier        string    `json:"carrier"`                                        // 运输商
	CarrierCode    int32     `json:"carrier_code"`                                   // 运输商代码
	Status         string    `json:"status"`                                         // 物流主状态。
	SubStatus      string    `json:"sub_status"`                                     // 包裹子状态.
	SubStatusDescr string    `json:"sub_status_descr"`                               // 状态描述。

	Milestone           string `json:"milestone" gorm:"type:BLOB;"` // 里程碑 json 序列化
	DaysAfterOrder      int    `json:"days_after_order"`            //从第一条事件到 成功签收 事件的间隔天数，累加计数。
	DaysOfTransit       int    `json:"days_of_transit"`             // 运输时效：包裹运输的天数，按以下优先级进行计算：
	DaysOfTransitDone   int    `json:"days_of_transit_done"`        // 妥投时效：与 days_of_transit 计算方式一样，在没有 成功签收 状态之前，此项始终为 0，不累加计数。
	DaysAfterLastUpdate int    `json:"days_after_last_update"`      // 信息无更新天数 |从最后一条事件至今没有更新的间隔天数，累加计数；
	WeightKg            string `json:"weight_kg"`                   // 把原始重量信息（ weight_raw） 转换为公斤。
	MiscInfo            string `json:"misc_info" gorm:"type:BLOB;"` // 包裹附属信息节点。（重量 体积、尺寸、件数、风险信息等）
	From                string `json:"from"`                        // 预计投递最早时间（ISO 格式)
	To                  string `json:"to"`                          // 预计投递最晚时间（ISO 格式）
	Tracking            string `json:"tracking" gorm:"type:BLOB;"`  // 物流信息节点。

	MyLogisticsChannel string `json:"my_logistics_channel"`                 // pod 平台的物流渠道
	LogisticsFees      int    `json:"logistics_fees"`                       // 物流费用，单位美分
	FirstStateChange   int64  `json:"first_state_change"`                   // 第一次状态更新，时间戳
	LatestStateChange  int64  `json:"latest_state_change" gorm:"default:0"` // 最近一次状态更新，时间戳
	Remark             int    `json:"remark"  gorm:"default:1"`             // 标注
	RemarkDetail       string `json:"remark_detail" gorm:"type:BLOB;"`      // 标注详细信息
	CountryCode        string `json:"country_code"`                         // 国家
	Progress           int    `json:"progress"`                             // 物流进度
	State              int    `json:"state" gorm:"default:1"`               // 物流状态，目前只在 待物流方操作 的状态下有效
	DeliveryTime       int64  `json:"delivery_time"`                        // 妥投时间

	TrackInfo string `json:"track_info" gorm:"type:BLOB;"` // 物流信息主结构节点。完整的物流信息
}

func (t *TrackInfo) TableName() string {
	return "track_info"
}

// 新建物流追踪
func (t *TrackInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(t.TableName()).Create(t).Error
}

func (t *TrackInfo) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&TrackInfo{}).Where("id = ?", t.ID).Updates(t).First(t).Error
}

func (t *TrackInfo) UpdateBy(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&TrackInfo{})
	if len(t.RefNo) > 0 {
		db = db.Where("ref_no = ?", t.RefNo)
	}
	return db.Updates(t).First(t).Error
}

// 当值字段为空时也更新为空
func (t *TrackInfo) UpdateByID(update map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&TrackInfo{})

	return db.Where("id = ?", t.ID).Updates(update).First(t).Error
}

// 查询订单子项数据
func (t *TrackInfo) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(t.TableName())
	if t.RefNo != "" {
		db = db.Where("ref_no = ?", t.RefNo)
	}
	if t.OrderCode != "" {
		db = db.Where("order_code = ?", t.OrderCode)
	}
	if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	}
	return db.First(t).Error
}

func (t *TrackInfo) IsExist() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(t.TableName()).
		Where("ref_no = ?", t.RefNo).
		Where("order_code = ?", t.OrderCode).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil
}

// 获取列表
func (t *TrackInfo) QueryList(pn, ps int, createTimeS, createTimeE int64, logisticsState []int, hasHistory bool, tx ...*gorm.DB) (list []*TrackInfo, count int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(t.TableName())

	if createTimeS > 0 {
		//db = db.Where("created_at >= FROM_UNIXTIME(?)", createTimeS)
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createTimeS)
	}
	if createTimeE > 0 {
		//db = db.Where("created_at <= FROM_UNIXTIME(?)", createTimeE)
		db = db.Where("created_at <= FROM_UNIXTIME(?)", createTimeE)
	}

	if t.RefNo != "" {
		//db = db.Where("ref_no LIKE ? OR old_ref_no LIKE ?", "%"+t.RefNo+"%", "%"+t.RefNo+"%")
		if hasHistory {
			db = db.Where("ref_no = ? OR old_ref_no LIKE ?", t.RefNo, "%"+t.RefNo+"%")
		} else {
			db = db.Where("ref_no = ?", t.RefNo)
		}
	}

	if t.Carrier != "" {
		db = db.Where("carrier LIKE ?", "%"+t.Carrier+"%")
	}

	if t.UserID > 0 {
		db = db.Where("user_id = ?", t.UserID)
	}
	if t.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+t.OrderCode+"%")
	}
	if len(t.CountryCode) > 0 {
		db = db.Where("country_code = ?", t.CountryCode)
	}
	if len(t.MyLogisticsChannel) > 0 {
		db = db.Where("my_logistics_channel = ?", t.MyLogisticsChannel)
	}
	if t.Remark > 0 {
		db = db.Where("remark = ?", t.Remark)
	}

	// 物流进度
	if t.Progress > 0 {
		db = db.Where("progress = ?", t.Progress)
	}

	// 物流状态筛选，物流进度 的确定标准 在不同的物流进度下是不同的
	sqlArr := make([]string, 0)
	for _, oneLogisticState := range logisticsState {
		oneSqlTmp := ""
		switch oneLogisticState {
		case LogisticsStateNormal:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) > %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND (latest_state_change > %d) AND (first_state_change > %d)", LogisticsProgressInTransit,
					time.Now().Unix()-7*24*60*60, time.Now().Unix()-15*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) > %d AND state != %d) OR (progress = %d AND (latest_state_change > %d) AND (first_state_change > %d)))",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-7*24*60*60, time.Now().Unix()-15*24*60*60)
			}
		case LogisticsStateTimeout:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) <= %d AND unix_timestamp(created_at) > %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((((latest_state_change > %d) AND (latest_state_change <= %d)) AND (first_state_change > %d)) OR ((first_state_change > %d) AND (first_state_change <= %d) AND latest_state_change > %d ) )", LogisticsProgressInTransit,
					time.Now().Unix()-10*24*60*60, time.Now().Unix()-7*24*60*60, time.Now().Unix()-20*24*60*60,
					time.Now().Unix()-20*24*60*60, time.Now().Unix()-15*24*60*60, time.Now().Unix()-10*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) <= %d AND unix_timestamp(created_at) > %d AND state != %d) OR (progress = %d AND ((((latest_state_change > %d) AND (latest_state_change <= %d)) AND (first_state_change > %d)) OR ((first_state_change > %d) AND (first_state_change <= %d) AND latest_state_change > %d ) ))",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-10*24*60*60, time.Now().Unix()-7*24*60*60, time.Now().Unix()-20*24*60*60, time.Now().Unix()-20*24*60*60, time.Now().Unix()-15*24*60*60, time.Now().Unix()-10*24*60*60)
			}

		case LogisticsStateSevereTimeout:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) <= %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((latest_state_change <= %d) OR (first_state_change <= %d))", LogisticsProgressInTransit,
					time.Now().Unix()-10*24*60*60, time.Now().Unix()-20*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) <= %d AND state != %d ) OR (progress = %d AND ((latest_state_change <= %d) OR (first_state_change <= %d)))",
					LogisticsProgressUnprocessed, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-10*24*60*60, time.Now().Unix()-20*24*60*60)
			}
		case LogisticsStateAbnormal:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND state = %d ", LogisticsProgressUnprocessed, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((status = '%s') OR (status = '%s') OR (status = '%s'))", LogisticsProgressInTransit,
					"Expired", "DeliveryFailure", "Exception")
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND state = %d) AND ((status = '%s') OR (status = '%s') OR (status = '%s'))",
					LogisticsProgressInTransit, LogisticsStateAbnormal,
					"Expired", "DeliveryFailure", "Exception")
			}
		default:
		}
		if len(oneSqlTmp) > 0 {
			sqlArr = append(sqlArr, oneSqlTmp)
		}
	}

	if len(sqlArr) == 1 {
		db = db.Where(sqlArr[0])
	} else if len(sqlArr) > 1 {
		sqlTotal := fmt.Sprintf("(%s)", sqlArr[0])
		for i := 1; i < len(sqlArr); i++ {
			sqlTotal = fmt.Sprintf("%s OR (%s)", sqlTotal, sqlArr[i])
		}
		db = db.Where(sqlTotal)
	}

	// 如果没有筛选项的话，就不显示 已签收 的订单
	if (len(t.RefNo) < 1) && (t.Progress < 1) && (len(logisticsState) < 1) && (createTimeS < 1) && (createTimeE < 1) &&
		(len(t.OrderCode) < 1) && (len(t.CountryCode) < 1) &&
		(t.UserID < 1) && (len(t.MyLogisticsChannel) < 1) && (t.Remark < 1) {
		db = db.Where("progress != ?", LogisticsProgressInDelivery)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (t *TrackInfo) PreciseQueryList(pn, ps int, createTimeS, createTimeE int64, logisticsState []int, tx ...*gorm.DB) (list []*TrackInfo, count int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(t.TableName())

	if t.RefNo == "" && t.OrderCode == "" {
		db = db.Where("ref_no = ?", t.RefNo)
		db = db.Where("order_code = ?", t.OrderCode)
	}

	if t.RefNo != "" {
		db = db.Where("ref_no = ?", t.RefNo)
	}
	if t.OrderCode != "" {
		db = db.Where("order_code = ?", t.OrderCode)
	}

	if createTimeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createTimeS)
	}
	if createTimeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", createTimeE)
	}
	if t.UserID > 0 {
		db = db.Where("user_id = ?", t.UserID)
	}
	if len(t.CountryCode) > 0 {
		db = db.Where("country_code = ?", t.CountryCode)
	}
	if len(t.MyLogisticsChannel) > 0 {
		db = db.Where("my_logistics_channel = ?", t.MyLogisticsChannel)
	}
	if t.Remark > 0 {
		db = db.Where("remark = ?", t.Remark)
	}

	// 物流进度
	if t.Progress > 0 {
		db = db.Where("progress = ?", t.Progress)
	}

	// 物流状态筛选，物流进度 的确定标准 在不同的物流进度下是不同的
	sqlArr := make([]string, 0)
	for _, oneLogisticState := range logisticsState {
		oneSqlTmp := ""
		switch oneLogisticState {
		case LogisticsStateNormal:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) > %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND (latest_state_change > %d) AND (first_state_change > %d))", LogisticsProgressInTransit,
					time.Now().Unix()-7*24*60*60, time.Now().Unix()-15*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) > %d AND state != %d) OR (progress = %d AND (latest_state_change > %d) AND (first_state_change > %d)))",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-7*24*60*60, time.Now().Unix()-15*24*60*60)
			}
		case LogisticsStateTimeout:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) <= %d AND unix_timestamp(created_at) > %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((((latest_state_change > %d) AND (latest_state_change <= %d)) AND (first_state_change > %d)) OR ((first_state_change > %d) AND (first_state_change <= %d) AND latest_state_change > %d ) )", LogisticsProgressInTransit,
					time.Now().Unix()-10*24*60*60, time.Now().Unix()-7*24*60*60, time.Now().Unix()-20*24*60*60,
					time.Now().Unix()-20*24*60*60, time.Now().Unix()-15*24*60*60, time.Now().Unix()-10*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) <= %d AND unix_timestamp(created_at) > %d AND state != %d) OR (progress = %d AND ((((latest_state_change > %d) AND (latest_state_change <= %d)) AND (first_state_change > %d)) OR ((first_state_change > %d) AND (first_state_change <= %d) AND latest_state_change > %d ) ))",
					LogisticsProgressUnprocessed, time.Now().Unix()-3*24*60*60, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-10*24*60*60, time.Now().Unix()-7*24*60*60, time.Now().Unix()-20*24*60*60, time.Now().Unix()-20*24*60*60, time.Now().Unix()-15*24*60*60, time.Now().Unix()-10*24*60*60)
			}

		case LogisticsStateSevereTimeout:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND unix_timestamp(created_at) <= %d AND state != %d",
					LogisticsProgressUnprocessed, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((latest_state_change <= %d) OR (first_state_change <= %d))", LogisticsProgressInTransit,
					time.Now().Unix()-10*24*60*60, time.Now().Unix()-20*24*60*60)
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND unix_timestamp(created_at) <= %d AND state != %d ) OR (progress = %d AND ((latest_state_change <= %d) OR (first_state_change <= %d)))",
					LogisticsProgressUnprocessed, time.Now().Unix()-5*24*60*60, LogisticsStateAbnormal,
					LogisticsProgressInTransit, time.Now().Unix()-10*24*60*60, time.Now().Unix()-20*24*60*60)
			}
		case LogisticsStateAbnormal:
			switch t.Progress {
			case LogisticsProgressUnprocessed:
				oneSqlTmp = fmt.Sprintf("progress = %d AND state = %d ", LogisticsProgressUnprocessed, LogisticsStateAbnormal)
			case LogisticsProgressInTransit:
				oneSqlTmp = fmt.Sprintf("progress = %d AND ((status = '%s') OR (status = '%s') OR (status = '%s'))", LogisticsProgressInTransit,
					"Expired", "DeliveryFailure", "Exception")
			case LogisticsProgressInDelivery: // 不可用
			case LogisticsProgressAbnormal: // 不可用
			default:
				oneSqlTmp = fmt.Sprintf("(progress = %d AND state = %d) AND ((status = '%s') OR (status = '%s') OR (status = '%s'))",
					LogisticsProgressInTransit, LogisticsStateAbnormal,
					"Expired", "DeliveryFailure", "Exception")
			}
		default:
		}
		if len(oneSqlTmp) > 0 {
			sqlArr = append(sqlArr, oneSqlTmp)
		}
	}

	if len(sqlArr) == 1 {
		db = db.Where(sqlArr[0])
	} else if len(sqlArr) > 1 {
		sqlTotal := fmt.Sprintf("(%s)", sqlArr[0])
		for i := 1; i < len(sqlArr); i++ {
			sqlTotal = fmt.Sprintf("%s OR (%s)", sqlTotal, sqlArr[i])
		}
		db = db.Where(sqlTotal)
	}

	// 如果没有筛选项的话，就不显示 已签收 的订单
	if (len(t.RefNo) < 1) && (t.Progress < 1) && (len(logisticsState) < 1) && (createTimeS < 1) && (createTimeE < 1) &&
		(len(t.OrderCode) < 1) && (len(t.CountryCode) < 1) &&
		(t.UserID < 1) && (len(t.MyLogisticsChannel) < 1) && (t.Remark < 1) {
		db = db.Where("progress != ?", LogisticsProgressInDelivery)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (c *TrackInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*TrackInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *TrackInfo) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&TrackInfo{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *TrackInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *TrackInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(TrackInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *TrackInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 查询 track 表 所有 7月23日后 carrier_code = 190172  运单
func (c *TrackInfo) GetCarrierCode190172() (list []*TrackInfo, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, carrier_code, ref_no, remark, created_at,order_code")

	err = db.Where("carrier_code = ? AND created_at >= '2023-07-23 00:00:00'", "190172").Where("country_code = ?", "US").Find(&list).Error
	//err = db.Where("created_at >= '2023-07-23 00:00:00'").Find(&list).Error

	return
}

// UpdateCarrierCode21051 将 refList 中的运单的 carrier_code 更新为 21051
func (c *TrackInfo) UpdateCarrierCode21051(refList []string) (err error) {

	db := mysql.NewConn().Table(c.TableName())

	err = db.Where("ref_no IN (?)", refList).Update("carrier_code", "21051").Error

	return
}
