package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*AfterSale)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*AfterSale)(nil))
}

const (
	// 处理结果
	NOT           = 1 // 暂无
	NO_PROCESSING = 2 // 不处理
	REFUND        = 3 // 退款
	REISSUE       = 4 // 重发
	Cancellation  = 5 // 工单部分取消
	// 售后状态
	PROCESSING = 1 // 处理中
	AUDIT      = 2 // 待审核
	FULFILL    = 3 // 已完成
)

// AfterSale 售后事项表
type AfterSale struct {
	ID            uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	State         int       `json:"state"`      // 状态 1 处理中 2 待审核 3 已完成
	StateTime     int64     `json:"state_time"` // 状态变更时间戳
	OrderCode     string    `json:"order_code"` // 原订单号
	UserID        uint      `json:"user_id"`    // 用户
	AdminName     string    `json:"admin_name"`
	AfterSaleCode string    `json:"after_sale_code"` // 售后ID
	Cause         string    `json:"cause"`           // 售后原因
	BackChannel   string    `json:"back_channel"`    // 反馈渠道
	ContactWay    string    `json:"contact_way"`     // 联系方式

	RetryOrderCode string `json:"retry_order_code"` // 重发订单号
	IndemnityMoney int    `json:"indemnity_money"`  // 补偿金额  美分
	RefundMoney    int    `json:"refund_money"`     // 退款金额 美分
	Responsible    string `json:"responsible"`      // 责任方
	Handling       int    `json:"handling"`         // 处理结果

	ProcessingRecord string `json:"processing_record" gorm:"type:BLOB;"` // 处理记录  json序列化

}

// 处理记录
type ProcessingRecordJson struct {
	AdminName      string `json:"admin_name"`
	Cause          string `json:"cause"`           // 原因判定
	Responsible    string `json:"responsible"`     // 责任方
	IsIndemnity    bool   `json:"is_indemnity"`    // 是否补偿
	IndemnityMoney int    `json:"indemnity_money"` // 补偿金额  美分
	Details        string `json:"details"`         // 详情，富文本可放图片
	Handling       int    `json:"handling"`        // 处理结果
	RefundModel    int    `json:"refund_model"`    // 退款模式 - 1全额 - 2部分
	RefundMoney    int    `json:"refund_money"`    // 退款金额 refund_model =2 时使用，美分
	RefundMethod   int    `json:"refund_method"`   // 退款方式 -1原路 -2钱包
	OrderReq       string `json:"order_req"`       // 重发订单的序列化数据
	CheckState     int    `json:"check_state"`     // 审核状态 1 待审核 2 已通过 3 未通过
	CheckRemark    string `json:"check_remark"`    // 审核原因备注
	SaveTime       int64  `json:"save_time"`       // 保存时间戳, 可以当ID用
	CancelWork     string `json:"cancel_work"`     // 取消工单号序列化 数组
}

// TableName 表名
func (a *AfterSale) TableName() string {
	return "after_sale"
}

// GetCountByDay is 查询今日创建的售后事项数量
func (a *AfterSale) GetCountByDay() (count int64, err error) {
	// 获取今日时间戳 开始和结束
	todayTime := time.Now().Format("2006-01-02")
	err = mysql.NewConnV2().Model(&AfterSale{}).Where("created_at like ?", "%"+todayTime+"%").Count(&count).Error
	return
}

// 新建一个
func (a *AfterSale) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(a.TableName()).Create(a).Error
}

// GetList is 根据筛选条件获取列表
func (a *AfterSale) GetList(pn, ps int, timeS, timeE int64) (list []*AfterSale, Count int, err error) {

	db := mysql.NewConn().Table(a.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if a.UserID != 0 {
		db = db.Where("user_id = ?", a.UserID)
	}

	if a.Cause != "" {
		db = db.Where("cause = ?", a.Cause)
	}

	if a.State != 0 {
		db = db.Where("state = ?", a.State)
	}

	if a.Handling != 0 {
		db = db.Where("handling = ?", a.Handling)
	}
	if a.AdminName != "" {
		db = db.Where("admin_name = ?", a.AdminName)
	}

	if a.Responsible != "" {
		db = db.Where("responsible = ?", a.Responsible)
	}

	// 模糊查询
	if a.OrderCode != "" {
		db = db.Where("order_code like ?", "%"+a.OrderCode+"%")
	}
	if a.AfterSaleCode != "" {
		db = db.Where("after_sale_code like ?", "%"+a.AfterSaleCode+"%")
	}

	err = db.Count(&Count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// GetDetails 获取详情
func (a *AfterSale) GetDetails() (err error) {
	err = mysql.NewConn().Table(a.TableName()).Where("after_sale_code = ?", a.AfterSaleCode).First(a).Error
	return
}

func (a *AfterSale) Query() (err error) {

	db := mysql.NewConn().Table(a.TableName())
	if len(a.OrderCode) > 0 {
		db = db.Where("order_code = ?", a.OrderCode)
	}

	err = db.First(a).Error
	return
}

func (a *AfterSale) QueryIDListByOrderCodeList(orderCodeList []string) (list []*AfterSale, err error) {

	db := mysql.NewConn().Table(a.TableName())
	if len(orderCodeList) > 0 {
		db = db.Where("order_code in (?)", orderCodeList)
	}

	err = db.Select("id,order_code").Find(&list).Error
	return
}

// 获取列表数量
func (a *AfterSale) Count() (count int, err error) {

	db := mysql.NewConn().Table(a.TableName())
	if len(a.OrderCode) > 0 {
		db = db.Where("order_code = ?", a.OrderCode)
	}

	err = db.Count(&count).Error
	return
}

// putDetails 更新详情
func (a *AfterSale) PutDetails(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&AfterSale{}).Where("after_sale_code = ?", a.AfterSaleCode).Updates(a).Error
	return
}

func (c *AfterSale) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*AfterSale, 0)

	err := db.Find(&list).Error

	return list, err
}

func (a *AfterSale) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&AfterSale{}).Where("id IN (?)", ids).Delete(a).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *AfterSale) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *AfterSale) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(AfterSale)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *AfterSale) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
