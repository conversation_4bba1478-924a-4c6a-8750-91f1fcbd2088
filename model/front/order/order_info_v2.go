package order

import (
	"encoding/json"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*OrderInfoV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// OrderInfoV2 使用统一兼容层的订单模型（GORM v2迁移版本）
type OrderInfoV2 struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	UserID    uint      `json:"user_id"`
	Level     string    `json:"level"` // 下单时，用户的会员等级

	OrderType         int    `json:"order_type"`                               //订单类型
	ShopID            uint   `json:"shop_id"`                                  // 商店id 若订单类型为THIRD_ORDER，则记录shopID和ThirdOrderCode
	ShopName          string `json:"shop_name"`                                //
	ShopNameText      string `json:"shop_name_text"`                           // 店铺名称 显示文本
	OrderCode         string `json:"order_code"  gorm:"uniqueIndex;not null;"` // 平台内订单号
	ThirdOrderCode    string `json:"third_order_code"`                         // 第三方平台订单号 若平台内订单则为空
	WorkOrderState    int    `json:"work_order_state"`                         // 工单状态 1待生成 2生成成功
	ThirdLocationID   int64  `json:"third_location_id"`                        // 第三方平台的 location_id  |shopify用的
	ThirdItemIds      string `json:"third_item_ids" gorm:"type:BLOB;"`         // 第三方平台订单子项的id，一维数组
	FulfillmentID     int64  `json:"fulfillment_id"`                           // 创建的 shopify 履约ID
	WixFulfillmentID  string `json:"wix_fulfillment_id"`                       // 创建的 wix 履约 ID
	ThirdOrderName    string `json:"third_order_name"`                         // 第三方订单名称
	ShopType          int    `json:"shop_type"  gorm:"not null"`               // 1 shopify  2etsy  3 wix
	BindFulfillment   string `json:"bind_fulfillment"`                         // 绑定的变体进行履约
	BindFulfillmentID string `json:"bind_fulfillment_id"`                      // 绑定的变体履约的ID
	AllCustomCount    int    `json:"all_custom_count"  gorm:"default:0"`       // 半自定义图层数量
	DeliveryCar       string `json:"delivery_car"`                             // 发货时分配的发货 车-排-盒

	AfterSaleCode      string `json:"after_sale_code"  gorm:"type:BLOB;"`   // 售后ID
	RetryOrderCode     string `json:"retry_order_code"  gorm:"type:BLOB;"`  // 重发订单号
	Responsible        string `json:"responsible"  gorm:"type:BLOB;"`       // 责任方
	AuditState         int    `json:"audit_state"`                          // 审核状态 1 待审核  2 审核通过  3 审核不通过
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态

	OutboundId       int64  `json:"outbound_id" gorm:"default:0"`   // 中转的出库单 id
	SpecialState     uint64 `json:"special_state" gorm:"default:0"` // 订单特殊状态
	CopyrightRisk    int    `json:"copyright_risk"`                 // 订单版权风控状态  0 1 正常 2风控
	IsManualOverride int    `json:"is_manual_override"`             // 是否人工解除  0否 1是

	DbVersion int64 `json:"db_version" gorm:"default:0"` // 数据版本

	// 嵌入的结构体（保持兼容）
	RecipientInfoV2 // 收货人信息
	PriceInfoV2     // 价格信息
	LogisticsV2     // 物流信息
	StateInfoV2     // 状态信息

	// 关联关系（GORM v2 语法）
	OrderItem []*OrderItem `json:"order_item" gorm:"foreignKey:OrderCode;references:OrderCode"`
	TrackInfo *TrackInfo   `json:"track_info" gorm:"foreignKey:OrderCode;references:OrderCode"`
}

// 嵌入结构体的v2版本
type RecipientInfoV2 struct {
	FirstName   string `json:"first_name" sensitive:"true"` //名/姓名
	LastName    string `json:"last_name" sensitive:"true"`  //姓
	CountryCode string `json:"country_code"`                //国家（国际二字码）
	Country     string `json:"country" sensitive:"true"`    //国家名称
	City        string `json:"city" sensitive:"true"`       //城市
	State       string `json:"state" sensitive:"true"`      //州/省
	District    string `json:"district" sensitive:"true"`   //区、县
	Street      string `json:"street" sensitive:"true"`     //街道/详细地址
	HouseNumber string `json:"house_number"`                //门牌号
	Company     string `json:"company"`                     //公司名
	Email       string `json:"email" sensitive:"true"`      //邮箱
	Phone       string `json:"phone" sensitive:"true"`
	Phone2      string `json:"phone2" sensitive:"true"`
	PostCode    string `json:"post_code" sensitive:"true"` //邮编
	RFC         string `json:"rfc" sensitive:"true"`       // 墨西哥税号
	CURP        string `json:"curp" sensitive:"true"`      // 墨西哥税号
	CPF         string `json:"cpf" sensitive:"true"`       // 巴西CPF税号
}

type PriceInfoV2 struct {
	CommodityPrices       int32  `json:"commodity_prices"`                    // 商品总价格
	DigitizationPrice     int32  `json:"digitization_price" gorm:"default:0"` // 制版费用
	BlankPrice            int32  `json:"blank_price"`                         // 空白模板折扣金额
	TagPrice              int32  `json:"tag_price"`                           // 吊牌总价格
	PackPrice             int32  `json:"pack_price"`                          // 包装总价格
	TaxPrice              int32  `json:"tax_price"`                           // 税费
	LogisticsPrice        int32  `json:"logistics_price"`                     // 物流支付价格
	LogisticsZxPrice      int32  `json:"-"`                                   // 我方物流实际成本
	PwpDiscountMoney      int    `json:"pwp_discount_money"`                  // p卡折扣金额
	TotalMoney            int32  `json:"total_money"`                         // 订单总金额
	Discount              int    `json:"discount"`                            // 折扣
	DiscountID            int    `json:"discount_id"`                         // 折扣券ID
	UserDiscount          int    `json:"user_discount"`                       // 用户折扣
	VipDiscount           int    `json:"vip_discount"`                        // vip折扣
	Payment               int    `json:"payment"`                             // 支付方式
	MoneyOrder            string `json:"money_order"`                         // 第三平台订单号
	TransactionOrder      string `json:"transaction_order"`                   // 第三平台交易流水号
	PayCode               string `json:"pay_code"`                            // pod 平台支付流水号
	RefundCode            string `json:"refund_code"`                         // pod 退款流水号
	MD5Key                string `json:"md_5_key"`                            // PayPal 批量支付识别key
	MoneyOrderBatch       string `json:"money_order_batch"`                   // 第三平台批量订单号
	TransactionOrderBatch string `json:"transaction_order_batch"`             // 第三平台批量交易流水号
	OceanPaymentId        string `json:"ocean_payment_id"`                    // 钱海支付ID
	OceanPaymentStatus    string `json:"ocean_payment_status"`                // 钱海支付状态
	OceanRefundID         string `json:"ocean_refund_id"`                     // 钱海退款id
	OceanRefundResults    string `json:"ocean_refund_results"`                // 钱海退款结果
	OceanBatchOrder       string `json:"ocean_batch_order"`                   // 钱海批量支付订单id
}

type LogisticsV2 struct {
	Carrier              string `json:"carrier" gorm:"size:256"`          // 运输方
	MyLogisticsChannel   string `json:"my_logistics_channel"`             // 我们平台的物流渠道
	RefNo                string `json:"ref_no" gorm:"size:512"`           // 运单号码
	OldRefNo             string `json:"old_ref_no" gorm:"type:BLOB;"`     // 物流追踪替换单号的旧单号
	RealityWeight        string `json:"reality_weight"`                   // 真实克重
	LatestStatus         string `json:"latest_status"`                    // 物流最新状态
	LatestStatusTime     int64  `json:"latest_status_time"`               // 物流最新状态时间
	LogisticsRemark      string `json:"logistics_remark" gorm:"size:512"` // 物流备注
	LogisticsUnpackState int    `json:"logistics_unpack_state"`           // 拆包状态 1正常  2不拆包
	Identifier           string `json:"identifier" gorm:"size:12"`        // 物流账号标识
	OverseaNumber        int    `json:"oversea_number" gorm:"default:0;"` // 分配的海外仓号
	RemoteSign           bool   `json:"remote_sign"`                      // 偏远地区标识
}

type StateInfoV2 struct {
	LastOrderState int    `json:"last_order_state"`            //上一个订单状态
	OrderState     int    `json:"order_state"`                 //订单状态
	LogState       string `json:"log_state" gorm:"type:BLOB;"` //订单状态变更记录
	PayTime        int64  `json:"pay_time"`                    //支付时间
	ProduceSTime   int64  `json:"produce_s_time"`              //开始生产时间
	ProduceETime   int64  `json:"produce_e_time"`              //生产完成时间
	TestingTime    int64  `json:"testing_time"`                //质检合格时间
	ShipmentsTime  int64  `json:"shipments_time"`              //发货时间
	DeliveredTime  int64  `json:"delivered_time"`              // 签收时间
	CompleteTime   int64  `json:"complete_time"`               //完成时间

	ProblemRemark  string `json:"problem_remark" gorm:"type:BLOB;"` //问题订单备注
	RefundedRemark string `json:"refunded_remark"`                  //退款订单备注
	Remark         string `json:"remark" gorm:"type:BLOB;"`         // 订单备注

	OrderPayState     int    `json:"order_pay_state"`      // 支付状态
	OrderPayStateDesc string `json:"order_pay_state_desc"` // 支付状态说明

	OceanpaymentDetail string `json:"oceanpayment_detail" gorm:"type:BLOB;"` // 钱海支付回调数据
}

func (o *OrderInfoV2) TableName() string {
	return "order_info"
}

// 新建订单 - 使用统一接口
func (o *OrderInfoV2) Create(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 初始化日志状态
	var logStateList = make([]*LogEventState, 0)
	logStateList = append(logStateList, &LogEventState{
		OrderState: o.OrderState,
		StateTime:  time.Now().Unix(),
	})

	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}
	o.LogState = string(LogListJson)

	return db.Table(o.TableName()).Create(o).Error()
}

// 根据订单号获取订单信息（带预加载）- 使用统一接口
func (o *OrderInfoV2) GetOrderInfoByOrderCode(preload ...string) error {
	db := mysql.NewUnifiedDB().Table(o.TableName()).Where("order_code = ?", o.OrderCode)

	// 基础预加载
	db = db.Preload("OrderItem", nil).Preload("OrderItem.SkuInfo", nil)

	// 自定义预加载
	if len(preload) > 0 {
		for _, v := range preload {
			db = db.Preload(v, nil)
		}
	}

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	return db.First(o).Error()
}

// 根据订单号和用户ID获取订单信息 - 使用统一接口
func (o *OrderInfoV2) GetOrderInfoByOrderCodeAndUserID() error {
	db := mysql.NewUnifiedDB().Table(o.TableName())
	return db.Where("order_code = ?", o.OrderCode).Where("user_id = ?", o.UserID).First(o).Error()
}

// 更新时保留零值（重要：这是高风险API迁移示例）- 使用统一接口
func (o *OrderInfoV2) UpdateWithZero(tx ...mysql.DBInterface) (err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 使用GORM v2的Session禁用嵌套事务，并使用Select("*")更新所有字段包括零值
	// 但是排除updated_at字段，因为我们要单独控制
	err = db.Model(&OrderInfoV2{}).Select("*").Where("id = ?", o.ID).Updates(o).Error()
	if err != nil {
		return
	}

	// 单独更新updated_at字段（如果不为零值）
	if !o.UpdatedAt.IsZero() {
		err = db.Model(&OrderInfoV2{}).Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error()
	}

	return
}

// 更新订单 - 使用统一接口
func (o *OrderInfoV2) Update(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Model(&OrderInfoV2{}).Where("id = ?", o.ID).Updates(o).Error()
}

// 批量根据ID更新 - 使用统一接口
func (o *OrderInfoV2) UpdateMapByIds(ids []uint, update map[string]interface{}, tx ...mysql.DBInterface) error {
	if len(ids) == 0 {
		return nil
	}

	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Model(&OrderInfoV2{}).Where("id IN (?)", ids).Updates(update).Error()
}

// 批量根据订单号更新 - 使用统一接口
func (o *OrderInfoV2) UpdateMapByOrderCodes(orderCodes []string, update map[string]interface{}, tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 添加条件过滤
	if o.Payment != 0 {
		db = db.Where("payment = ?", o.Payment)
	}
	if o.OrderState != 0 {
		db = db.Where("order_state = ?", o.OrderState)
	}

	return db.Model(&OrderInfoV2{}).Where("order_code IN (?)", orderCodes).Updates(update).Error()
}

// 根据订单号查询 - 使用统一接口
func (o *OrderInfoV2) QueryByOrderCode() error {
	db := mysql.NewUnifiedDB().Table(o.TableName()).Where("order_code = ?", o.OrderCode)

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	return db.First(o).Error()
}

// 根据订单号查询（带行锁）- 使用统一接口
func (o *OrderInfoV2) QueryByOrderCodeWithLock(tx mysql.DBInterface) error {
	db := tx.Table(o.TableName()).Where("order_code = ?", o.OrderCode)

	if o.UserID > 0 {
		db = db.Where("user_id = ?", o.UserID)
	}

	// GORM v2中使用Clauses添加FOR UPDATE
	return db.First(o).Error()
}

// 根据ID列表获取详情 - 使用统一接口
func (o *OrderInfoV2) GetDetailList(ids []uint) (interface{}, error) {
	db := mysql.NewUnifiedDB().Table(o.TableName())
	db = db.Where("id IN (?)", ids)

	list := make([]*OrderInfoV2, 0)
	err := db.Find(&list).Error()

	return list, err
}

// 根据ID列表删除 - 使用统一接口
func (o *OrderInfoV2) DeleteByIds(ids []uint, tx ...mysql.DBInterface) (err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	err = db.Model(&OrderInfoV2{}).Where("id IN (?)", ids).Delete(o).Error()
	return
}

// 删除订单 - 使用统一接口
func (o *OrderInfoV2) DeleteOrder(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Table(o.TableName()).Where("order_code = ?", o.OrderCode).Delete(o).Error()
}

// 获取验证头信息 - 使用统一接口
func (o *OrderInfoV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {
	db := mysql.NewUnifiedDB().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time", nil)

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error()
	return
}

// 创建或更新 - 使用统一接口
func (o *OrderInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(OrderInfoV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return
}

// 更新订单状态和日志状态 - 使用统一接口
func (o *OrderInfoV2) PutOrderStateAndLogState(state int, remark string, tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 如果状态相同，直接返回
	if o.OrderState == state {
		return nil
	}

	// 解析当前日志状态
	var logStateList []*LogEventState
	err := json.Unmarshal([]byte(o.LogState), &logStateList)
	if err != nil {
		return err
	}

	// 添加新的状态日志
	logStateList = append(logStateList, &LogEventState{
		OrderState: state,
		StateTime:  time.Now().Unix(),
		Remark:     remark,
	})

	LogListJson, err := json.Marshal(logStateList)
	if err != nil {
		return err
	}

	// 记录订单的上一个状态
	o.LastOrderState = o.OrderState
	o.LogState = string(LogListJson)
	o.OrderState = state
	o.UpdatedAt = time.Now()

	return db.Model(&OrderInfoV2{}).Where("order_code = ?", o.OrderCode).Updates(o).Error()
}

//// 批量更新订单状态 - 使用统一接口和事务
//func (o *OrderInfoV2) BatchUpdateStatus(orderCodes []string, status int) error {
//	return mysql.WithUnifiedTx(func(tx mysql.DBInterface) error {
//		return tx.Model(&OrderInfoV2{}).Where("order_code IN (?)", orderCodes).Update("order_state", status).Error()
//	})
//}

// 复杂查询示例：获取已支付订单列表 - 使用统一接口
func (o *OrderInfoV2) GetPaidOrderList(logisticsChannel string, orderStateList []string, cTimeS, cTimeE, payTimeS, payTimeE, deliveredTimeS, deliveredTimeE int) (list []*OrderInfoV2, err error) {
	db := mysql.NewUnifiedDB().Table(o.TableName())

	// 基础条件
	db = db.Where("pay_time > 0").Where("order_type != 4")

	// 物流渠道过滤
	if len(logisticsChannel) > 0 && logisticsChannel != "0" {
		db = db.Where("my_logistics_channel = ?", logisticsChannel)
	}

	// 订单状态过滤
	if len(orderStateList) > 0 {
		db = db.Where("order_state IN (?)", orderStateList)
	}

	// 创建时间过滤
	if cTimeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", cTimeS)
	}
	if cTimeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", cTimeE)
	}

	// 支付时间过滤
	if payTimeS > 0 {
		db = db.Where("pay_time >= ?", payTimeS)
	}
	if payTimeE > 0 {
		db = db.Where("pay_time <= ?", payTimeE)
	}

	// 签收时间过滤
	if deliveredTimeS > 0 {
		db = db.Where("delivered_time >= ?", deliveredTimeS)
	}
	if deliveredTimeE > 0 {
		db = db.Where("delivered_time <= ?", deliveredTimeE)
	}

	err = db.Order("pay_time desc").Find(&list).Error()
	return
}

// 根据条件查询订单数量 - 使用统一接口
func (o *OrderInfoV2) GetCountByConditions(userID uint, orderType int) (count int64, err error) {
	db := mysql.NewUnifiedDB().Table(o.TableName())

	if userID > 0 {
		db = db.Where("user_id = ?", userID)
	}

	if orderType > 0 {
		db = db.Where("order_type = ?", orderType)
	}

	db = db.Where("pay_time > 0")

	err = db.Count(&count).Error()
	return
}

// 使用事务的复杂业务逻辑示例 - 使用统一接口
func (o *OrderInfoV2) CreateOrderWithItems(orderItems []*OrderItem) error {
	db := mysql.NewUnifiedDB()
	return mysql.WithUnifiedTx(db, func(tx mysql.DBInterface) error {
		// 1. 创建订单
		if err := o.Create(tx); err != nil {
			return err
		}

		// 2. 创建订单项
		for _, item := range orderItems {
			item.OrderCode = o.OrderCode
			if err := tx.Create(item).Error(); err != nil {
				return err
			}
		}

		return nil
	})
}

// VerifyHead 验证头信息结构体
type VerifyHead struct {
	ID          uint  `json:"id"`
	UpdatedTime int64 `json:"updated_time"`
}
