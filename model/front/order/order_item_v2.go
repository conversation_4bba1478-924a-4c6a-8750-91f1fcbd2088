package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*OrderItemV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// OrderItemV2 订单明细表 - GORM v2版本
type OrderItemV2 struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	OrderCode string `json:"order_code"`                // 平台订单号 关联
	Source    string `json:"source"`                    // 商品来源，template|shop
	DesignID  int    `json:"design_id"`                 // 设计模板id
	Versions  int    `json:"versions" gorm:"default:1"` // 下单时选中设计的版本号
	SkuTag    string `json:"sku_tag"`                   // sku标签 ABCDEFG,手动标记

	BrandLabelID uint `json:"brand_label_id" gorm:"default:0"` // 品牌领标ID

	SKU         string `json:"sku"`                           // 平台sku
	ThirdSKU    string `json:"third_sku"`                     // 用户商店对应的sku
	Price       int    `json:"price"`                         // 美分
	Title       string `json:"title"`                         // 商品名称
	ColorName   string `json:"color_name"`                    // 颜色名称
	ColorNum    string `json:"color_num"`                     // 颜色编号
	Size        string `json:"size"`                          // 尺码
	DesignInfo  string `json:"design_info" gorm:"type:BLOB"`  // 设计信息
	CustomCount int    `json:"custom_count" gorm:"default:0"` // 半自定义图层数量
	EmbCount    int    `json:"emb_count" gorm:"default:0"`    // 刺绣框数量

	ModelImg      string `json:"model_img" gorm:"type:BLOB"` // 模特展示图信息
	IndexImg      string `json:"index_img"`                  // 列表首图
	Weight        string `json:"weight"`                     // 面料克重
	RealityWeight string `json:"reality_weight"`             // 真实克重

	ItemCount          int    `json:"item_count"`                            // 属于订单第几项
	IsPodSKU           bool   `json:"is_pod_sku"`                            // 是否pod平台内sku
	IsIgnore           bool   `json:"is_ignore"`                             // 是否忽略
	InnerLabelIsWhite  *bool  `json:"inner_label_is_white" gorm:"default:0"` // 内领标是否使用白色打印
	ThirdProductID     int64  `json:"third_product_id"`                      // shopify 平台商品 id
	ThirdProductIDStr  string `json:"third_product_id_str"`                  // shopify 平台商品 GraphQL id
	FulfillmentService string `json:"fulfillment_service"`                   // 履约方数据
	OtherSku           string `json:"other_sku"`                             // 用于存储其他平台的sku

	// 吊牌相关
	TagDesignID   uint   `json:"tag_design_id" gorm:"default:0"` // 吊牌设计ID
	TagName       string `json:"tag_name"`                       // 吊牌名称
	TagModel      string `json:"tag_model" gorm:"type:BLOB"`     // 吊牌款式模特
	TagPrice      int    `json:"tag_price" gorm:"default:0"`     // 美分
	TagDesign     string `json:"tag_design" gorm:"type:BLOB"`    // 吊牌设计信息
	HangTagInfoID int64  `json:"hang_tag_info_id"`               // 吊牌款式 id
	TagVersions   int    `json:"tag_versions"`                   // 吊牌版本号

	// 包装相关
	PackDesignID uint   `json:"pack_design_id" gorm:"default:0"` // 包装设计ID
	PackPrice    int    `json:"pack_price" gorm:"default:0"`     // 美分
	PackDesign   string `json:"pack_design" gorm:"type:BLOB"`    // 包装设计信息
	PackName     string `json:"pack_name"`                       // 包装名称
	PackModel    string `json:"pack_model" gorm:"type:BLOB"`     // 包装模特
	PackID       int64  `json:"pack_id"`                         // 包装袋款式 id
	PackVersions int    `json:"pack_versions"`                   // 包装版本号

	IsBlank    bool `json:"is_blank"`    // 是否空白模板
	AuditState int  `json:"audit_state"` // 审核状态 1 待审核 2 审核通过 3 审核不通过

	CopyrightRisk      int    `json:"copyright_risk"`                       // 订单版权风控状态
	RiskReason         string `json:"risk_reason" gorm:"type:BLOB"`         // 侵权描述
	DigitizationPrice  int32  `json:"digitization_price" gorm:"default:0"`  // 制版费用
	DigitizationCount  int    `json:"digitization_count" gorm:"default:0"`  // 制版任务数量
	DigitizationList   string `json:"digitization_list" gorm:"type:BLOB"`   // 制版任务信息
	DigitizationStatus int    `json:"digitization_status" gorm:"default:0"` // 制版任务状态
	DigitizationIDList string `json:"digitization_id_list"`                 // 制版任务ID列表
}

func (o *OrderItemV2) TableName() string {
	return "order_item"
}

// 统一数据库接口方法
func (o *OrderItemV2) Create(db mysql.DBInterface) error {
	return db.Create(o).Error()
}

func (o *OrderItemV2) Query(db mysql.DBInterface) error {
	return db.Where(o).First(o).Error()
}

func (o *OrderItemV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*OrderItemV2, int64, error) {
	var list []*OrderItemV2
	var count int64

	query := db.Model(&OrderItemV2{})
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (o *OrderItemV2) Update(db mysql.DBInterface) error {
	return db.Save(o).Error()
}

func (o *OrderItemV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(o).Error()
}

func (o *OrderItemV2) Delete(db mysql.DBInterface) error {
	return db.Delete(o).Error()
}

// 根据订单号查询订单项列表
func (o *OrderItemV2) GetListByOrderCode(db mysql.DBInterface, orderCode string) ([]*OrderItemV2, error) {
	var list []*OrderItemV2
	err := db.Where("order_code = ?", orderCode).Find(&list).Error
	return list, err()
}

// 根据订单号批量查询
func (o *OrderItemV2) GetListByOrderCodes(db mysql.DBInterface, orderCodes []string) ([]*OrderItemV2, error) {
	var list []*OrderItemV2
	err := db.Where("order_code IN ?", orderCodes).Find(&list).Error
	return list, err()
}

// 根据设计ID查询
func (o *OrderItemV2) GetListByDesignID(db mysql.DBInterface, designID int) ([]*OrderItemV2, error) {
	var list []*OrderItemV2
	err := db.Where("design_id = ?", designID).Find(&list).Error
	return list, err()
}

// 更新忽略状态
func (o *OrderItemV2) UpdateIgnoreStatus(db mysql.DBInterface, isIgnore bool) error {
	return db.Model(o).Update("is_ignore", isIgnore).Error()
}

// 更新审核状态
func (o *OrderItemV2) UpdateAuditState(db mysql.DBInterface, auditState int) error {
	return db.Model(o).Update("audit_state", auditState).Error()
}

// 批量更新审核状态
func (o *OrderItemV2) BatchUpdateAuditState(db mysql.DBInterface, orderCodes []string, auditState int) error {
	return db.Model(&OrderItemV2{}).Where("order_code IN ?", orderCodes).Update("audit_state", auditState).Error()
}

// 获取POD订单项
func (o *OrderItemV2) GetPodItems(db mysql.DBInterface, orderCode string) ([]*OrderItemV2, error) {
	var list []*OrderItemV2
	err := db.Where("order_code = ? AND is_pod_sku = ?", orderCode, true).Find(&list).Error
	return list, err()
}

//// 更新吊牌信息
//func (o *OrderItemV2) UpdateTagInfo(db mysql.DBInterface, tagDesignID uint, tagPrice int) error {
//	return db.Model(o).Updates(map[string]interface{}{
//		"tag_design_id": tagDesignID,
//		"tag_price":     tagPrice,
//	}).Error
//}

//// 更新包装信息
//func (o *OrderItemV2) UpdatePackInfo(db mysql.DBInterface, packDesignID uint, packPrice int) error {
//	return db.Model(o).Updates(map[string]interface{}{
//		"pack_design_id": packDesignID,
//		"pack_price":     packPrice,
//	}).Error
//}

// 获取制版任务相关订单项
func (o *OrderItemV2) GetDigitizationItems(db mysql.DBInterface, status int) ([]*OrderItemV2, error) {
	var list []*OrderItemV2
	err := db.Where("digitization_status = ? AND digitization_count > 0", status).Find(&list).Error
	return list, err()
}

// 更新制版状态
func (o *OrderItemV2) UpdateDigitizationStatus(db mysql.DBInterface, status int) error {
	return db.Model(o).Update("digitization_status", status).Error()
}

// 获取财务数据
func (o *OrderItemV2) GetFinanceData(db mysql.DBInterface, orderCodes []string) ([]*FinanceListV2, error) {
	var list []*FinanceListV2
	err := db.Table(o.TableName()).
		Select("COUNT(*) as total, sku, price, color_name, size, SUM(price) as total_money, tag_price, pack_price, order_code").
		Where("order_code IN ?", orderCodes).
		Group("sku, price, color_name, size, tag_price, pack_price, order_code").
		Find(&list).Error
	return list, err()
}

// 工厂验证头部信息
func (o *OrderItemV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) ([]*model.VerifyHead, int64, error) {
	var list []*model.VerifyHead
	var count int64

	db := mysql.NewUnifiedDB().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	if err := db.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	if idMax < 1 && idMin < 1 {
		offset := (pn - 1) * ps
		db = db.Offset(offset).Limit(ps)
	}

	err := db.Find(&list).Error
	return list, count, err()
}

// 创建或更新 - 工厂同步
func (o *OrderItemV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("OrderItemV2 detailData: ", string(detailData))

	detailObj := new(OrderItemV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingOrderItem OrderItemV2
	err = db.Where("id = ?", detailObj.ID).First(&existingOrderItem).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建OrderItem失败: ", err)
			return err()
		}
		log.Info("创建OrderItem成功: ", detailObj.ID)
	} else {
		// 更新现有记录
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新OrderItem失败: ", err)
			return err()
		}
		log.Info("更新OrderItem成功: ", detailObj.ID)
	}

	return nil
}

// 辅助结构体
type FinanceListV2 struct {
	Total      int    `json:"total"`
	SKU        string `json:"sku"`
	Price      int    `json:"price"`
	ColorName  string `json:"color_name"`
	Size       string `json:"size"`
	TotalMoney int    `json:"total_money"`
	TagPrice   int    `json:"tag_price"`
	PackPrice  int    `json:"pack_price"`
	OrderCode  string `json:"order_code"`
}
