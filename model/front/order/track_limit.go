package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*TrackLimit)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*TrackLimit)(nil))
}

// 物流限重表，记录各国家物流渠道限重数据
type TrackLimit struct {
	ID                 uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	CountryCode        string    `json:"country_code"`         // 国家（国际二字码 标准ISO 3166-2 ）
	LogisticsChannelNo string    `json:"logistics_channel_no"` // 物流渠道名称。
	Carrier            string    `json:"carrier"`              // 运输方
	MyLogisticsChannel string    `json:"my_logistics_channel"` // 我们平台的物流渠道
	LimitWeight        string    `json:"limit_weight"`         // 设置限重
}

func (t *TrackLimit) TableName() string {
	return "track_limit"
}

// 新增
func (r *TrackLimit) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(r.TableName()).Create(r).Error
}

func (r *TrackLimit) Query() (err error) {
	db := mysql.NewConn().Table(r.TableName())

	if r.CountryCode != "" {
		db = db.Where("country_code = ?", r.CountryCode)
	}
	if r.Carrier != "" {
		db = db.Where("carrier = ?", r.Carrier)
	}
	if r.LogisticsChannelNo != "" {
		db = db.Where("logistics_channel_no = ?", r.LogisticsChannelNo)
	}
	if r.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", r.MyLogisticsChannel)
	}

	err = db.First(r).Error
	return err
}

func (r *TrackLimit) IsExists() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(r.TableName())

	if r.CountryCode != "" {
		db = db.Where("country_code = ?", r.CountryCode)
	}
	if r.Carrier != "" {
		db = db.Where("carrier = ?", r.Carrier)
	}
	if r.LogisticsChannelNo != "" {
		db = db.Where("logistics_channel_no = ?", r.LogisticsChannelNo)
	}
	if r.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", r.MyLogisticsChannel)
	}

	db = db.Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil

}

// 更新
func (r *TrackLimit) Update() (err error) {
	db := mysql.NewConn().Table(r.TableName())

	if r.CountryCode != "" {
		db = db.Where("country_code = ?", r.CountryCode)
	}
	if r.Carrier != "" {
		db = db.Where("carrier = ?", r.Carrier)
	}
	if r.LogisticsChannelNo != "" {
		db = db.Where("logistics_channel_no = ?", r.LogisticsChannelNo)
	}
	if r.MyLogisticsChannel != "" {
		db = db.Where("my_logistics_channel = ?", r.MyLogisticsChannel)
	}

	return db.Update("limit_weight", r.LimitWeight).Error
}

func (c *TrackLimit) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*TrackLimit, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *TrackLimit) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&TrackLimit{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *TrackLimit) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *TrackLimit) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(TrackLimit)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *TrackLimit) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
