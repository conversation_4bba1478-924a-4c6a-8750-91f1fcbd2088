package order

import (
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
)

func init() {
	// 注册TrackInfoV2模型到数据库迁移
	mysql.RegisterTable(&TrackInfoV2{})
}

// TrackInfoV2 物流追踪信息模型V2版本 - 支持详细的物流状态追踪和多物流商管理
type TrackInfoV2 struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	UserID         uint      `json:"user_id"`
	OrderCode      string    `json:"order_code"`                                     // 订单号  同物流信息的tag
	OrderIndex     int       `json:"order_index"`                                    // 代表订单的第几个运单，通常为1，拆包的会有1/2/3/4/5之类的，只有1的追踪状态会更改订单状态
	TrackState     int       `json:"track_state"`                                    // 调用接口的状态   1成功、2失败
	TrackErrMsg    string    `json:"track_err_msg"`                                  // 调用接口报错的信息
	RefNo          string    `json:"ref_no"     gorm:"unique;not null"`              // 运单号码
	OldRefNo       string    `json:"old_ref_no" gorm:"type:BLOB;" gorm:"default:[]"` // 旧的运输单号和运输方 json
	Event          string    `json:"event"`                                          // 通知类型 状态
	Param          string    `json:"param"`                                          // 物流单号附加参数，请参考【注册物流单号】入参说明
	Carrier        string    `json:"carrier"`                                        // 运输商
	CarrierCode    int32     `json:"carrier_code"`                                   // 运输商代码
	Status         string    `json:"status"`                                         // 物流主状态。
	SubStatus      string    `json:"sub_status"`                                     // 包裹子状态.
	SubStatusDescr string    `json:"sub_status_descr"`                               // 状态描述。

	Milestone           string `json:"milestone" gorm:"type:BLOB;"` // 里程碑 json 序列化
	DaysAfterOrder      int    `json:"days_after_order"`
	DaysOfTransit       int    `json:"days_of_transit"`             // 运输时效：包裹运输的天数，按以下优先级进行计算：
	DaysOfTransitDone   int    `json:"days_of_transit_done"`        // 妥投时效：与 days_of_transit 计算方式一样，在没有 成功签收 状态之前，此项始终为 0，不累加计数。
	DaysAfterLastUpdate int    `json:"days_after_last_update"`      // 信息无更新天数 |从最后一条事件至今没有更新的间隔天数，累加计数；
	WeightKg            string `json:"weight_kg"`                   // 把原始重量信息（ weight_raw） 转换为公斤。
	MiscInfo            string `json:"misc_info" gorm:"type:BLOB;"` // 包裹附属信息节点。（重量 体积、尺寸、件数、风险信息等）
	From                string `json:"from"`                        // 预计投递最早时间（ISO 格式)
	To                  string `json:"to"`                          // 预计投递最晚时间（ISO 格式）
	Tracking            string `json:"tracking" gorm:"type:BLOB;"`  // 物流信息节点。

	MyLogisticsChannel string `json:"my_logistics_channel"`                 // pod 平台的物流渠道
	LogisticsFees      int    `json:"logistics_fees"`                       // 物流费用，单位美分
	FirstStateChange   int64  `json:"first_state_change"`                   // 第一次状态更新，时间戳
	LatestStateChange  int64  `json:"latest_state_change" gorm:"default:0"` // 最近一次状态更新，时间戳
	Remark             int    `json:"remark"  gorm:"default:1"`             // 标注
	RemarkDetail       string `json:"remark_detail" gorm:"type:BLOB;"`      // 标注详细信息
	CountryCode        string `json:"country_code"`                         // 国家
	Progress           int    `json:"progress"`                             // 物流进度
	State              int    `json:"state" gorm:"default:1"`               // 物流状态，目前只在 待物流方操作 的状态下有效
	DeliveryTime       int64  `json:"delivery_time"`                        // 妥投时间

	TrackInfo string `json:"track_info" gorm:"type:BLOB;"` // 物流信息主结构节点。完整的物流信息
}

// TableName 返回表名
func (t *TrackInfoV2) TableName() string {
	return "track_info"
}

// Create 创建新的物流追踪记录
func (t *TrackInfoV2) Create(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Create(t).Error()
}

// Update 更新物流追踪记录
func (t *TrackInfoV2) Update(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(t).Updates(t).Error()
}

// UpdateBy 根据条件更新记录
func (t *TrackInfoV2) UpdateBy(tx ...mysql.DBInterface) error {
	if t.ID == 0 && t.RefNo == "" {
		return errors.New("update condition cannot be empty")
	}

	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	query := db.Model(t)
	if t.ID > 0 {
		query = query.Where("id = ?", t.ID)
	} else if t.RefNo != "" {
		query = query.Where("ref_no = ?", t.RefNo)
	}

	return query.Updates(t).Error()
}

// UpdateByID 根据ID更新特定字段
func (t *TrackInfoV2) UpdateByID(update map[string]interface{}, tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(t).Where("id = ?", t.ID).Updates(update).Error()
}

// Query 查询物流追踪记录
func (t *TrackInfoV2) Query(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	query := db.Model(t)
	if t.ID > 0 {
		query = query.Where("id = ?", t.ID)
	}
	if t.RefNo != "" {
		query = query.Where("ref_no = ?", t.RefNo)
	}
	if t.OrderCode != "" {
		query = query.Where("order_code = ?", t.OrderCode)
	}
	if t.UserID > 0 {
		query = query.Where("user_id = ?", t.UserID)
	}

	return query.First(t).Error()
}

// IsExist 检查记录是否存在
func (t *TrackInfoV2) IsExist(tx ...mysql.DBInterface) (bool, error) {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	var count int64
	query := db.Model(t)

	if t.ID > 0 {
		query = query.Where("id = ?", t.ID)
	} else if t.RefNo != "" {
		query = query.Where("ref_no = ?", t.RefNo)
	} else if t.OrderCode != "" {
		query = query.Where("order_code = ?", t.OrderCode)
	} else {
		return false, errors.New("query condition cannot be empty")
	}

	err := query.Count(&count).Error()
	return count > 0, err
}

// QueryList 分页查询物流追踪列表
func (t *TrackInfoV2) QueryList(pn, ps int, createTimeS, createTimeE int64, logisticsState []int, hasHistory bool, tx ...mysql.DBInterface) (list []*TrackInfoV2, count int64, err error) {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	query := db.Model(&TrackInfoV2{})

	// 时间范围过滤
	if createTimeS > 0 {
		query = query.Where("created_at >= ?", time.Unix(createTimeS, 0))
	}
	if createTimeE > 0 {
		query = query.Where("created_at <= ?", time.Unix(createTimeE, 0))
	}

	// 物流状态过滤
	if len(logisticsState) > 0 {
		query = query.Where("track_state IN (?)", logisticsState)
	}

	// 历史记录过滤
	if !hasHistory {
		query = query.Where("order_index = 1")
	}

	// 用户ID过滤
	if t.UserID > 0 {
		query = query.Where("user_id = ?", t.UserID)
	}

	// 订单号过滤
	if t.OrderCode != "" {
		query = query.Where("order_code LIKE ?", "%"+t.OrderCode+"%")
	}

	// 运单号过滤
	if t.RefNo != "" {
		query = query.Where("ref_no LIKE ?", "%"+t.RefNo+"%")
	}

	// 物流商过滤
	if t.Carrier != "" {
		query = query.Where("carrier = ?", t.Carrier)
	}

	// 统计总数
	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	// 分页查询
	err = query.Order("created_at DESC").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error()
	return
}

// PreciseQueryList 精确查询物流追踪列表
func (t *TrackInfoV2) PreciseQueryList(pn, ps int, createTimeS, createTimeE int64, logisticsState []int, tx ...mysql.DBInterface) (list []*TrackInfoV2, count int64, err error) {
	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	query := db.Model(&TrackInfoV2{})

	// 精确时间范围过滤
	if createTimeS > 0 && createTimeE > 0 {
		query = query.Where("created_at BETWEEN ? AND ?", time.Unix(createTimeS, 0), time.Unix(createTimeE, 0))
	}

	// 物流状态过滤
	if len(logisticsState) > 0 {
		query = query.Where("track_state IN (?)", logisticsState)
	}

	// 精确匹配条件
	if t.UserID > 0 {
		query = query.Where("user_id = ?", t.UserID)
	}
	if t.OrderCode != "" {
		query = query.Where("order_code = ?", t.OrderCode)
	}
	if t.RefNo != "" {
		query = query.Where("ref_no = ?", t.RefNo)
	}
	if t.Carrier != "" {
		query = query.Where("carrier = ?", t.Carrier)
	}
	if t.CarrierCode > 0 {
		query = query.Where("carrier_code = ?", t.CarrierCode)
	}
	if t.Status != "" {
		query = query.Where("status = ?", t.Status)
	}
	if t.Progress > 0 {
		query = query.Where("progress = ?", t.Progress)
	}
	if t.State > 0 {
		query = query.Where("state = ?", t.State)
	}
	if t.CountryCode != "" {
		query = query.Where("country_code = ?", t.CountryCode)
	}

	// 统计总数
	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	// 分页查询
	err = query.Order("latest_state_change DESC, created_at DESC").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error()
	return
}

// GetDetailList 根据ID列表获取详细信息
func (t *TrackInfoV2) GetDetailList(ids []uint) (interface{}, error) {
	if len(ids) == 0 {
		return nil, errors.New("ids cannot be empty")
	}

	var list []*TrackInfoV2
	db := mysql.NewUnifiedDB()
	err := db.Where("id IN (?)", ids).Find(&list).Error()
	return list, err
}

// DeleteByIds 根据ID列表删除记录
func (t *TrackInfoV2) DeleteByIds(ids []uint, tx ...mysql.DBInterface) error {
	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}

	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Where("id IN (?)", ids).Delete(&TrackInfoV2{}).Error()
}

// UpdateWithZero 更新记录但不更新updated_at字段（与本地工厂数据保持同步）
func (t *TrackInfoV2) UpdateWithZero(tx ...mysql.DBInterface) error {
	if t.ID == 0 && t.RefNo == "" {
		return errors.New("update condition cannot be empty")
	}

	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// 构建更新字段映射
	updateMap := make(map[string]interface{})

	// 基础信息字段
	if t.UserID > 0 {
		updateMap["user_id"] = t.UserID
	}
	if t.OrderCode != "" {
		updateMap["order_code"] = t.OrderCode
	}
	if t.OrderIndex > 0 {
		updateMap["order_index"] = t.OrderIndex
	}
	if t.TrackState > 0 {
		updateMap["track_state"] = t.TrackState
	}
	if t.TrackErrMsg != "" {
		updateMap["track_err_msg"] = t.TrackErrMsg
	}
	if t.Event != "" {
		updateMap["event"] = t.Event
	}
	if t.Param != "" {
		updateMap["param"] = t.Param
	}
	if t.Carrier != "" {
		updateMap["carrier"] = t.Carrier
	}
	if t.CarrierCode > 0 {
		updateMap["carrier_code"] = t.CarrierCode
	}
	if t.Status != "" {
		updateMap["status"] = t.Status
	}
	if t.SubStatus != "" {
		updateMap["sub_status"] = t.SubStatus
	}
	if t.SubStatusDescr != "" {
		updateMap["sub_status_descr"] = t.SubStatusDescr
	}

	// 时效和状态字段
	if t.DaysAfterOrder > 0 {
		updateMap["days_after_order"] = t.DaysAfterOrder
	}
	if t.DaysOfTransit > 0 {
		updateMap["days_of_transit"] = t.DaysOfTransit
	}
	if t.DaysOfTransitDone > 0 {
		updateMap["days_of_transit_done"] = t.DaysOfTransitDone
	}
	if t.DaysAfterLastUpdate > 0 {
		updateMap["days_after_last_update"] = t.DaysAfterLastUpdate
	}
	if t.Progress > 0 {
		updateMap["progress"] = t.Progress
	}
	if t.State > 0 {
		updateMap["state"] = t.State
	}
	if t.Remark > 0 {
		updateMap["remark"] = t.Remark
	}
	if t.CountryCode != "" {
		updateMap["country_code"] = t.CountryCode
	}

	// 时间戳字段
	if t.FirstStateChange > 0 {
		updateMap["first_state_change"] = t.FirstStateChange
	}
	if t.LatestStateChange > 0 {
		updateMap["latest_state_change"] = t.LatestStateChange
	}
	if t.DeliveryTime > 0 {
		updateMap["delivery_time"] = t.DeliveryTime
	}

	// JSON字段
	if t.Milestone != "" {
		updateMap["milestone"] = t.Milestone
	}
	if t.MiscInfo != "" {
		updateMap["misc_info"] = t.MiscInfo
	}
	if t.Tracking != "" {
		updateMap["tracking"] = t.Tracking
	}
	if t.RemarkDetail != "" {
		updateMap["remark_detail"] = t.RemarkDetail
	}
	if t.TrackInfo != "" {
		updateMap["track_info"] = t.TrackInfo
	}
	if t.OldRefNo != "" {
		updateMap["old_ref_no"] = t.OldRefNo
	}

	// 其他字段
	if t.WeightKg != "" {
		updateMap["weight_kg"] = t.WeightKg
	}
	if t.From != "" {
		updateMap["from"] = t.From
	}
	if t.To != "" {
		updateMap["to"] = t.To
	}
	if t.MyLogisticsChannel != "" {
		updateMap["my_logistics_channel"] = t.MyLogisticsChannel
	}
	if t.LogisticsFees > 0 {
		updateMap["logistics_fees"] = t.LogisticsFees
	}

	if len(updateMap) == 0 {
		return errors.New("no fields to update")
	}

	query := db.Model(&TrackInfoV2{})
	if t.ID > 0 {
		query = query.Where("id = ?", t.ID)
	} else {
		query = query.Where("ref_no = ?", t.RefNo)
	}

	return query.Update(updateMap).Error()
}

// CreateOrUpdate 创建或更新物流追踪记录
func (t *TrackInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	//db := mysql.NewUnifiedDB()

	// 检查记录是否存在
	exists, err := t.IsExist()
	if err != nil {
		return fmt.Errorf("check existence failed: %v", err)
	}

	if exists {
		// 更新现有记录
		return t.UpdateWithZero()
	} else {
		// 创建新记录
		return t.Create()
	}
}

// GetVerifyHead 获取验证头信息（用于数据校验）
func (t *TrackInfoV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	db := mysql.NewUnifiedDB()
	query := db.Model(&TrackInfoV2{}).Select("id, ref_no as unique_key, updated_at")

	// ID范围过滤
	if idMin > 0 {
		query = query.Where("id >= ?", idMin)
	}
	if idMax > 0 {
		query = query.Where("id <= ?", idMax)
	}

	// 更新时间范围过滤
	if updateTimeS > 0 {
		query = query.Where("updated_at >= ?", time.Unix(updateTimeS, 0))
	}
	if updateTimeE > 0 {
		query = query.Where("updated_at <= ?", time.Unix(updateTimeE, 0))
	}

	// 统计总数
	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	// 分页查询
	if idMax < 1 && idMin < 1 {
		query = query.Offset((pn - 1) * ps).Limit(ps)
	}

	err = query.Find(&list).Error()
	return
}

// GetCarrierCode190172 获取特定载体代码的物流记录
func (t *TrackInfoV2) GetCarrierCode190172() (list []*TrackInfoV2, err error) {
	db := mysql.NewUnifiedDB()
	err = db.Where("carrier_code = ?", 190172).Find(&list).Error()
	return
}

// UpdateCarrierCode21051 批量更新载体代码为21051
func (t *TrackInfoV2) UpdateCarrierCode21051(refList []string, tx ...mysql.DBInterface) error {
	if len(refList) == 0 {
		return errors.New("refList cannot be empty")
	}

	var db mysql.DBInterface
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	return db.Model(t).Where("ref_no IN (?)", refList).Update("carrier_code", "21051").Error()
}
