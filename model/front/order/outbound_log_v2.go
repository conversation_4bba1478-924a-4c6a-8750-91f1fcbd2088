package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*OutboundLogV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OutboundLogV2)(nil))
}

// OutboundLogV2 出库日志
type OutboundLogV2 struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	OrderCode       string    `json:"order_code"`                      // 订单号
	UserId          uint      `json:"user_id"`                         // 用户 id
	OrderTotalMoney int32     `json:"order_total_money"`               // 订单总金额，包括运费及税费等
	PayTime         int64     `json:"pay_time"`                        // 支付时间
	OrderState      int       `json:"order_state" gorm:"default:0"`    // 订单状态
	OutboundState   int       `json:"outbound_state" gorm:"default:0"` // 出库状态
	OutboundTime    int64     `json:"outbound_time" gorm:"default:0"`  // 出库时间
	Remark          string    `json:"remark" gorm:"type:BLOB;"`        // 备注
}

// TableName 表名
func (o *OutboundLogV2) TableName() string {
	return "outbound_log"
}

func (o *OutboundLogV2) GetDetailList(ids []uint) (interface{}, error) {
	db := mysql.NewConnV2().Table(o.TableName())
	db = db.Where("id IN (?)", ids)
	list := make([]*OutboundLogV2, 0)
	err := db.Find(&list).Error
	return list, err
}

func (o *OutboundLogV2) DeleteByIds(ids []uint, tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&OutboundLogV2{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (o *OutboundLogV2) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(o.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error

	return
}

func (o *OutboundLogV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(OutboundLogV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return
}

func (o *OutboundLogV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	db := mysql.NewConnV2().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (o *OutboundLogV2) Create(tx ...*gorm2.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Create(o).Error
}

func (o *OutboundLogV2) Query(tx ...*gorm2.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Where("order_code = ?", o.OrderCode).First(o).Error
}

func (o *OutboundLogV2) Update(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(o).Updates(o).Error
	return
}

func (o *OutboundLogV2) UpdateWithMap(data map[string]interface{}, tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(o).Updates(data).Error
	return
}

func (o *OutboundLogV2) QueryCountByUserId() (total int64, err error) {
	db := mysql.NewConnV2()
	err = db.Model(&OutboundLogV2{}).Where("user_id = ?", o.UserId).Count(&total).Error
	return
}

func (o *OutboundLogV2) QueryList(pn, ps int, createdAtS, createdAtE int64, outboundStates []int, orderStates []string) (list []*OutboundLogV2, total int64, err error) {
	db := mysql.NewConnV2()

	if createdAtS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createdAtS)
	}
	if createdAtE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", createdAtE)
	}
	if len(outboundStates) > 0 {
		db = db.Where("outbound_state IN (?)", outboundStates)
	}
	if len(orderStates) > 0 {
		db = db.Where("order_state IN (?)", orderStates)
	}

	err = db.Model(&OutboundLogV2{}).Count(&total).Error
	if err != nil {
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (o *OutboundLogV2) GetList() (list []*OutboundLogV2, err error) {
	db := mysql.NewConnV2()
	err = db.Find(&list).Error
	return
}
