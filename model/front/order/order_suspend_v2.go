package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*SuspendOrderV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*SuspendOrderV2)(nil))
}

// 暂停方式  work_order.suspend_state
const (
	OrderSuspendStateDelivery = 100 // 发货暂停
	OrderSuspendStateAll      = 2   // 全部暂停
)

var OrderSuspendStateDescMap = map[int]string{
	OrderSuspendStateDelivery: "发货暂停",
	OrderSuspendStateAll:      "全部暂停",
}

// 全部发货暂停原因  对应 work_order.suspend_reason  suspend_state = 2 时
const (
	AllSuspendReasonOrderCancel = 1  // 订单可能取消
	AllSuspendReasonDesign      = 2  // 设计问题
	AllSuspendReasonsuspicious  = 3  // 顾客身份存疑
	AllSuspendReasonOther       = 10 // 其他
)

var AllSuspendReasonDescMap = map[int]string{
	AllSuspendReasonOrderCancel: "订单可能取消",
	AllSuspendReasonDesign:      "设计问题",
	AllSuspendReasonsuspicious:  "顾客身份存疑",
	AllSuspendReasonOther:       "其他",
}

// 发货暂停原因   对应 work_order.suspend_reason  suspend_state = 100 时
const (
	DeliverySuspendReasonChangeAdress = 1  // 需要修改地址
	DeliverySuspendReasonOther        = 10 // 其他
)

var DeliverySuspendReasonDescMap = map[int]string{
	DeliverySuspendReasonChangeAdress: "需要修改地址",
	DeliverySuspendReasonOther:        "其他",
}

// SuspendOrderV2
type SuspendOrderV2 struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	OrderCode       string    `json:"order_code"`                      // 订单号
	UserId          uint      `json:"user_id"`                         // 用户 id
	OrderTotalMoney int32     `json:"order_total_money"`               // 订单总金额，包括运费及税费等
	PayTime         int64     `json:"pay_time"`                        // 支付时间
	OrderState      int       `json:"order_state" gorm:"default:0"`    // 订单状态，！！！注意，这里的状态是暂停订单时的状态，用于优化筛选
	SuspendState    int       `json:"suspend_state" gorm:"default:0"`  // 暂停方式，2：全部暂停，100：发货暂停
	SuspendReason   int       `json:"suspend_reason" gorm:"default:0"` // 暂停原因，详见对应 全部发货暂停原因 和 发货暂停原因
	ResumeTime      int64     `json:"resume_time"  gorm:"default:0"`   // 订单恢复时间
	Remark          string    `json:"remark" gorm:"type:BLOB;"`        // 备注
}

// TableName 表名
func (s *SuspendOrderV2) TableName() string {
	return "suspend_order"
}

func (s *SuspendOrderV2) GetDetailList(ids []uint) (interface{}, error) {
	db := mysql.NewConnV2().Table(s.TableName())
	db = db.Where("id IN (?)", ids)
	list := make([]*SuspendOrderV2, 0)
	err := db.Find(&list).Error
	return list, err
}

func (s *SuspendOrderV2) DeleteByIds(ids []uint, tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&SuspendOrderV2{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (s *SuspendOrderV2) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(s.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", s.ID).Updates(s).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", s.ID).Update("updated_at", s.UpdatedAt).Error

	return
}

func (s *SuspendOrderV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(SuspendOrderV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return
}

func (s *SuspendOrderV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {
	db := mysql.NewConnV2().Table(s.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (s *SuspendOrderV2) Create(tx ...*gorm2.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Create(s).Error
}

func (s *SuspendOrderV2) Query(hasResumedOrder bool, tx ...*gorm2.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Where("order_code = ?", s.OrderCode)
	if hasResumedOrder {
		db = db.Where("resume_time > 0")
	} else {
		db = db.Where("resume_time = 0")
	}
	return db.First(s).Error
}

func (s *SuspendOrderV2) Update(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(s).Updates(s).Error
	return
}

func (s *SuspendOrderV2) UpdateWithMap(data map[string]interface{}, tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(s).Updates(data).Error
	return
}

func (s *SuspendOrderV2) QueryCountByUserId() (total int64, err error) {
	db := mysql.NewConnV2()
	err = db.Model(&SuspendOrderV2{}).Where("user_id = ?", s.UserId).Count(&total).Error
	return
}

func (s *SuspendOrderV2) QueryList(pn, ps int, createdAtS, createdAtE int64, suspendReasons []int, orderState []string) (list []*SuspendOrderV2, total int64, err error) {
	db := mysql.NewConnV2()

	if createdAtS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", createdAtS)
	}
	if createdAtE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", createdAtE)
	}
	if len(suspendReasons) > 0 {
		db = db.Where("suspend_reason IN (?)", suspendReasons)
	}
	if len(orderState) > 0 {
		db = db.Where("order_state IN (?)", orderState)
	}

	err = db.Model(&SuspendOrderV2{}).Count(&total).Error
	if err != nil {
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (s *SuspendOrderV2) GetList() (list []*SuspendOrderV2, err error) {
	db := mysql.NewConnV2()
	err = db.Find(&list).Error
	return
}
