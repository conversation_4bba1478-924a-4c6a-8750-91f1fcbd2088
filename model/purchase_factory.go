package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"strconv"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*PurchaseFactory)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*PurchaseFactory)(nil))
}

type PurchaseFactory struct {
	ID              uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	IdentifyId      string    `json:"identify_id"      gorm:"unique;not null"` //识别id
	FactoryName     string    `json:"factory_name"       gorm:"not null"`      //工厂名
	DeliveryAddress string    `json:"delivery_address"       gorm:"not null"`  //收货地址
	ShipAddress     string    `json:"ship_address"       gorm:"not null"`      //发货地址
	ProduceAddress  string    `json:"produce_address"       gorm:"not null"`   //生产地址
	//ProduceFactory  string    `json:"produce_factory"       gorm:"not null"`                                  //生产地址
	PurchaseType     string `json:"purchase_type"       gorm:"not null"`                                    //采购类型  string  |分割
	DockingPersons   string `json:"docking_persons"       gorm:"type:BLOB;not null"`                        //对接人 id
	PrincipalId      uint   `json:"principal_id"       gorm:"not null"`                                     //负责同事id
	CooperationState int    `json:"cooperation_state"`                                                      //合作状态
	Person           Person `json:"person"        gorm:"foreignkey:id;association_foreignkey:principal_id"` //负责同事信息
	OwnType          int    `json:"own_type" gorm:"default:1"`                                              // 1 0 外部  2 自有，自有会推送到制衣厂
}

type PurchaseFactoryResp struct {
	//ID              uint      `gorm:"PRIMARY_KEY" json:"ID"`
	IdentifyId  string `json:"identify_id"      gorm:"unique;not null"` //识别id
	FactoryName string `json:"factory_name"       gorm:"not null"`      //工厂名
	//DeliveryAddress string    `json:"delivery_address"       gorm:"not null"`  //收货地址
	//ShipAddress     string    `json:"ship_address"       gorm:"not null"`      //发货地址
	//ProduceFactory  string    `json:"produce_factory"       gorm:"not null"`   //生产地址
	//PurchaseType    string    `json:"purchase_type"       gorm:"not null"`     //采购类型  string  |分割
	//DockingId       string    `json:"docking_persons"       gorm:"not null"`        //对接人 id
	//PrincipalId     uint      `json:"principal_id"       gorm:"not null"`      //负责同事id
}

func (p *PurchaseFactory) TableName() string {
	return "purchase_factory"
}

// 新增
func (p *PurchaseFactory) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&PurchaseFactory{}).Create(p).Error
}

// 根据 ID 查询具体数据
func (p *PurchaseFactory) GetDetailById() error {
	db := mysql.NewConn()
	return db.Table(p.TableName()).Where("id = ?", p.ID).First(p).Error
}

// 查询
func (p *PurchaseFactory) Query() (List []*PurchaseFactory, err error) {
	db := mysql.NewConn().Table(p.TableName())
	if p.IdentifyId != "" {
		db = db.Where("name like ?", "%"+p.IdentifyId+"%")
	}
	err = db.Order("sort asc").Find(&List).Error
	return
}

// 查询所有
func (p *PurchaseFactory) GetList(page, size int) (produceFactoryList []*PurchaseFactory, count int, err error) {

	db := mysql.NewConn().Table(p.TableName())

	if p.FactoryName != "" {
		db = db.Where("factory_name like ?", "%"+p.FactoryName+"%")
	}
	if p.IdentifyId != "" {
		db = db.Where("identify_id like ?", "%"+p.IdentifyId+"%")
	}
	if p.PrincipalId > 0 {
		db = db.Where("principal_id = ?", p.PrincipalId)
	}

	err = db.Count(&count).Error

	err = db.Preload("Person").Offset((page - 1) * size).Limit(size).Find(&produceFactoryList).Error
	return
}

// 生成新采购工厂ID
func (p *PurchaseFactory) GerateNewFactoryId(tx ...*gorm.DB) error {

	tmp := make([]*ProduceFactory, 0)
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}

	err := db.Table(p.TableName()).Limit(1).Order("id desc").Find(&tmp).Error
	if err != nil {
		return err
	}

	//如果有查到，则在最后一条的基础上继续往下加，否则从S0000开始创建
	if len(tmp) > 0 {
		n, _ := strconv.ParseInt(tmp[0].IdentifyId[1:5], 10, 64)
		p.IdentifyId = fmt.Sprintf("C%04d", int(n)+1)
	} else {
		p.IdentifyId = "C0000"
	}
	return nil
}

// 精准查询，根据identify_id
func (p *PurchaseFactory) QueryDetailByIdentifyID() (List []PurchaseFactory, err error) {
	db := mysql.NewConn().Table(p.TableName()).Preload("Person")
	if p.IdentifyId != "" {
		db = db.Where("identify_id = ?", p.IdentifyId)
	}
	err = db.Find(&List).Error
	return
}

// 更新
func (p *PurchaseFactory) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Model(&PurchaseFactory{}).Where("identify_id = ?", p.IdentifyId).Update(p).First(p).Error
	if err != nil {
		return err
	}
	return err
}

func (c *PurchaseFactory) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*PurchaseFactory, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *PurchaseFactory) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&PurchaseFactory{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *PurchaseFactory) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *PurchaseFactory) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(PurchaseFactory)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *PurchaseFactory) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

type FactoryGroup struct {
	PurchaseID  uint   `json:"purchase_id"`                        //采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` //工厂名
	OwnType     int    `json:"own_type" gorm:"default:1"`          // 1 0 外部  2 自有，自有会推送到制衣厂

}

// 根据 IDList 查询工厂 只输出 FactoryGroup 相关内容
func (p *PurchaseFactory) GetFactoryGroupByIdList(idList []uint) (list []*FactoryGroup, err error) {
	db := mysql.NewConn().Table(p.TableName()).Select("id as purchase_id, factory_name, own_type")
	err = db.Where("id IN (?)", idList).Find(&list).Error
	return
}
