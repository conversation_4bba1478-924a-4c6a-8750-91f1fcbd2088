package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	// 注册表的时候，指定表的options
	// 如果没有指定，默认为`mysql.OPTION_USE_INNODB_ENGINE`
	mysql.RegisterTable((*ToolVersion)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*ToolVersion)(nil))
}

type ToolVersion struct {
	ID          int64     `json:"id"gorm:"PRIMARY_KEY"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Version     string    `json:"version"`
	Description string    `json:"description"  gorm:"type:BLOB;"`
	FileName    string    `json:"file_name"`
	DownloadUrl string    `json:"download_url"`
	ServerAddr  string    `json:"server_addr"`
}

func (t *ToolVersion) TableName() string {
	return "tool_version"
}

// 创建
func (t *ToolVersion) Create(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Table(t.TableName()).Create(t).Error
	return
}

//查询所有
/*func (t *ToolVersion) GetList(size int) (toolVersionList []*ToolVersion, count int, err error) {

	db := mysql.NewConn().Table(t.TableName())

	err = db.Count(&count).Error

	// 这里可以加筛选条件

	err = db.Order("id desc").Limit(size).Find(&toolVersionList).Error
	return
}*/

// 查询翻页
func (t *ToolVersion) GetList(page, size int) (toolVersionList []*ToolVersion, count int, err error) {

	db := mysql.NewConn().Table(t.TableName())

	err = db.Count(&count).Error

	// 这里可以加筛选条件

	err = db.Order("id desc").Offset((page - 1) * size).Limit(size).Find(&toolVersionList).Error
	return
}

func (t *ToolVersion) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(t.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ToolVersion, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *ToolVersion) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ToolVersion{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (t *ToolVersion) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(t.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", t.ID).Updates(t).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", t.ID).Update("updated_at", t.UpdatedAt).Error

	return
}

func (t *ToolVersion) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ToolVersion)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[uint(detailObj.ID)] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (t *ToolVersion) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(t.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
