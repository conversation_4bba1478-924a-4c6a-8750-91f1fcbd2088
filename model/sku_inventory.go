package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

const (
	LIGHT_COLOR = "0" //浅色
	DARK_COLOR  = "2" //深色
)

func init() {
	mysql.RegisterTable((*SkuInventory)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*SkuInventory)(nil))
}

type SkuInventory struct {
	ID              uint        `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt       time.Time   `json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`
	ProductSizeID   int         `json:"product_size_id"`                   //商品尺码表id
	ProductID       uint        `json:"product_id"`                        //spu id
	Spu             string      `json:"spu"`                               //spu
	Sku             string      `json:"sku" gorm:"unique_index;not null;"` //sku 唯一
	SpuMainClassify string      `json:"spu_main_classify"`                 //SPU的主分类
	ColorID         string      `json:"color_id"`
	ColorName       string      `json:"color_name"` //颜色名称
	ColorNum        string      `json:"color_num"`  //色号
	ColorType       string      `json:"color_type"` //颜色类型 0浅色  2深色
	SizeID          string      `json:"size_id"`
	Quality         int         `json:"quality"`   //库存
	Threshold       int         `json:"threshold"` //预警值
	ProductSize     ProductSize `json:"product_size" gorm:"foreignkey:id;association_foreignkey:product_size_id"`
	ProductInfo     ProductInfo `json:"product_info"  gorm:"foreignkey:id;association_foreignkey:product_id"`
}

type SpuList struct {
	ProductID uint `json:"product_id"` //spu id
}

func (s *SkuInventory) TableName() string {
	return "sku_inventory"
}

// 新增sku
func (s *SkuInventory) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 查找sku
func (s *SkuInventory) GetSku() error {
	db := mysql.NewConn().Table(s.TableName()).Preload("ProductSize").Preload("ProductInfo").Preload("ProductInfo.Supply")
	return db.Where(s).First(s).Error
}

// 更改sku库存
func (s *SkuInventory) UpdateSkuQuality(status, num int, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&SkuInventory{}).Where("sku = ?", s.Sku)

	if status == 1 {
		err := db.UpdateColumn("quality", gorm.Expr("quality + ?", num)).Error
		return err
	}
	if status == 2 {
		err := db.UpdateColumn("quality", gorm.Expr("quality - ?", num)).Error
		return err
	}
	//更新告警阈值
	if status == 3 {
		err := db.UpdateColumn("threshold", num).Error
		return err
	}

	return nil
}

func (s *SkuInventory) MinusSkuQuality() error {
	return mysql.NewConn().Model(&SkuInventory{}).Where("sku = ?", s.Sku).
		UpdateColumn("quality", gorm.Expr("quality - ?", 1)).
		First(s).Error
}

// 筛选查找SPU List
func (s *SkuInventory) GetSpuList() (list []*SpuList, err error) {
	err = mysql.NewConn().Table(s.TableName()).Select("product_id").Group("product_id").Where("quality < ?", s.Quality).Find(&list).Error
	return
}

func (s *SkuInventory) GetQualityByPID() (list []*SkuInventory, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("product_id = ?", s.ProductID).Find(&list).Error
	return
}

func (s *SkuInventory) GetQualityBySpu() (list []*SkuInventory, err error) {
	err = mysql.NewConn().Table(s.TableName()).Where("spu = ?", s.Spu).Preload("ProductSize").Find(&list).Error
	return
}

func (s *SkuInventory) PutColorType(tx *gorm.DB, colorType string) error {
	return tx.Model(&SkuInventory{}).Where("product_id = ? AND color_id = ?", s.ProductID, s.ColorID).Update("color_type", colorType).Error
}

func (c *SkuInventory) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*SkuInventory, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *SkuInventory) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&SkuInventory{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *SkuInventory) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *SkuInventory) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(SkuInventory)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *SkuInventory) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
