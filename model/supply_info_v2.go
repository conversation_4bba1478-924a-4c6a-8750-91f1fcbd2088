package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*SupplyInfoV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// SupplyInfoV2 供应链信息V2版本，支持商品物流和工厂信息管理
type SupplyInfoV2 struct {
	ID                  uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt           time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	ProductId           uint      `json:"product_id" gorm:"uniqueIndex;not null;index"` // 关联商品ID
	PackSize            string    `json:"pack_size" gorm:"size:128"`                    // 包装尺寸 长宽高顺序排列|分割
	ShipAddress         string    `json:"ship_address" gorm:"size:256"`                 // 发货地址
	PackWeight          string    `json:"pack_weight" gorm:"size:64"`                   // 包装重量
	RealWeight          string    `json:"real_weight" gorm:"size:64"`                   // 真实重量 单位kg
	ProduceFactory      string    `json:"produce_factory" gorm:"size:128;index"`        // 生产工厂ID
	PurchaseFactoryId   string    `json:"purchase_factory_id" gorm:"size:128;index"`    // 采购工厂ID
	PurchaseFactoryName string    `json:"purchase_factory_name" gorm:"size:256"`        // 采购工厂名
	ProduceFactoryName  string    `json:"produce_factory_name" gorm:"size:256"`         // 生产工厂名
}

func (s *SupplyInfoV2) TableName() string {
	return "supply_info"
}

// Create 创建供应链信息记录
func (s *SupplyInfoV2) Create(db ...mysql.DBInterface) error {
	if len(db) > 0 {
		return db[0].Create(s).Error()
	}
	return mysql.NewUnifiedDB().Create(s).Error()
}

// Update 更新供应链信息
func (s *SupplyInfoV2) Update(db ...mysql.DBInterface) error {
	if len(db) > 0 {
		return db[0].Model(s).Updates(s).Error()
	}
	return mysql.NewUnifiedDB().Model(s).Updates(s).Error()
}

// UpdateSupplyInfo 更新供应链信息，如果不存在则创建
func (s *SupplyInfoV2) UpdateSupplyInfo(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	// 尝试更新
	result := dbConn.Model(s).Where("product_id = ?", s.ProductId).Updates(s)
	if result.Error() != nil {
		return result.Error()
	}

	// 如果没有影响任何行，说明记录不存在，创建新记录
	if result.RowsAffected() == 0 {
		return dbConn.Create(s).Error()
	}

	return nil
}

// QueryByProductId 根据产品ID查询供应链信息
func (s *SupplyInfoV2) QueryByProductId(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	return dbConn.Model(s).Where("product_id = ?", s.ProductId).First(s).Error()
}

// GetDetailList 批量获取供应链信息详情
func (s *SupplyInfoV2) GetDetailList(ids []uint, db ...mysql.DBInterface) (interface{}, error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	if len(ids) == 0 {
		return []*SupplyInfoV2{}, nil
	}

	var list []*SupplyInfoV2
	err := dbConn.Model(s).Where("id IN ?", ids).Find(&list).Error()
	return list, err
}

// DeleteByIds 批量删除供应链信息
func (s *SupplyInfoV2) DeleteByIds(ids []uint, db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	if len(ids) == 0 {
		return nil
	}

	return dbConn.Where("id IN ?", ids).Delete(&SupplyInfoV2{}).Error()
}

// UpdateWithZero 工厂本地专用，不更新updated_at字段的更新方法
func (s *SupplyInfoV2) UpdateWithZero(db ...mysql.DBInterface) error {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	// 首先更新除updated_at外的所有字段
	err := dbConn.Model(s).
		Session(&mysql.Session{SkipHooks: true}).
		Select("*").
		Omit("updated_at").
		Where("id = ?", s.ID).
		Updates(s).Error()
	if err != nil {
		return err
	}

	// 然后单独更新updated_at字段
	return dbConn.Model(s).Where("id = ?", s.ID).Update("updated_at", s.UpdatedAt).Error()
}

// CreateOrUpdate 创建或更新供应链信息（用于同步）
func (s *SupplyInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("CreateOrUpdate detail data:", string(detailData))

	detailObj := new(SupplyInfoV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		return ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
	}

	switch idMapType[detailObj.ID] {
	case 1:
		// 创建新记录
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	case 2:
		// 更新现有记录
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
	}

	return nil
}

// GetVerifyHead 获取校验头信息
func (s *SupplyInfoV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64, db ...mysql.DBInterface) (list []*VerifyHead, count int64, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(s).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		query = query.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		query = query.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		query = query.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		query = query.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 && pn > 0 && ps > 0 {
		offset := (pn - 1) * ps
		query = query.Offset(offset).Limit(ps)
	}

	err = query.Find(&list).Error()
	return
}

// QueryList 查询供应链信息列表
func (s *SupplyInfoV2) QueryList(pn, ps int, db ...mysql.DBInterface) (list []*SupplyInfoV2, count int64, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(s)

	// 添加查询条件
	if s.ProductId > 0 {
		query = query.Where("product_id = ?", s.ProductId)
	}
	if s.ProduceFactory != "" {
		query = query.Where("produce_factory = ?", s.ProduceFactory)
	}
	if s.PurchaseFactoryId != "" {
		query = query.Where("purchase_factory_id = ?", s.PurchaseFactoryId)
	}

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	if pn > 0 && ps > 0 {
		offset := (pn - 1) * ps
		query = query.Offset(offset).Limit(ps)
	}

	err = query.Order("id desc").Find(&list).Error()
	return
}

// IsExist 检查供应链信息是否存在
func (s *SupplyInfoV2) IsExist(db ...mysql.DBInterface) (bool, error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	var count int64
	query := dbConn.Model(s)

	if s.ID > 0 {
		query = query.Where("id = ?", s.ID)
	} else if s.ProductId > 0 {
		query = query.Where("product_id = ?", s.ProductId)
	} else {
		return false, fmt.Errorf("insufficient conditions for existence check")
	}

	err := query.Count(&count).Error()
	return count > 0, err
}

// GetByProductIds 根据产品ID列表批量获取供应链信息
func (s *SupplyInfoV2) GetByProductIds(productIds []uint, db ...mysql.DBInterface) (list []*SupplyInfoV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	if len(productIds) == 0 {
		return []*SupplyInfoV2{}, nil
	}

	err = dbConn.Model(s).Where("product_id IN ?", productIds).Find(&list).Error()
	return
}

// GetByFactoryId 根据工厂ID获取供应链信息列表
func (s *SupplyInfoV2) GetByFactoryId(factoryId string, factoryType string, db ...mysql.DBInterface) (list []*SupplyInfoV2, err error) {
	var dbConn mysql.DBInterface
	if len(db) > 0 {
		dbConn = db[0]
	} else {
		dbConn = mysql.NewUnifiedDB()
	}

	query := dbConn.Model(s)

	switch factoryType {
	case "produce":
		query = query.Where("produce_factory = ?", factoryId)
	case "purchase":
		query = query.Where("purchase_factory_id = ?", factoryId)
	default:
		query = query.Where("produce_factory = ? OR purchase_factory_id = ?", factoryId, factoryId)
	}

	err = query.Find(&list).Error()
	return
}
