package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*LogisticsV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*LogisticsV2)(nil))
}

// LogisticsV2 物流信息V2模型，处理高风险API迁移
type LogisticsV2 struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	ISOCode   string    `json:"iso_code"` // 国家二字码 （ISO 3166-2）
	EnName    string    `json:"en_name"`  // 英文名
	CName     string    `json:"c_name"`   // 中文名 中国 惯用名

	PKShipping  string `json:"pk_shipping"  gorm:"type:BLOB;"` // 普快 json
	KYShipping  string `json:"ky_shipping"  gorm:"type:BLOB;"` // 快运 json
	TKShipping  string `json:"tk_shipping"  gorm:"type:BLOB;"` // 特快 json
	RemoteCount int    `json:"remote_count"`                   // 偏远地区数量
}

// TableName 返回表名
func (l *LogisticsV2) TableName() string {
	return "logistics"
}

// Create 新建物流信息 - 使用统一接口
func (l *LogisticsV2) Create(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(l.TableName()).Create(l).Error()
}

// Update 更新物流信息 - 使用统一接口
func (l *LogisticsV2) Update(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(l).Where("id = ?", l.ID).Updates(l).Error()
}

// UpdateWithMap 根据map更新 - 使用统一接口
func (l *LogisticsV2) UpdateWithMap(data map[string]interface{}, tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(l).Where("id = ?", l.ID).UpdatesMap(data).Error()
}

// IsExist 判断国家是否存在 - 使用统一接口
func (l *LogisticsV2) IsExist(tx ...mysql.DBInterface) (bool, error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	var count int64
	err := db.Table(l.TableName()).Where("iso_code = ?", l.ISOCode).Count(&count).Error()
	if err != nil {
		return false, err
	}
	return count == 1, nil
}

// GetInfo 根据ISO代码获取物流信息 - 使用统一接口
func (l *LogisticsV2) GetInfo(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Table(l.TableName()).Where("iso_code = ?", l.ISOCode).First(l).Error()
}

// GetList 获取物流配置列表 - 使用统一接口
func (l *LogisticsV2) GetList(pn, ps int, shipping, carrier string, tx ...mysql.DBInterface) (list []*LogisticsV2, count int64, err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	query := db.Table(l.TableName())

	// 偏远地区筛选
	switch l.RemoteCount {
	case 0:
		// 不筛选
	case 1:
		query = query.Where("remote_count > 0") // 筛选有偏远地区的
	case 2:
		query = query.Where("remote_count = 0") // 筛选无偏远地区的
	}

	// 中文名筛选
	if l.CName != "" {
		query = query.Where("c_name LIKE ?", "%"+l.CName+"%")
	}

	// 物流方式和承运商筛选
	if shipping == "" && carrier != "" {
		query = query.Where("pk_shipping LIKE ? OR ky_shipping LIKE ? OR tk_shipping LIKE ?",
			"%"+carrier+"%", "%"+carrier+"%", "%"+carrier+"%")
	}
	if shipping != "" && carrier != "" {
		switch shipping {
		case "PK", "pk":
			query = query.Where("pk_shipping LIKE ?", "%"+carrier+"%")
		case "KY", "ky":
			query = query.Where("ky_shipping LIKE ?", "%"+carrier+"%")
		case "TK", "tk":
			query = query.Where("tk_shipping LIKE ?", "%"+carrier+"%")
		}
	}
	if shipping != "" && carrier == "" {
		switch shipping {
		case "PK", "pk":
			query = query.Where("pk_shipping != ?", "{\"is_open\":false}")
		case "KY", "ky":
			query = query.Where("ky_shipping != ?", "{\"is_open\":false}")
		case "TK", "tk":
			query = query.Where("tk_shipping != ?", "{\"is_open\":false}")
		}
	}

	// 获取总数
	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	// 分页查询
	query = query.Order("id desc")
	err = query.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error()
	return
}

// QueryList 获取所有物流配置列表 - 使用统一接口
func (l *LogisticsV2) QueryList(tx ...mysql.DBInterface) (list []*LogisticsV2, err error) {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	err = db.Table(l.TableName()).Find(&list).Error()
	return
}

// GetDetailList 根据ID列表获取详细信息 - 实现SyncInterface接口
func (l *LogisticsV2) GetDetailList(ids []uint) (interface{}, error) {
	db := mysql.NewUnifiedDB()

	list := make([]*LogisticsV2, 0)
	err := db.Table(l.TableName()).Where("id IN (?)", ids).Find(&list).Error()

	return list, err
}

// DeleteByIds 根据ID列表删除 - 实现SyncInterface接口，使用统一接口
func (l *LogisticsV2) DeleteByIds(ids []uint, param ...interface{}) error {

	var db mysql.DBInterface

	if len(param) > 0 {
		db = param[0].(mysql.DBInterface)
	} else {
		db = mysql.NewUnifiedDB()
	}
	return db.Model(l).Where("id IN (?)", ids).Delete(l).Error()
}

// UpdateWithZero 零值更新支持 - 使用统一接口，高风险API迁移
func (l *LogisticsV2) UpdateWithZero(tx ...mysql.DBInterface) error {
	var db mysql.DBInterface
	if len(tx) != 0 {
		db = tx[0]
	} else {
		db = mysql.NewUnifiedDB()
	}

	// GORM v2: 使用UpdatesMap替代Session方式
	updateMap := map[string]interface{}{
		"id":           l.ID,
		"created_at":   l.CreatedAt,
		"iso_code":     l.ISOCode,
		"en_name":      l.EnName,
		"c_name":       l.CName,
		"pk_shipping":  l.PKShipping,
		"ky_shipping":  l.KYShipping,
		"tk_shipping":  l.TKShipping,
		"remote_count": l.RemoteCount,
	}

	err := db.Model(l).Where("id = ?", l.ID).UpdatesMap(updateMap).Error()
	if err != nil {
		return err
	}

	// 单独更新updated_at字段
	return db.Model(l).Where("id = ?", l.ID).UpdateSingle("updated_at", l.UpdatedAt).Error()
}

// CreateOrUpdate 创建或更新物流配置 - 实现SyncInterface接口
func (l *LogisticsV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(LogisticsV2)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		return ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		return ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
	}
	return nil
}

// GetVerifyHead 获取校验头信息 - 实现SyncInterface接口，使用统一接口
func (l *LogisticsV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {
	db := mysql.NewUnifiedDB()

	query := db.Table(l.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		query = query.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		query = query.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		query = query.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		query = query.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = query.Count(&count).Error()
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		query = query.Offset((pn - 1) * ps).Limit(ps)
	}

	err = query.Find(&list).Error()
	return
}
