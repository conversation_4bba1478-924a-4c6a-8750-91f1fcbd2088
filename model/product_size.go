package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ProductSize)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*ProductSize)(nil))
}

type ProductSize struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	ProductID uint      `json:"product_id" gorm:"index"`    //关联商品 id
	SizeID    string    `json:"size_id"    gorm:"not null"` //尺码id
	SizeName  string    `json:"size_name"  gorm:"not null"` //尺码名称
	Sort      int       `json:"sort"`                       //排序 继承尺码的排序

	OutputWidth  int    `json:"output_width"`  //输出高
	OutputHeight int    `json:"output_height"` //输出宽
	Scaling      string `json:"scaling"`       //对应缩放比例
	BaseBoard    string `json:"base_board"`    //0大 2标准
	Price        int    `json:"price"`         //价格 美分
}

type OutputSize struct {
	ID       uint   `json:"id"`
	SizeName string `json:"size_name"` //尺码名称
	SizeID   string `json:"size_id"`   //尺码id
	Scaling  string `json:"scaling"`   //对应缩放比例
	//OutputWidth  int    `json:"output_width"`               //输出高
	//OutputHeight int    `json:"output_height"`              //输出宽
	BaseBoard string `json:"base_board"` //0大 2标准
}

func (p *ProductSize) TableName() string {
	return "product_size"
}

// 新增size绑定
func (p *ProductSize) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

func (p *ProductSize) UpdatePrice(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&ProductSize{}).Where("product_id = ? AND size_id = ?", p.ProductID, p.SizeID).Update("price", p.Price).Error
}

// 根据PID获取size列表
func (p *ProductSize) GetListByPID(pid uint) (list []*ProductSize, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", pid).Order("sort asc").
		Find(&list).Error
	return
}

// 获取Size输出格式
func (p *ProductSize) GetOutputSize(pid uint) (list []*OutputSize, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", pid).Order("sort asc").
		Find(&list).Error
	return
}

func (p *ProductSize) PutOutputSize(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ProductSize{}).Where("id = ? AND product_id = ?", p.ID, p.ProductID).Updates(p).Error
	return
}

// 根据 size id 批量查询
func (p *ProductSize) BatchQury(productID uint, sizeIDs []string) (list []*OutputSize, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ? AND size_id in (?)", productID, sizeIDs).Order("sort asc").Find(&list).Error
	return
}

func (c *ProductSize) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ProductSize, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *ProductSize) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ProductSize{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ProductSize) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ProductSize) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ProductSize)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ProductSize) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
