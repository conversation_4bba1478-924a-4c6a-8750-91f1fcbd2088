package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Logistics)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*Logistics)(nil))
}

type Logistics struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	ISOCode   string    `json:"iso_code"` // 国家二字码 （ISO 3166-2）
	EnName    string    `json:"en_name"`  // 英文名
	CName     string    `json:"c_name"`   // 中文名 中国 惯用名

	PKShipping  string `json:"pk_shipping"  gorm:"type:BLOB;"` // 普快 json
	KYShipping  string `json:"ky_shipping"  gorm:"type:BLOB;"` // 快运 json
	TKShipping  string `json:"tk_shipping"  gorm:"type:BLOB;"` // 特快 json
	RemoteCount int    `json:"remote_count"`                   // 偏远地区数量
}

func (l *Logistics) TableName() string {
	return "logistics"
}

// 新建物流信息
func (l *Logistics) Create(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(l.TableName()).Create(l).Error
}

// 更新物流信息
func (l *Logistics) Update(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	//return db.Table(s.TableName()).Where("product_id = ?", s.ProductID).Updates(s).Error
	return db.Model(l).Where("id = ?", l.ID).Updates(l).First(l).Error
}

// 根据map更新
func (l *Logistics) UpdateWithMap(data map[string]interface{}, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&Logistics{}).Where("id = ?", l.ID).Updates(data).Error
}

// 判断国家是否存在
func (l *Logistics) IsExist() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(l.TableName()).Where("iso_code = ?", l.ISOCode).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count == 1, nil
}

func (l *Logistics) GetInfo() error {
	return mysql.NewConn().Table(l.TableName()).Where("iso_code = ?", l.ISOCode).First(l).Error
}

func (l *Logistics) GetList(pn, ps int, shipping, carrier string) (list []*Logistics, count int, err error) {

	db := mysql.NewConn().Table(l.TableName())

	switch l.RemoteCount {
	case 0:
	case 1:
		db = db.Where("remote_count > 0 ") // 筛选有偏远地区的
	case 2:
		db = db.Where("remote_count = 0 ") // 筛选有偏远地区的
	}

	if l.CName != "" {
		db = db.Where("c_name LIKE ?", "%"+l.CName+"%")
	}
	if shipping == "" && carrier != "" {
		db = db.Where("pk_shipping LIKE ? OR ky_shipping LIKE ? OR tk_shipping LIKE ?", "%"+carrier+"%", "%"+carrier+"%", "%"+carrier+"%")
	}
	if shipping != "" && carrier != "" {
		switch shipping {
		case "PK", "pk":
			db = db.Where("pk_shipping LIKE ?", "%"+carrier+"%")
		case "KY", "ky":
			db = db.Where("ky_shipping LIKE ?", "%"+carrier+"%")
		case "TK", "tk":
			db = db.Where("tk_shipping LIKE ?", "%"+carrier+"%")
		}
	}
	if shipping != "" && carrier == "" {
		switch shipping {
		case "PK", "pk":
			db = db.Where("pk_shipping != ?", "{\"is_open\":false}")
		case "KY", "ky":
			db = db.Where("ky_shipping != ?", "{\"is_open\":false}")
		case "TK", "tk":
			db = db.Where("tk_shipping != ?", "{\"is_open\":false}")
		}
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	db = db.Order("id desc")
	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (c *Logistics) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Logistics, 0)

	err := db.Find(&list).Error

	return list, err
}

func (l *Logistics) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Logistics{}).Where("id IN (?)", ids).Delete(l).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Logistics) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Logistics) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Logistics)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Logistics) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 获取列表
func (l *Logistics) QueryList() (list []*Logistics, err error) {

	db := mysql.NewConn().Table(l.TableName())

	err = db.Find(&list).Error
	return
}
