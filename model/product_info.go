package model

import (
	"encoding/json"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"strings"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ProductInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*ProductInfo)(nil))
}

// 商品信息表
type ProductInfo struct {
	ID                  uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
	Spu                 string    `json:"spu" gorm:"unique;not null"`            //spu
	Name                string    `json:"name"`                                  //商品名称
	Classify            string    `json:"classify"`                              //商品主分类
	OtherClassify       string    `json:"other_classify"`                        //商品其他分类
	Brand               string    `json:"brand"`                                 //所属品牌id  可筛选
	ColorId             string    `json:"color_id"`                              //颜色id 使用|分割
	LightColor          string    `json:"light_color"`                           //浅色颜色ID 使用|分割
	DarkColor           string    `json:"dark_color"`                            //深色颜色ID 使用|分割
	SizeId              string    `json:"size_id"`                               //尺码id 使用|分割
	OutputSize          string    `json:"output_size"`                           //标准输出尺码ID
	EmbOutputSize       string    `json:"emb_output_size"`                       //刺绣标准输出尺码ID
	PrintProcess        string    `json:"print_process"`                         //打印工艺 使用|分割
	PrintableSurface    string    `json:"printable_surface" gorm:"size:1024"`    //可打印面 使用|分割  可筛选
	Summarize           string    `json:"summarize" gorm:"size:1024"`            //款式概述
	FeatureIntroduction string    `json:"feature_introduction" gorm:"size:1024"` //特点介绍
	Material            string    `json:"material" gorm:"size:1024"`             //面料材质  可筛选
	Weight              string    `json:"weight" gorm:"size:1024"`               //面料克重
	Technology          string    `json:"technology"  gorm:"size:1024"`          //成衣工艺
	OtherDescription    string    `json:"other_description" gorm:"size:1024"`    //其他说明
	Maintain            string    `json:"maintain" gorm:"size:1024"`             //保养说明
	Tags                string    `json:"tags" gorm:"size:1024"`                 //标签 使用|分割
	Description         string    `json:"description" gorm:"type:BLOB;"`         //整体描述
	ChromaBad           string    `json:"chroma_bad"`                            // 色差问题
	DesignImg           string    `json:"design_img"`                            // 设计图
	EntityImg           string    `json:"entity_img"`                            // 实物图
	DeliveryTime        string    `json:"delivery_time"`                         // 平均发货时间
	Status              int       `json:"status"`                                // 1待上架  2上架  3淘汰
	SpecialStatus       int       `json:"special_status"`                        // 0无  1热卖
	Sort                int       `json:"sort"`                                  // 商品排序 越大越前
	DesignStatus        int       `json:"design_status"`                         // 1待填充  2已完成
	SizeImgUrl          string    `json:"size_img_url"`                          // 尺码表图片 url

	MetricSystem  string `json:"metric_system"   gorm:"type:BLOB;"` //公制
	EnglishSystem string `json:"english_system"  gorm:"type:BLOB;"` //英制

	ImgList  []*ProductImage `json:"img_list" gorm:"foreignkey:product_id;association_foreignkey:id"`  //关联图片信息
	SizeList []*ProductSize  `json:"size_list" gorm:"foreignkey:product_id;association_foreignkey:id"` //关联尺码信息 价格等
	Supply   SupplyInfo      `json:"supply"  gorm:"foreignkey:product_id;association_foreignkey:id"`   //关联供应信息

	LowPrice int `json:"low_price"` // 最低价格

	LightColorSet  string `json:"light_color_set"  gorm:"type:BLOB;"` // 打印设置
	DarkColorSet   string `json:"dark_color_set"   gorm:"type:BLOB;"` // 打印设置
	Mockup         string `json:"mockup" gorm:"type:BLOB;"`           // 模板素材图
	MockupCount    int    `json:"mockup_count"`                       // 模板素材图数量
	TemplateDesign string `json:"template_design"  gorm:"type:BLOB;"` // 模板详情的设计展示信息

	MetaDescription string `json:"meta_description" gorm:"type:BLOB;"` // seo mate 描述
	SeoTitle        string `json:"seo_title"`                          // seo 标题
}

type LightColorSetJson struct {
	ColorMultiple  bool `json:"color_multiple"`   //彩色的复合路径打印（bool）
	ColorInkVolume int  `json:"color_ink_volume"` //油墨量(1~10)
	DoublePrinting int  `json:"double_printing"`  //2次打印(0~3, 0代表打印一次，1~3代表打印两次)
}

type DarkColorSetJson struct {
	ColorMultiple      bool `json:"color_multiple"`       //彩色符合路径打印
	Highlight          int  `json:"highlight"`            //高光（1-9）
	Mask               int  `json:"mask"`                 //遮光（1-5）
	TwoLayerWhite      bool `json:"two_layer_white"`      //白色分次打印
	IntervalWhiteWhite int  `json:"interval_white_white"` //白色分次打印间隔 （0-60s）
	WhiteColorPause    bool `json:"white_color_pause"`    //白色/彩色各个别打印
	IntervalWhiteColor int  `json:"interval_white_color"` //白色/彩色各个别打印间隔 （0-60s）
	Choke              int  `json:"choke"`                //白色油墨削减范围 （0-10）
}

func (p *ProductInfo) TableName() string {
	return "product_info"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (p *ProductInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(p.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (p *ProductInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(ProductInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (p *ProductInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(p.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", p.ID).Updates(p).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", p.ID).Update("updated_at", p.UpdatedAt).Error

	return
}

func (p *ProductInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(p.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ProductInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *ProductInfo) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ProductInfo{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

//修改商品信息
//根据筛选条件 分页获取 list

// 新建商品
func (p *ProductInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

func (p *ProductInfo) UpdateDetails(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(p).Updates(p).First(p).Error
}

// 更新模板素材
func (p *ProductInfo) UpdateMockup(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(p).Where("id = ?", p.ID)

	updateMap := make(map[string]interface{})
	updateMap["mockup"] = p.Mockup
	updateMap["mockup_count"] = p.MockupCount

	return db.Updates(updateMap).First(p).Error
}

// 更新模板素材
func (p *ProductInfo) UpdateMockupBySpu(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(p).Where("spu = ?", p.Spu)
	updateMap := make(map[string]interface{})
	updateMap["mockup"] = p.Mockup
	updateMap["mockup_count"] = p.MockupCount
	return db.Updates(updateMap).First(p).Error

}

func (p *ProductInfo) UpdateMetricAndEnglish(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(p).Where("id = ?", p.ID)
	updateMap := make(map[string]interface{})
	updateMap["metric_system"] = p.MetricSystem
	updateMap["english_system"] = p.EnglishSystem

	return db.Updates(updateMap).First(p).Error
}

// 根据id获取商品详情
func (p *ProductInfo) GetInfoById(preload ...string) error {
	db := mysql.NewConn().Table(p.TableName())
	if len(preload) != 0 {
		for _, v := range preload {
			db = db.Preload(v)
		}
	}
	if p.ID != 0 {
		db = db.Where("id = ?", p.ID)
	}

	if len(p.Spu) == 7 {
		db = db.Where("spu = ?", p.Spu)
	}

	return db.First(p).Error
}

func (p *ProductInfo) GetDetails() error {
	db := mysql.NewConn().Table(p.TableName())
	db = db.Preload("SizeList").Preload("Supply").Preload("ImgList", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_display = ?", true).Order("sort asc")
	})

	if p.ID != 0 {
		db = db.Where("id = ?", p.ID)
	}

	if len(p.Spu) == 7 {
		db = db.Where("spu = ?", p.Spu)
	}

	return db.First(p).Error
}

type ListSearch struct {
	CreatedAtS int    `form:"created_at_s"` //创建时间开始
	CreatedAtE int    `form:"created_at_e"` //创建时间结束
	Page       int    `form:"page"`
	Size       int    `form:"size"`
	SPUName    string `json:"spu_name"`
	Pod        bool
	BrandList  string
	SortStatus int
	Name       string `json:"name"`
}

// 根据筛选条件 分页获取 list
func (p *ProductInfo) List(coon ListSearch, idList ...[]uint) (list []*ProductInfo, count int, err error) {

	db := mysql.NewConn().Table(p.TableName())

	if p.PrintableSurface != "" {
		printableSurface := strings.Split(p.PrintableSurface, "|")
		for _, v := range printableSurface {
			db = db.Where("printable_surface Like ?", "%"+v+"%")
		}
	}

	if p.SizeId != "" {
		sizeList := strings.Split(p.SizeId, "|")
		for _, v := range sizeList {
			db = db.Where("size_id Like ?", "%"+v+"%")
		}
	}

	if coon.SPUName != "" {
		db = db.Where("name LIKE ?", "%"+coon.SPUName+"%")
	}

	if p.Spu != "" {
		db = db.Where("spu = ?", p.Spu)
	}
	if p.Brand != "" {
		db = db.Where("brand = ?", p.Brand)
	}
	if p.Name != "" {
		db = db.Where("name Like ?", "%"+p.Name+"%")
	}
	if p.Status != 0 {
		db = db.Where("status = ?", p.Status)
	}
	if p.SpecialStatus != 0 {
		db = db.Where("special_status = ?", p.SpecialStatus)
	}
	if p.DesignStatus != 0 {
		db = db.Where("design_status = ?", p.DesignStatus)
	}
	if coon.CreatedAtS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", coon.CreatedAtS)
	}
	if coon.CreatedAtE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", coon.CreatedAtE)
	}
	if len(coon.Name) > 0 {
		db = db.Where("name LIKE ?", fmt.Sprintf("%%%s%%", coon.Name))
	}

	if len(idList) != 0 {
		db = db.Where("id IN (?)", idList[0])
	}

	if coon.BrandList != "" {
		brandList := strings.Split(coon.BrandList, "|")
		db = db.Where("brand in (?)", brandList)
		//for _, v := range brandList {
		//	db = db.Or("brand = ?", v)
		//}
	}

	if coon.Pod {
		db = db.Where("status = ?", 2)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	//预加载图片信息，根据sort升序取第一位可展示图片为首图
	db = db.Preload("ImgList", func(db *gorm.DB) *gorm.DB {
		return db. /*Where("is_display = ?", true).*/ Order("sort asc")
	})

	//预加载尺码价格信息
	db = db.Preload("SizeList", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	switch coon.SortStatus {
	case 1:
		db = db.Order("sort desc")
	case 2:
		db = db.Order("created_at desc")
	case 3:
		db = db.Order("low_price desc")
	case 4:
		db = db.Order("low_price asc")
	default:
		db = db.Order("sort desc")
	}

	err = db.Offset((coon.Page - 1) * coon.Size).Limit(coon.Size).Find(&list).Error
	return
}

// 根据主分类获取所属商品数
func (p *ProductInfo) GetCountByClassify(Classify string) (int, error) {
	var count int
	db := mysql.NewConn().Table(p.TableName()).Where("classify = ?", Classify)
	err := db.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// 查询
func (p *ProductInfo) Query() error {
	return mysql.NewConn().Table(p.TableName()).Where("id = ?", p.ID).First(p).Error
}

// 查询全部
func (p *ProductInfo) QueryAll() (list []*ProductInfo, err error) {
	err = mysql.NewConn().Table(p.TableName()).Find(&list).Error
	return
}

// 商品素材列表
func (p *ProductInfo) QueryMockupList(pn, ps int) (list []*ProductInfo, count int, err error) {
	db := mysql.NewConn().Table(p.TableName())

	if len(p.Spu) > 0 {
		db = db.Where("spu LIKE ?", fmt.Sprintf("%%%s%%", p.Spu))
	}

	if len(p.Name) > 0 {
		db = db.Where("name LIKE ?", fmt.Sprintf("%%%s%%", p.Name))
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	db = db.Preload("ImgList", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_display = ?", true).Order("sort asc")
	})

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (p *ProductInfo) GetProductByCollect(idList []uint) (list []*ProductInfo, err error) {

	db := mysql.NewConn().Table(p.TableName())
	db = db.Where("id IN (?)", idList)
	db = db.Where("status = ?", 2)

	db = db.Preload("ImgList", func(db *gorm.DB) *gorm.DB {
		return db. /*.Where("is_display = ?", true)*/ Order("sort asc")
	})

	//预加载尺码价格信息
	db = db.Preload("SizeList", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	err = db.Find(&list).Error
	return
}

// 查询
func (p *ProductInfo) QueryBySpu() error {
	return mysql.NewConn().Table(p.TableName()).Where("spu = ?", p.Spu).First(p).Error
}

// 更改特殊状态
func (p *ProductInfo) PutSpecialStatus(updateMap map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&ProductInfo{}).Where("id = ?", p.ID).Updates(updateMap).Error
}

func (p *ProductInfo) TestGetList() (list []*ProductInfo, err error) {
	err = mysql.NewConn().Table(p.TableName()).Find(&list).Error
	return
}

func (p *ProductInfo) GetNewArrivals() (int, error) {
	var count int
	twoMonthsAgo := time.Now().AddDate(0, -2, 0)
	err := mysql.NewConn().Table(p.TableName()).Where("created_at > ?", twoMonthsAgo).Where("status = 2").Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetProductByColorID... 根据颜色id获取商品信息
func (p *ProductInfo) GetProductByColorID() (list []*ProductInfo, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("color_id like ?", "%"+p.ColorId+"%").Find(&list).Error
	return
}

// 获取全部的SPU 列表
func (p *ProductInfo) GetSpuList() (list []*ProductInfo, err error) {
	err = mysql.NewConn().Table(p.TableName()).Select("spu").Group("spu").Find(&list).Error
	return
}

// GetListBySpuList
func (p *ProductInfo) GetListBySpuList(spuList []string) (list []*ProductInfo, err error) {
	db := mysql.NewConn().Table(p.TableName()).Where("spu IN (?)", spuList)

	//预加载图片信息，根据sort升序取第一位可展示图片为首图
	db = db.Preload("ImgList", func(db *gorm.DB) *gorm.DB {
		return db. /*Where("is_display = ?", true).*/ Order("sort asc")
	})

	//预加载尺码价格信息
	db = db.Preload("SizeList", func(db *gorm.DB) *gorm.DB {
		return db.Order("price asc")
	})

	err = db.Find(&list).Error
	return
}

// 通过spu 更新MetaDescription
func (p *ProductInfo) UpdateMetaDescriptionBySpu() error {
	return mysql.NewConn().Model(&ProductInfo{}).Where("spu = ?", p.Spu).Update("meta_description", p.MetaDescription).Error
}
