package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*ToolVersionV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// ToolVersionV2 工具版本表 - GORM v2版本
type ToolVersionV2 struct {
	ID          int64     `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Version     string    `json:"version"`                      // 版本号
	Description string    `json:"description" gorm:"type:BLOB"` // 版本描述
	FileName    string    `json:"file_name"`                    // 文件名
	DownloadUrl string    `json:"download_url"`                 // 下载地址
	ServerAddr  string    `json:"server_addr"`                  // 服务器地址
}

func (t *ToolVersionV2) TableName() string {
	return "tool_version"
}

// 统一数据库接口方法
func (t *ToolVersionV2) Create(db mysql.DBInterface) error {
	return db.Create(t).Error()
}

func (t *ToolVersionV2) Query(db mysql.DBInterface) error {
	return db.Where(t).First(t).Error()
}

func (t *ToolVersionV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*ToolVersionV2, int64, error) {
	var list []*ToolVersionV2
	var count int64

	query := db.Model(&ToolVersionV2{})
	for key, value := range conditions {
		if key == "version_like" {
			query = query.Where("version LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		} else {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}

	if err := query.Count(&count).Error(); err != nil {
		return nil, 0, err
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Order("id desc").Find(&list).Error(); err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (t *ToolVersionV2) Update(db mysql.DBInterface) error {
	return db.Save(t).Error()
}

func (t *ToolVersionV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(t).Error()
}

func (t *ToolVersionV2) Delete(db mysql.DBInterface) error {
	return db.Delete(t).Error()
}

// 根据版本号查询
func (t *ToolVersionV2) QueryByVersion(db mysql.DBInterface, version string) error {
	return db.Where("version = ?", version).First(t).Error()
}

// 获取最新版本
func (t *ToolVersionV2) GetLatestVersion(db mysql.DBInterface) error {
	return db.Order("id desc").First(t).Error()
}

// 批量删除
func (t *ToolVersionV2) DeleteByIds(db mysql.DBInterface, ids []uint) error {
	return db.Where("id IN ?", ids).Delete(&ToolVersionV2{}).Error()
}

// 获取指定数量的最新版本
func (t *ToolVersionV2) GetListByLimit(db mysql.DBInterface, limit int) ([]*ToolVersionV2, error) {
	var list []*ToolVersionV2
	err := db.Order("id desc").Limit(limit).Find(&list).Error()
	return list, err
}

// 工厂验证头部信息
func (t *ToolVersionV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) ([]*VerifyHead, int64, error) {
	var list []*VerifyHead
	var count int64

	db := mysql.NewUnifiedDB().Table(t.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	if err := db.Count(&count).Error(); err != nil {
		return nil, 0, err
	}

	if idMax < 1 && idMin < 1 {
		offset := (pn - 1) * ps
		db = db.Offset(offset).Limit(ps)
	}

	err := db.Find(&list).Error()
	return list, count, err
}

// 创建或更新 - 工厂同步
func (t *ToolVersionV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	detailObj := new(ToolVersionV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingRecord ToolVersionV2
	err = db.Where("id = ?", detailObj.ID).First(&existingRecord).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		if err := db.Create(detailObj).Error(); err != nil {
			log.Error("创建ToolVersion失败: ", err)
			return err
		}
		log.Info("创建ToolVersion成功: ", detailObj.ID)
	} else {
		if err := db.Save(detailObj).Error(); err != nil {
			log.Error("更新ToolVersion失败: ", err)
			return err
		}
		log.Info("更新ToolVersion成功: ", detailObj.ID)
	}

	return nil
}
