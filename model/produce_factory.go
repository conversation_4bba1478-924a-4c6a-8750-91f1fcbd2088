package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"strconv"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ProduceFactory)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*ProduceFactory)(nil))
}

type ProduceFactory struct {
	ID                uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	IdentifyId        string    `json:"identify_id"      gorm:"unique;not null"`                                //识别id
	FactoryName       string    `json:"factory_name"       gorm:"not null"`                                     //工厂名
	DeliveryAddress   string    `json:"delivery_address"       gorm:"not null"`                                 //收货地址
	ShipAddress       string    `json:"ship_address"       gorm:"not null"`                                     //发货地址
	ProduceAddress    string    `json:"produce_address"       gorm:"not null"`                                  //生产地址
	PrintTechnology   string    `json:"print_technology"       gorm:"not null"`                                 //打印工艺  string  |分割
	Technology        int64     `json:"technology" gorm:"technology"`                                           // 支持的工艺
	DeliveryCars      string    `json:"delivery_cars" gorm:"delivery_cars"`                                     // 衣服拣车的车号
	BrandDeliveryCars string    `json:"brand_delivery_cars" gorm:"brand_delivery_cars"`                         // 包装吊牌拣车的车号
	Weight            int       `json:"weight" gorm:"weight"`                                                   // 工厂权重，目前暂未使用，后续可能会用到工单分配算法
	PrincipalId       uint      `json:"principal_id"       gorm:"not null"`                                     //负责同事 ID
	Person            Person    `json:"person"        gorm:"foreignkey:id;association_foreignkey:principal_id"` //负责同事信息
}

type ProduceFactoryResp struct {
	//ID              uint   `gorm:"PRIMARY_KEY" json:"ID"`
	IdentifyId  string `json:"identify_id"`  //识别id
	FactoryName string `json:"factory_name"` //工厂名
	//DeliveryAddress string `json:"delivery_address"` //收货地址
	ShipAddress string `json:"ship_address"` //发货地址
	//ProduceAddress  string `json:"produce_address"`  //生产地址
	//PrintTechnology string `json:"print_technology"` //打印工艺  string  |分割
	//PrincipalId     uint   `json:"principal_id"`     //负责同事 ID
}

func (p *ProduceFactory) TableName() string {
	return "produce_factory"
}

// 新增
func (p *ProduceFactory) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&ProduceFactory{}).Create(p).Error
}

// 更新
func (p *ProduceFactory) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	err := db.Model(&ProduceFactory{}).Where("identify_id = ?", p.IdentifyId).Update(p).First(p).Error
	if err != nil {
		return err
	}
	return err

	//return db.Model(&ProduceFactory{}).Where("identify_id = ?", p.IdentifyId).Preload("Person").Updates(
	//	map[string]interface{}{
	//		"factory_name":     p.FactoryName,
	//		"delivery_address": p.DeliveryAddress,
	//		"ship_address":     p.ShipAddress,
	//		"produce_address":  p.ProduceAddress,
	//		"print_technology": p.PrintTechnology,
	//		"name": p.Person.Name,
	//		"phone": p.Person.Phone,
	//	}).Error
}

// 查询
func (p *ProduceFactory) Query() (List []*ProduceFactory, err error) {
	db := mysql.NewConn().Table(p.TableName())
	if p.IdentifyId != "" {
		db = db.Where("name like ?", "%"+p.IdentifyId+"%")
	}
	if p.ID > 0 {
		db = db.Where("id = ?", p.ID)
	}
	err = db.Order("sort asc").Find(&List).Error
	return
}

// 根据 id 查询
func (p *ProduceFactory) QueryOne() (err error) {
	db := mysql.NewConn().Table(p.TableName())

	db = db.Where("id = ?", p.ID)

	err = db.First(p).Error

	return
}

// 精准查询，根据identify_id
func (p *ProduceFactory) QueryDetailByIdentifyID() (List []ProduceFactory, err error) {
	db := mysql.NewConn().Table(p.TableName()).Preload("Person")
	if p.IdentifyId != "" {
		db = db.Where("identify_id = ?", p.IdentifyId)
	}
	err = db.Find(&List).Error
	return
}

// 查询所有
func (p *ProduceFactory) GetList(page, size int) (produceFactoryList []*ProduceFactory, count int, err error) {

	db := mysql.NewConn().Table(p.TableName())

	if p.FactoryName != "" {
		db = db.Where("factory_name like ?", "%"+p.FactoryName+"%")
	}
	if p.IdentifyId != "" {
		db = db.Where("identify_id like ?", "%"+p.IdentifyId+"%")
	}
	if p.PrincipalId > 0 {
		db = db.Where("principal_id = ?", p.PrincipalId)
	}
	err = db.Count(&count).Error

	err = db.Preload("Person").Order("id asc").Offset((page - 1) * size).Limit(size).Find(&produceFactoryList).Error
	return
}

// 生成新生产工厂ID
func (p *ProduceFactory) GerateNewFactoryId(tx ...*gorm.DB) error {

	tmp := make([]*ProduceFactory, 0)
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}

	err := db.Table(p.TableName()).Limit(1).Order("id desc").Find(&tmp).Error
	if err != nil {
		return err
	}

	//如果有查到，则在最后一条的基础上继续往下加，否则从S0000开始创建
	if len(tmp) > 0 {
		n, _ := strconv.ParseInt(tmp[0].IdentifyId[1:5], 10, 64)
		p.IdentifyId = fmt.Sprintf("S%04d", int(n)+1)
	} else {
		p.IdentifyId = "S0000"
	}
	return nil
}

func (c *ProduceFactory) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ProduceFactory, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *ProduceFactory) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ProduceFactory{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ProduceFactory) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ProduceFactory) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ProduceFactory)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ProduceFactory) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
