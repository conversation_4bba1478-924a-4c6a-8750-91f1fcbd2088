package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

const (
	_         = iota
	SHIPPED   // 已发货
	COMPLETED // 已完成
)

func init() {
	mysql.RegisterTable((*ReplenishOrderItem)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ReplenishOrderItem)(nil))
}

// 工厂端补货列表
type ReplenishOrderItem struct {
	ID            uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt     time.Time `json:"created_at,omitempty"`
	UpdatedAt     time.Time `json:"updated_at,omitempty"`
	CompletionAT  time.Time `json:"completion_at"` // 完成时间
	State         int       `json:"state"`
	StorageStatus int       `json:"storage_status"` // 入库状态 2023年10月23日新增 默认0值，未进行入库，1、部分入库 2、完全入库

	ReplenishOrderID uint   `json:"replenish_order_id"` // 补货订单ID
	ItemCode         string `json:"item_code"`          // 补货子项ID | 制衣厂发货记录ID

	PurchaseID  uint   `json:"purchase_id"`                        // 采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` // 工厂名

	PlateNumber string `json:"plate_number"` // 车牌号
	ShipmentDay int64  `json:"shipment_day"` // 出货日期 取当天0点时间戳  或当天任意时间戳

	RefNo         string `json:"ref_no"`         // 运单号
	ShipmentCount int    `json:"shipment_count"` // 发货数量 由子项统计而来
	ArrivalCount  int    `json:"arrival_count"`  // 到货数量 由子项工厂填写后统计而来 已质检数量
	BackCount     int    `json:"back_count"`     // 退货数量

	ImgUrlList string `json:"img_url_list" gorm:"type:BLOB;"` // 图片列表 序列化使用|拼接

	Remark string `json:"remark"` // 备注 来自制衣厂发货记录

	ReplenishJson string `json:"replenish_json"  gorm:"type:BLOB;"` // 具体补货的json数组
	//ReplenishOrderItemEntry []*ReplenishOrderItemEntry `json:"replenish_order_item_entry"  gorm:"foreignkey:replenish_order_item_id;association_foreignkey:id"`
}

func (r *ReplenishOrderItem) TableName() string {
	return "replenish_order_item"
}

// 新增
func (r *ReplenishOrderItem) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(r.TableName()).Create(r).Error
}

// 查询
func (r *ReplenishOrderItem) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(r.TableName())

	if r.ID > 0 {
		db = db.Where("id = ?", r.ID)
	}
	if len(r.ItemCode) > 8 {
		db = db.Where("item_code = ?", r.ItemCode)
	}

	return db.First(r).Error
}

func (r *ReplenishOrderItem) NewItemCode(orderCode string) (err error) {
	var count int

	err = mysql.NewConn().Table(r.TableName()).Where("replenish_order_id = ?", r.ReplenishOrderID).Count(&count).Error

	if err != nil {
		return
	}

	r.ItemCode = fmt.Sprintf("%s-%d", orderCode, count+1)

	return
}

// 根据 replenish_order_id 获取列表
func (r *ReplenishOrderItem) GetListByReplenishOrderID() (list []*ReplenishOrderItem, err error) {
	err = mysql.NewConn().Table(r.TableName()).Where("replenish_order_id = ?", r.ReplenishOrderID).Find(&list).Error
	return
}

// 获取列表
func (r *ReplenishOrderItem) GetList(pn, ps int, timeS, timeE int64) (list []*ReplenishOrderItem, count int, err error) {
	db := mysql.NewConn().Table(r.TableName())

	//db = db.Preload("ReplenishOrderItemEntry")

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if r.State != 0 {
		db = db.Where("state = ?", r.State)
	}

	if r.RefNo != "" {
		db = db.Where("ref_no like ?", "%"+r.RefNo+"%")
	}

	if r.ItemCode != "" {
		db = db.Where("item_code like ?", "%"+r.ItemCode+"%")
	}

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	if r.ReplenishOrderID != 0 {
		db = db.Where("replenish_order_id = ?", r.ReplenishOrderID)
	}

	/*if timeS == 0 && timeE == 0 && r.State == 0 && r.ItemCode == "" && r.PurchaseID == 0 && r.ReplenishOrderID == 0 {
		db = db.Where("state != ?", COMPLETED)
	}
	*/

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Order("shipment_day asc").Find(&list).Error

	return
}

func (r *ReplenishOrderItem) PutReplenishItem(updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ReplenishOrderItem{}).Where("id = ?", r.ID).Updates(updateMap).Error
	return
}

// 模糊查询根据颜色ID
func (r *ReplenishOrderItem) GetListByColorID(colorID []string) (list []*ReplenishOrderItem, err error) {
	db := mysql.NewConn().Table(r.TableName())
	for i, v := range colorID {
		if i == 0 {
			db = db.Where("replenish_json like ?", "%"+v+"%")
		} else {
			db = db.Or("replenish_json like ?", "%"+v+"%")
		}
	}
	err = db.Find(&list).Error
	return
}

// 根据 itemCode 获取详情
func (r *ReplenishOrderItem) GetDetailByItemCode() (err error) {
	err = mysql.NewConn().Table(r.TableName()).Where("item_code = ?", r.ItemCode).First(r).Error
	return
}

func (c *ReplenishOrderItem) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ReplenishOrderItem, 0)

	err := db.Find(&list).Error

	return list, err
}

func (r *ReplenishOrderItem) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ReplenishOrderItem{}).Where("id IN (?)", ids).Delete(r).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ReplenishOrderItem) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ReplenishOrderItem) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ReplenishOrderItem)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ReplenishOrderItem) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
