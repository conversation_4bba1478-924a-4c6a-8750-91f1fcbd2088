package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*InventoryV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// InventoryV2 库存管理表 - GORM v2版本
type InventoryV2 struct {
	ID                   uint      `gorm:"primaryKey" json:"id,omitempty"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	StorageID            uint      `json:"storage_id"`             // 仓库ID
	ColorID              string    `json:"color_id"`               // 颜色id
	ColorName            string    `json:"color_name"`             // 颜色名称
	SizeID               string    `json:"size_id"`                // 尺码id
	SizeName             string    `json:"size_name"`              // 尺码名称
	Location             string    `json:"location"`               // 颜色ID+尺码ID，sku的后6位
	InventoryStorageName string    `json:"inventory_storage_name"` // 库存的库位名称

	FactoryID   uint   `json:"factory_id"`   // 工厂ID
	Enable      int    `json:"enable"`       // 0 1 未启用，2启用
	FactoryName string `json:"factory_name"` // 工厂名称 预留

	Quality   int `json:"quality"`   // 库存数量
	Threshold int `json:"threshold"` // 预警值

	TotalSales      int     `json:"total_sales"`                    // 历史总销量
	FifteenDaySales int     `json:"fifteen_day_sales"`              // 15天总销量
	AverageSales    float64 `json:"average_sales"`                  // 15天平均销量
	FifteenCount    string  `json:"fifteen_count" gorm:"type:BLOB"` // 15天 json,数组
	TodaySales      int     `json:"today_sales"`                    // 今日销量
}

func (i *InventoryV2) TableName() string {
	return "inventory"
}

// 统一数据库接口方法
func (i *InventoryV2) Create(db mysql.DBInterface) error {
	return db.Create(i).Error()
}

func (i *InventoryV2) Query(db mysql.DBInterface) error {
	return db.Where(i).First(i).Error()
}

func (i *InventoryV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*InventoryV2, int64, error) {
	var list []*InventoryV2
	var count int64

	query := db.Model(&InventoryV2{})
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (i *InventoryV2) Update(db mysql.DBInterface) error {
	return db.Save(i).Error()
}

func (i *InventoryV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(i).Error()
}

func (i *InventoryV2) Delete(db mysql.DBInterface) error {
	return db.Delete(i).Error()
}

// 根据Location查询库存
func (i *InventoryV2) GetByLocation(db mysql.DBInterface, location string) error {
	return db.Where("location = ?", location).First(i).Error()
}

// 根据颜色ID查询库存列表
func (i *InventoryV2) GetListByColorID(db mysql.DBInterface, colorID string) ([]*InventoryV2, error) {
	var list []*InventoryV2
	err := db.Where("color_id = ?", colorID).Find(&list).Error
	return list, err
}

// 根据工厂ID查询库存
func (i *InventoryV2) GetListByFactoryID(db mysql.DBInterface, factoryID uint) ([]*InventoryV2, error) {
	var list []*InventoryV2
	err := db.Where("factory_id = ?", factoryID).Find(&list).Error
	return list, err
}

// 更新库存数量 - 加库存
func (i *InventoryV2) AddStock(db mysql.DBInterface, quantity int) error {
	return db.Model(i).Update("quality", gorm.Expr("quality + ?", quantity)).Error()
}

// 更新库存数量 - 减库存
func (i *InventoryV2) ReduceStock(db mysql.DBInterface, quantity int) error {
	return db.Model(i).Updates(map[string]interface{}{
		"quality":     gorm.Expr("quality - ?", quantity),
		"today_sales": gorm.Expr("today_sales + ?", quantity),
	}).Error()
}

//// 原子性减库存操作
//func (i *InventoryV2) ReduceStockAtomic(db mysql.DBInterface, quantity int) error {
//	return db.Transaction(func(tx mysql.DBInterface) error {
//		// 先检查库存是否充足
//		var currentInventory InventoryV2
//		if err := tx.Where("location = ? AND factory_id = ?", i.Location, i.FactoryID).First(&currentInventory).Error; err != nil {
//			return err
//		}
//
//		if currentInventory.Quality < quantity {
//			return errors.New("库存不足")
//		}
//
//		// 减库存
//		return tx.Model(&InventoryV2{}).
//			Where("location = ? AND factory_id = ?", i.Location, i.FactoryID).
//			Updates(map[string]interface{}{
//				"quality":     gorm.Expr("quality - ?", quantity),
//				"today_sales": gorm.Expr("today_sales + ?", quantity),
//			}).Error
//	})
//}

// 更新预警阈值
func (i *InventoryV2) UpdateThreshold(db mysql.DBInterface, threshold int) error {
	return db.Model(i).Update("threshold", threshold).Error
}

// 更新库位名称
func (i *InventoryV2) UpdateStorageName(db mysql.DBInterface, storageName string) error {
	return db.Model(i).Update("inventory_storage_name", storageName).Error
}

// 获取低库存预警列表
func (i *InventoryV2) GetLowStockList(db mysql.DBInterface, factoryID uint) ([]*InventoryV2, error) {
	var list []*InventoryV2
	err := db.Where("factory_id = ? AND quality <= threshold AND enable = 2", factoryID).Find(&list).Error
	return list, err
}

// 批量更新库存
func (i *InventoryV2) BatchUpdateStock(db mysql.DBInterface, updates []InventoryUpdateV2) error {
	return db.Transaction(func(tx mysql.DBInterface) error {
		for _, update := range updates {
			if err := tx.Model(&InventoryV2{}).
				Where("location = ? AND factory_id = ?", update.Location, update.FactoryID).
				Update("quality", gorm.Expr("quality + ?", update.Quantity)).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// 获取颜色列表
func (i *InventoryV2) GetColorList(db mysql.DBInterface) ([]*ColorItemV2, error) {
	var list []*ColorItemV2
	err := db.Table(i.TableName()).
		Select("DISTINCT color_id, color_name").
		Where("enable = 2").
		Find(&list).Error
	return list, err
}

// 根据颜色获取尺码列表
func (i *InventoryV2) GetSizeListByColor(db mysql.DBInterface, colorID string) ([]*SizeItemV2, error) {
	var list []*SizeItemV2
	err := db.Table(i.TableName()).
		Select("DISTINCT size_id, size_name").
		Where("color_id = ? AND enable = 2", colorID).
		Find(&list).Error
	return list, err
}

// 更新销售统计信息
func (i *InventoryV2) UpdateSalesInfo(db mysql.DBInterface, totalSales, fifteenDaySales int, averageSales float64, fifteenCount string) error {
	return db.Model(i).Updates(map[string]interface{}{
		"total_sales":       totalSales,
		"fifteen_day_sales": fifteenDaySales,
		"average_sales":     averageSales,
		"fifteen_count":     fifteenCount,
	}).Error
}

// 重置今日销量
func (i *InventoryV2) ResetTodaySales(db mysql.DBInterface) error {
	return db.Model(&InventoryV2{}).Update("today_sales", 0).Error
}

// 工厂验证头部信息
func (i *InventoryV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) ([]*model.VerifyHead, int64, error) {
	var list []*model.VerifyHead
	var count int64

	db := mysql.NewUnifiedDB().Table(i.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	if err := db.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if idMax < 1 && idMin < 1 {
		offset := (pn - 1) * ps
		db = db.Offset(offset).Limit(ps)
	}

	err := db.Find(&list).Error
	return list, count, err
}

// 创建或更新 - 工厂同步
func (i *InventoryV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("InventoryV2 detailData: ", string(detailData))

	detailObj := new(InventoryV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingInventory InventoryV2
	err = db.Where("id = ?", detailObj.ID).First(&existingInventory).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建Inventory失败: ", err)
			return err
		}
		log.Info("创建Inventory成功: ", detailObj.ID)
	} else {
		// 更新现有记录
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新Inventory失败: ", err)
			return err
		}
		log.Info("更新Inventory成功: ", detailObj.ID)
	}

	return nil
}

// 辅助结构体
type InventoryUpdateV2 struct {
	Location  string `json:"location"`
	FactoryID uint   `json:"factory_id"`
	Quantity  int    `json:"quantity"`
}

type ColorItemV2 struct {
	ColorID   string `json:"color_id"`
	ColorName string `json:"color_name"`
}

type SizeItemV2 struct {
	SizeID   string `json:"size_id"`
	SizeName string `json:"size_name"`
}
