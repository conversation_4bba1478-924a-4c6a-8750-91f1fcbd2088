package factory

import (
	"github.com/jinzhu/gorm"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
)

func init() {
	mysql.RegisterTable((*WorkLogLocal)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*WorkLogLocal)(nil))
}

/*WorkLogLocal
* 工作记录的印花厂本地表，用来暂时记录当前的生产状态。内容：新的工作记录创建、更新，生产流程（喷淋、打印、刺绣、质检、发货）时查询是否有工作记录时使用
 */
type WorkLogLocal struct {
	WorkLog
}

func (w *WorkLogLocal) TableName() string {
	return "work_log_local"
}

// 新增
func (w *WorkLogLocal) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

func (w *WorkLogLocal) PreWorkList() (list []*WorkLog, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if w.MinusTime == 100 {
		db = db.Where("minus_time < 1")
	}

	err = db.Where("last_operate = ?", w.LastOperate).Order("id desc").Find(&list).Error

	return
}

// 被再次打印了，就将之前的工作记录失效，不记录到绩效里面
func (w *WorkLogLocal) SetOldLogDisable(tx ...*gorm.DB) (list []*WorkLog, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLogLocal{}).Where("worker_order_id = ?", w.WorkerOrderId)
	if w.CodeType == CodeTypePrePrint {
		db = db.Where("code_type = ?", w.CodeType)
		w.CodeType = 0
	} else {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if len(w.CodeDesc) > 0 {
		db = db.Where("code_desc = ?", w.CodeDesc)
	}

	// 只更新未损耗过的工作记录，这里通过小于10来判断，预留一些状态
	db = db.Where("minus_time < 10")

	err = db.Updates(w).Find(&list).Error

	return
}

// 工单进行了损耗，给非负责人的工作记录标记上损耗时间，但是仍然统计进绩效
func (w *WorkLogLocal) UpdateMinusTime(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLogLocal{})

	if w.WorkerId > 0 {
		db = db.Where("worker_id != ? ", w.WorkerId)
		// 不更新工人 id
		w.WorkerId = 0
	}

	db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	err = db.Updates(w).Error
	return
}

// 工单进行了损耗，将某个工人的工作记录设置为不统计进绩效
func (w *WorkLogLocal) CancelWorkPerformance(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLogLocal{})

	db = db.Where("worker_id = ? AND worker_order_id = ?", w.WorkerId, w.WorkerOrderId)

	// 损耗的工作记录排除掉
	db = db.Where("code_type != ?", CodeTypeMinus)

	// 不更新工人 id
	w.WorkerId = 0

	err = db.Updates(w).Error
	return
}
