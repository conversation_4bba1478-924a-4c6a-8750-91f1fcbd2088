package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Replenishment)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Replenishment)(nil))
}

// 缺货统计表
type Replenishment struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	InventoryID  uint      `json:"inventory_id"` //关联库存表id
	StorageID    uint      `json:"storage_id"`
	PurchaseID   uint      `json:"purchase_id"`
	ProductCName string    `json:"product_c_name"` //商品中文名

	SubNum    int    `json:"sub_num"`    //差值
	PayTime   int64  `json:"pay_time"`   //支付时间
	OrderCode string `json:"order_code"` //关联订单号

	State             int   `json:"state"`              // 1 待补货 |2 补货中 | 3 已忽略 | 4 正常状态（默认隐藏）| 5 补货异常
	ReplenishmentTime int64 `json:"replenishment_time"` // 点击补货时间
	Period            int   `json:"period"`             // 补货周期 状态为补货中，则记录补货周期

	Avg           float64 `json:"avg"`             // 7天销量平均值
	FutureNum     int     `json:"future_num"`      // 预估未来三天销量
	Quality       int     `json:"quality"`         // 当前触发时库存
	DaysCountJson string  `json:"days_count_json"` // 近十五日内销量json

	Inventory   *Inventory   `json:"inventory" gorm:"foreignkey:id;association_foreignkey:inventory_id"`
	StorageSite *StorageSite `json:"storage_site"  gorm:"foreignkey:id;association_foreignkey:storage_id"`
}

// 7日内 销量统计 json
type DaysJson struct {
	Day   string `json:"day"`
	Count int    `json:"count"`
}

func (r *Replenishment) TableName() string {
	return "replenishment"
}

// 新增sku
func (s *Replenishment) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

func (s *Replenishment) GetList(pn, ps int, time_s, time_e int64) (list []*Replenishment, count int, err error) {

	db := mysql.NewConn().Table(s.TableName()).Preload("Inventory").Preload("Inventory.StorageSite").Preload("Inventory.StorageSite.PurchaseFactory")
	//db := mysql.NewConn().Table(s.TableName()).Preload("Inventory").Preload("StorageSite")

	if s.State != 0 {
		db = db.Where("state = ?", s.State)
	} else {
		db = db.Where("state != ?", 4)
	}

	if time_s != 0 {
		db = db.Where("pay_time >= ?", time_s)
	}

	if time_e != 0 {
		db = db.Where("pay_time <= ?", time_e)
	}
	if s.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", s.PurchaseID)
	}

	if s.ProductCName != "" {
		db = db.Where("product_c_name like ?", "%"+s.ProductCName+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (s *Replenishment) PutState(idList []int) (err error) {
	err = mysql.NewConn().Model(&Replenishment{}).Where("id IN (?)", idList).Update("state", s.State).Error
	return
}

func (s *Replenishment) Query() (bool, error) {
	db := mysql.NewConn().Table(s.TableName())

	if s.InventoryID != 0 {
		db = db.Where("inventory_id = ?", s.InventoryID)
	}

	err := db.First(s).Error
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}

	return true, err
}

func (s *Replenishment) Updates() (err error) {
	return mysql.NewConn().Model(&Replenishment{}).Where("id = ?", s.ID).Updates(s).Error
}

func (s *Replenishment) UpdateQuality() (err error) {
	return mysql.NewConn().Model(&Replenishment{}).Where("id = ?", s.ID).Update("quality", s.Quality).Error
}

func (c *Replenishment) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Replenishment, 0)

	err := db.Find(&list).Error

	return list, err
}

func (r *Replenishment) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&Replenishment{}).Where("id IN (?)", ids).Delete(r).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Replenishment) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Replenishment) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Replenishment)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Replenishment) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
