package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*UserPickingTask)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*UserPickingTask)(nil))
}

type PickingTaskDesignItem struct {
	TemplateId           string `json:"template_id"` // 模板id
	Version              int    `json:"version"`     // 版本号
	Type                 int    `json:"type"`        // 类型，1：包装袋，2：吊牌
	DeliveryCar          string `json:"delivery_car"`
	InventoryStorageName string `json:"inventory_storage_name"`
	DesignInfo           string `json:"design_info"`
	ModelImg             string `json:"model_img"   gorm:"type:BLOB;"` // 模特展示图信息
}

type UserPickingTask struct {
	ID             uint      `json:"id" gorm:"PRIMARY_KEY" `
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	FactoryId      int       `json:"factory_id"`                      // 工厂id
	CompleteTime   int64     `json:"complete_time"`                   // 完成时间
	DesignItems    string    `json:"design_items"  gorm:"type:BLOB;"` // 设计项，对应 struct PickingTaskDesignItem
	PackPickingCar string    `json:"pack_picking_car"`                // 包装袋专属拣车号
	PackPlanId     int       `json:"pack_plan_id"`                    // 关联到的包装生产任务
	PackType       int       `json:"pack_type" gorm:"default:0"`      // 包装袋类型，0：大包装袋，1：小包装袋
	TagPickingCar  string    `json:"tag_picking_car"`                 // 吊牌专属拣车号
	TagPlanId      int       `json:"tag_plan_id"`                     // 关联到的吊牌生产任务
	WorkerName     string    `json:"worker_name" gorm:"default:''"`   // 工人姓名
	WorkerPhone    string    `json:"worker_phone" gorm:"default:''"`  // 工人电话

	PackTemplateId string `json:"pack_template_id"`             // 包装袋模板id
	TagTemplateId  string `json:"tag_template_id"`              // 吊牌模板id
	PriorityB      uint64 `json:"priority_b"  gorm:"default:0"` // 优先分配到 B 区，优先级最高的排序，bit 位标识，也就是说可以标记 64 个优先级，并且越大，优先级越高，！！！逻辑搞反了，优先级越高，是越不要往B区分配

	Specification int `json:"specification" gorm:"default:0"` // 规格，只在包装袋时使用：0-大包装袋，1-小包装袋
}

func (u *UserPickingTask) TableName() string {
	return "user_picking_task"
}

// 新增
func (u *UserPickingTask) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserPickingTask{}).Create(u).Error
}

func (u *UserPickingTask) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*UserPickingTask, 0)

	err := db.Find(&list).Error

	return list, err
}

func (u *UserPickingTask) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&UserPickingTask{}).Where("id IN (?)", ids).Delete(u).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (u *UserPickingTask) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(u.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", u.ID).Updates(u).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", u.ID).Update("updated_at", u.UpdatedAt).Error

	return
}

func (u *UserPickingTask) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(UserPickingTask)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (u *UserPickingTask) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(u.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (u *UserPickingTask) GetList(pn, ps, produceState int, pickingCar string, timeStart, timeEnd int64) (list []*UserPickingTask, noFinishCount, totalCount int, err error) {
	db := mysql.NewConn().Table(u.TableName())

	// 未完成的数量，不会因筛选条件或翻页而变更，总是展示总数
	err = db.Where("complete_time <= 10").Count(&noFinishCount).Error
	if err != nil {
		return
	}

	// id
	if u.ID > 0 {
		db = db.Where("id = ?", u.ID)
	}

	// 创建时间
	if u.CreatedAt.Unix() > 0 {
		db = db.Where("created_at >= ?", u.CreatedAt)
	}

	// 专属拣车号
	if len(pickingCar) > 0 {
		db = db.Where("pack_picking_car LIKE ? OR tag_picking_car LIKE ?", "%"+pickingCar+"%", "%"+pickingCar+"%")
	}

	// 如果为 未生产
	if produceState == 1 { // 已生产
		db = db.Where("complete_time > 10")
		if timeStart > 0 {
			db = db.Where("complete_time >= ?", timeStart)
		}
		if timeEnd > 0 {
			db = db.Where("complete_time <= ?", timeEnd)
		}
	} else { // 未生产
		db = db.Where("complete_time <= 10")
		if timeStart > 0 {
			db = db.Where("created_at >= ?", time.Unix(timeStart, 0))
		}
		if timeEnd > 0 {
			db = db.Where("created_at <= ?", time.Unix(timeEnd, 0))
		}
	}

	// 所属工厂
	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	// 全部数量
	err = db.Count(&totalCount).Error
	if err != nil {
		return
	}

	// 未生产的数量
	//err = db.Where("complete_time < 10").Count(&notProduceCount).Error
	//if err != nil {
	//	return
	//}

	// 如果是未生产的，就按照 id 排序，否则按照完成时间倒序
	if produceState == 1 { // 已生产
		err = db.Order("complete_time desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	} else { // 未生产
		err = db.Order("id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	}

	return
}

// 获取点击开始生产按钮后的两个生产任务，orderType：1：B区优先级倒序，其他（默认）：B区优先级正序
func (u *UserPickingTask) GetProduceList(limitNum, orderType int, exceptIds []int64) (list []*UserPickingTask, unproduceCount int, err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("complete_time <= 10")

	err = db.Count(&unproduceCount).Error
	if err != nil {
		return
	}

	// 排除的 id，用于完成生产时候的使用
	if len(exceptIds) > 0 {
		db = db.Where("id NOT IN (?)", exceptIds)
	}

	if orderType == 1 { // B区优先级正序，即第一个为优先分配到 B 区
		db = db.Order("priority_b asc, id asc")
	} else { // B区优先级倒序，即第一个为优先分配到 A 区
		db = db.Order("priority_b desc, id asc")
	}

	err = db.Limit(limitNum).Find(&list).Error

	return
}

// 更新
func (u *UserPickingTask) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(u).Updates(u).First(u).Error
}

func (u *UserPickingTask) Query() (err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id = ?", u.ID)

	err = db.First(u).Error

	return
}

func (u *UserPickingTask) GetCountByStatus() (count int, err error) {

	db := mysql.NewConn().Table(u.TableName())

	if u.PackPlanId > 0 {
		db = db.Where("pack_plan_id = ?", u.PackPlanId)
	}

	if u.TagPlanId > 0 {
		db = db.Where("tag_plan_id = ?", u.TagPlanId)
	}

	// 未完成的
	db = db.Where("complete_time <= 10")

	err = db.Count(&count).Error

	return
}
