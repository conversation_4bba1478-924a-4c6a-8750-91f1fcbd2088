package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/model/front/order"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

const (
	_              = iota
	NOT_PRINT      //未打印
	YSE_PRINT      //已出工单
	IN_PRODUCTION  //正在生产
	OK_PRODUCTION  //生产完成
	UNQUALIFIED    //质检不合格
	TESTING_OK     //质检合格
	HAVE_WAYBILL   //已经贴运单
	HAVE_LOGISTICS //已揽收（已完成）
	IN_TRANSIT     //运输中

	PROBLEM_STATE = 15 //问题状态 当为问题状态时，查看问题状态备注
	CANCEL_STATE  = 20 // 取消状态
)

const (
	ArxpGeneratedSyncStateNo  = 1 // arxp文件：未生成
	ArxpGeneratedSyncStateYes = 2 // arxp文件：已生成

	CheckoutImageDownloadNo  = 1 // 质检图片，未下载
	CheckoutImageDownloadYes = 2 // 质检图片，已下载

	TagBagPrintTypeSingle = 1
	TagBagPrintTypeDouble = 2

	BrandMinusStateMinus  = 1 // 吊牌和包装进行了库存损耗
	BrandMinusStateReduce = 2 // 吊牌和包装库存损耗后生产了
)

const (
	ColorTypeLight = "0" // 浅色
	ColorTypeDark  = "2" // 深色
)

func init() {
	mysql.RegisterTable((*WorkOrder)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*WorkOrder)(nil))
}

type ImgAnalyticStruct struct {
	CanvasName string `json:"canvas_name"`
	OutputImg  string `json:"output_img"`
	Data       string `json:"data"`
}

// 工单特殊标记位的位运算
const (
	// 包装袋相关
	SpecialStatePackagingHas         = uint64(1 << 0) // 包装袋
	SpecialStatePackagingHasProduced = uint64(1 << 1) // 包装袋已被生产
	SpecialStatePackagingMinus       = uint64(1 << 2) // 包装袋有损耗
	SpecialStatePackagingDouble      = uint64(1 << 3) // 双面包装袋
	// 吊牌相关
	SpecialStateTagHas         = uint64(1 << 4) // 吊牌
	SpecialStateTagHasProduced = uint64(1 << 5) // 吊牌已被生产
	SpecialStateTagMinus       = uint64(1 << 6) // 吊牌有损耗
	SpecialStateTagDouble      = uint64(1 << 7) // 双面吊牌
	// 拣车相关
	SpecialStateRetainDeliveryCar      = uint64(1 << 8)  // 是否还留在拣车上
	SpecialStateReserve                = uint64(1 << 9)  // 预留
	SpecialStateCheckoutToCancel       = uint64(1 << 10) // 质检转取消
	SpecialStateRetainBrandDeliveryCar = uint64(1 << 11) // 是否还占用包装吊牌拣车，如果是专属拣车，占用包装吊牌拣车的话就说明还没质检合格

	// 其他
	SpecialStateCombinationPicture = uint64(1 << 12) // 是否生成打印模式下预览组合图
	SpecialStatePickingCar         = uint64(1 << 13) // 是否为专属拣车，如果损耗以后，就要清掉这个标记

)

type WorkOrder struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	OrderCode   string    `json:"order_code"`   //平台内订单号
	WorkCode    string    `json:"work_code"`    //工单号
	SKU         string    `json:"sku"`          //sku信息，额外的筛选项
	SPU         string    `json:"spu"`          //spu信息，额外的筛选项
	PrintCount  int       `json:"print_count"`  //0 1 2 全部 单面 多面
	ColorType   string    `json:"color_type"`   //颜色类型 0浅色  2深色
	ProductType int       `json:"product_type"` // 商品的类型 目前 0衣服，1长裤  2短裤  2022-11-23 16:52:00 zc  增加短裤 2023年3月26日17:21:03 zc

	OrderItemID       uint   `json:"order_item_id"`      //订单子项id 直接关联
	IsDeliver         bool   `json:"is_deliver"`         //是否打印发货信息
	UnqualifiedCount  int    `json:"unqualified_count"`  //质检不合格次数
	UnqualifiedRemark string `json:"unqualified_remark"` //不合格备注 |分割
	DesignInfo        string `json:"design_info"  gorm:"type:BLOB;"`
	DesignBit         uint32 `json:"design_bit"`      // 生产面的位运算
	PantDesignBit     uint32 `json:"pant_design_bit"` // 裤子的生产面的位运算  2022-11-23 16:51:53 zc

	EmbInfo         string `json:"emb_info"  gorm:"type:BLOB;"` // 刺绣生产数据
	EmbBit          int    `json:"emb_bit"`                     // 刺绣未生产面的数量
	EmbCount        int    `json:"emb_count"`                   // 存在总刺绣任务
	DownloadEmbFile int    `json:"download_emb_file"`           // 是否下载刺绣生产文件 2：已下载，1：未下载 | TODO：弃用

	PrintTime       int64  `json:"print_time"`       //已出工单
	ProduceSTime    int64  `json:"produce_s_time"`   //开始生产时间
	ProduceETime    int64  `json:"produce_e_time"`   //生产完成时间
	UnqualifiedTime string `json:"unqualified_time"` //质检不合格时间  多次不合格的|分割
	TestingTime     int64  `json:"testing_time"`     //质检合格时间
	DeliveryCar     string `json:"delivery_car"`     // 拣车号，前端追加到质检合格后
	WaybillTime     int64  `json:"waybill_time"`     //贴运单时间
	CompletedTime   int64  `json:"completed_time"`   //工单完成时间

	WorkState     int    `json:"work_state"`     // 工单状态
	LogisticsType string `json:"logistics_type"` // 物流类型，用于物流揽件区分

	IsFirstCheckout           int `json:"is_first_checkout" gorm:"default:1"`            // 是否为第一次发货，0，2：不是第一次发货，1：第一次发货
	GeneratedArxpFile         int `json:"generated_arxp_file" gorm:"default:1"`          // 是否已经生成了 arxp 文件，0，2：已经生成了 arxp 文件，1：未生成 arxp 文件
	DownloadCheckoutImageFile int `json:"download_checkout_image_file" gorm:"default:1"` // 是否已下载质检需要的文件，0，2：已下载，1：未下载

	//吊牌和包装需要的信息
	TagDesignID           uint   `json:"tag_design_id" gorm:"default:0"`       // 吊牌设计ID
	TagDesignType         int    `json:"tag_design_type" gorm:"default:1"`     // 打印类型，1：单面，2：双面，默认1单面
	TagProduceState       int    `json:"tag_produce_state" gorm:"default:1"`   // 自定义吊牌的生产情况，1：未生产，2：已生成，默认1未生产
	TagProduceMinus       int    `json:"tag_produce_minus"`                    // 1：自定义吊牌进行了库存损耗，2：库存损耗又进行了生产
	TagDesignVersion      int    `json:"tag_design_version" gorm:"default:0"`  // 吊牌设计版本，默认0
	PackDesignID          uint   `json:"pack_design_id" gorm:"default:0"`      // 包装设计ID
	PackDesignType        int    `json:"pack_design_type" gorm:"default:1"`    // 打印类型，1：单面，2：双面，默认1单面
	PackProduceState      int    `json:"pack_produce_state" gorm:"default:1"`  // 自定义包装的生产情况，1：未生产，2：已生成，默认1未生产
	PackagingProduceMinus int    `json:"packaging_produce_minus"`              // 1：自定义包装进行了库存损耗，2：库存损耗又进行了生产
	BrandDeliveryCar      string `json:"brand_delivery_car"`                   // 分配的吊牌和包装拣车号
	ProductCName          string `json:"product_c_name"`                       // 商品中文名
	PackDesignVersion     int    `json:"pack_design_version" gorm:"default:0"` // 包装设计版本，默认0

	// 暂停订单相关
	SuspendState  int    `json:"suspend_state" gorm:"default:0"`   // 暂停方式
	SuspendReason int    `json:"suspend_reason" gorm:"default:0"`  // 暂停原因
	ResumeTime    int64  `json:"resume_time"  gorm:"default:0"`    // 订单恢复时间
	SuspendRemark string `json:"suspend_remark" gorm:"type:BLOB;"` // 暂停订单备注

	SpecialState       uint64 `json:"special_state" gorm:"default:0"`         // 特殊状态记录 --目前用来处理包装吊牌的生产，linxb 2023-09-14
	MinusTime          int64  `json:"minus_time" gorm:"default:0"`            // 包装吊牌损耗的时间
	WorkOrderMinusTime int64  `json:"work_order_minus_time" gorm:"default:0"` // 工单损耗的时间
	CancelTime         int64  `json:"cancel_time"`                            // 工单取消时间

	CheckoutPictures string `json:"checkout_pictures" gorm:"type:BLOB;not null"` // 质检图片路径，阿里云
	Combination      string `json:"combination" gorm:"type:BLOB;"`               // 组合图的路径
	DbVersion        int64  `json:"db_version" gorm:"default:0"`                 // 数据版本
	DtgProductID     uint   `json:"dtg_product_id"  gorm:"default:0"`            // 可为空 2024-5-23 14:17:02新增  记录Dtg生成信息表ID

	ProduceFactory    int `json:"produce_factory" gorm:"default:0"`     // 生产工厂id，目前直接使用【主键id】
	FactoryIdCheckout int `json:"factory_id_checkout" gorm:"default:0"` // 质检所在工厂id，目前直接使用【主键id】

	OrderInfo order.OrderInfo `json:"order_info"  gorm:"foreignkey:order_code;association_foreignkey:order_code"`
	OrderItem order.OrderItem `json:"order_item"  gorm:"foreignkey:order_item_id;association_foreignkey:id"`
}

type WorkOrderCustomBrandProduce struct {
	ID         uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	OrderCode  string    `json:"order_code"`  //平台内订单号
	WorkCode   string    `json:"work_code"`   //工单号
	SKU        string    `json:"sku"`         //sku信息，额外的筛选项
	SPU        string    `json:"spu"`         //spu信息，额外的筛选项
	PrintCount int       `json:"print_count"` //0 1 2 全部 单面 多面
	ColorType  string    `json:"color_type"`  //颜色类型 0浅色  2深色

	OrderItemID       uint   `json:"order_item_id"`      //订单子项id 直接关联
	IsDeliver         bool   `json:"is_deliver"`         //是否打印发货信息
	UnqualifiedCount  int    `json:"unqualified_count"`  //质检不合格次数
	UnqualifiedRemark string `json:"unqualified_remark"` //不合格备注 |分割
	DesignInfo        string `json:"design_info"  gorm:"type:BLOB;"`
	DesignBit         uint32 `json:"design_bit"` //生产面的位运算

	PrintTime       int64  `json:"print_time"`       //已出工单
	ProduceSTime    int64  `json:"produce_s_time"`   //开始生产时间
	ProduceETime    int64  `json:"produce_e_time"`   //生产完成时间
	UnqualifiedTime string `json:"unqualified_time"` //质检不合格时间  多次不合格的|分割
	TestingTime     int64  `json:"testing_time"`     //质检合格时间
	DeliveryCar     string `json:"delivery_car"`     // 拣车号，前端追加到质检合格后
	WaybillTime     int64  `json:"waybill_time"`     //贴运单时间
	CompletedTime   int64  `json:"completed_time"`   //工单完成时间

	WorkState     int    `json:"work_state"`     //工单状态
	LogisticsType string `json:"logistics_type"` // 物流类型，用于物流揽件区分

	IsFirstCheckout   int `json:"is_first_checkout" gorm:"default:1"`   // 是否为第一次发货，0，2：不是第一次发货，1：第一次发货
	GeneratedArxpFile int `json:"generated_arxp_file" gorm:"default:1"` // 是否已经生成了 arxp 文件，0，2：已经生成了 arxp 文件，1：未生成 arxp 文件

	//吊牌和包装需要的信息
	TagDesignID      uint   `json:"tag_design_id" gorm:"default:0"`    // 吊牌设计ID
	TagDesignType    int    `json:"tag_design_type" gorm:"default:1"`  // 打印类型，1：单面，2：双面，默认1单面
	PackDesignID     uint   `json:"pack_design_id" gorm:"default:0"`   // 包装设计ID
	PackDesignType   int    `json:"pack_design_type" gorm:"default:1"` // 打印类型，1：单面，2：双面，默认1单面
	BrandDeliveryCar string `json:"brand_delivery_car"`                // 分配的吊牌和包装拣车号

}

// 设计信息
type DesignOutInfo struct {
	SurfaceName  string `json:"surface_name"`  //对应可打印面名称
	CanvasName   string `json:"canvas_name"`   //设计区名称
	OutputImg    string `json:"output_img"`    //输出素材图
	OriginalImg  string `json:"original_img"`  //原始素材图  为了左右袖单独加的 淦
	ModelImg     string `json:"model_img"`     //模型展示图
	OutputWidth  int    `json:"output_width"`  //输出高
	OutputHeight int    `json:"output_height"` //输出宽
	BaseBoard    string `json:"base_board"`    //台板类型
	ColorType    string `json:"color_type"`    //颜色类别
	ColorSet     string `json:"color_set"`     //颜色打印设置

	CanvasWidth   int     `json:"canvas_width,omitempty"`  // 画板的宽度
	CanvasHeight  int     `json:"canvas_height,omitempty"` // 画板的高度
	RealWidth     int     `json:"real_width,omitempty"`    //真实宽
	RealHeight    int     `json:"real_height,omitempty"`   //真实高
	Layers        string  `json:"layers,omitempty"`        //图层信息  直接存储字符串BLOB，前端自行处理
	IsFirstScan   bool    `json:"is_first_scan"`           // 是否为第一次扫码
	WorkOrderCode string  `json:"work_order_code"`         // 工单 id
	CanvasRotate  int     `json:"canvas_rotate"`           // 旋转系数
	CanvasZoom    float32 `json:"canvas_zoom"`             // 缩放系数
	CanvasLeft    int     `json:"canvas_left"`             // 画板相对容器的左边的位置
	CanvasTop     int     `json:"canvas_top"`              // 画板相对容器的左边的位置
	EmbZoom       float32 `json:"emb_zoom"`                // 绣框缩放系数 type!=2 时忽略该参数

	ColorNo    string `json:"color_no"`    // 颜色的 RGB 值，只有在打印页面的时候使用到
	PrintPiece string `json:"print_piece"` // 打印面
	IsBig      bool   `json:"is_big"`      // 是否大图
	LeftIsBig  bool   `json:"left_is_big"`
	RightIsBig bool   `json:"right_is_big"`

	LightColorSet string `json:"light_color_set"` // 自定义打印设置浅色
	DarkColorSet  string `json:"dark_color_set"`  // 自定义打印设置深色

	Type      int    `json:"type"`       // 0/1DTG 2 EMB    订单子项中使用，工单中已经区分出 emb_info 字段了 --linxb 2025年5月8日17:44:25
	Platens   string `json:"platens"`    // emb 刺绣的绣框  直接存储字符串BLOB
	MaskImage string `json:"mask_image"` //图片URL
}

const (
	EmbStateNoProduce = 1 // 未生产
	EmbStateProduce   = 2 // 已生产
)

// 刺绣生产数据
type EmbInfoItem struct {
	Index          int    `json:"index"` // 第几个刺绣任务
	ID             int    `json:"id"`
	SurfaceName    string `json:"surface_name"`    // 对应可打印面名称
	CanvasName     string `json:"canvas_name"`     // 设计区名称
	DSTName        string `json:"dst_name"`        // 生产文件名
	DSTUrl         string `json:"dst_url"`         // 输出板带路径
	PositionImg    string `json:"position_img"`    // 定位图片
	PaperLocation  string `json:"paper_location"`  // 衬纸库位
	FrameName      string `json:"frame_name"`      // 绣框名称
	ProductColors  string `json:"product_colors"`  // 生产线色信息json
	ProductPreview string `json:"product_preview"` // 绣框预览图

	State int `json:"state"` // 1未生产 2 已完成
}

func (w *WorkOrder) TableName() string {
	return "work_order"
}

// 创建、更新的后置函数
func (w *WorkOrder) AfterSave(tx *gorm.DB) error {
	if tx.Error == nil {
		sql := tx.QueryExpr()

		// 将SQL和参数传递给另一个函数执行
		log.Info("after save work order, sql: %s", sql)
	}
	return nil
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (w *WorkOrder) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(w.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (w *WorkOrder) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(WorkOrder)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (w *WorkOrder) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(w.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", w.ID).Updates(w).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", w.ID).Update("updated_at", w.UpdatedAt).Error

	return
}

// 获取指定时间内生成的工单
func (w *WorkOrder) GetListBetweenPayTime(createAtS, createAtE int64) (list []*SkuStaticListItem, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if createAtS > 0 && createAtE > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?) && created_at < FROM_UNIXTIME(?)", createAtS, createAtE)
	}

	err = db.Select("count(*) as total,sku").Group("sku").Scan(&list).Error
	return
}

func (w *WorkOrder) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*WorkOrder, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *WorkOrder) GetListByIds(ids []uint) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("id IN (?)", ids)

	err = db.Find(&list).Error

	return
}

func (w *WorkOrder) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&WorkOrder{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

// 新建订单
func (w *WorkOrder) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

func (w *WorkOrder) GetHangTagList(timeS, timeE int64) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	db = db.Where("tag_design_id > 0")

	err = db.Order("id desc").Find(&list).Error

	return
}

func (w *WorkOrder) GetPackagingList(timeS, timeE int64) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	db = db.Where("pack_design_id > 0")

	err = db.Order("id desc").Find(&list).Error

	return
}

type CheckedListItem struct {
	OrderCode   string `json:"order_code"`
	WorkCode    string `json:"work_code"`
	SKU         string `json:"sku"`          //sku信息，额外的筛选项
	TestingTime int64  `json:"testing_time"` //质检合格时间
}

// 获取单件订单、已质检合格的工单列表
func (w *WorkOrder) GetCheckedList(pn, ps int) (list []*CheckedListItem, count int, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_state = ?", TESTING_OK).Where("work_code NOT LIKE '%-%'").Select("order_code, work_code, sku, testing_time")

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if w.FactoryIdCheckout > 0 {
		db = db.Where("factory_id_checkout = ?", w.FactoryIdCheckout)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("testing_time asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (w *WorkOrder) GetList(pn, ps int, timeS, timeE int64, suspendStates []int, workStateList []int, craft int) (list []*WorkOrder, count int, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// Craft filtering logic
	switch craft {
	case 1: // 仅DTG
		db = db.Where("print_count > 0 AND emb_count = 0")
	case 2: // 仅刺绣
		db = db.Where("print_count = 0 AND emb_count > 0")
	case 3: // DTG和刺绣都有
		db = db.Where("print_count > 0 AND emb_count > 0")
	case 0: // 默认全部，不做筛选
		// 不做任何操作
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if w.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+w.OrderCode+"%")
	}
	if w.SPU != "" {
		db = db.Where("spu LIKE ?", "%"+w.SPU+"%")
	}
	if w.SKU != "" {
		db = db.Where("sku LIKE ?", "%"+w.SKU+"%")
	}
	if w.WorkCode != "" {
		db = db.Where("work_code LIKE ?", "%"+w.WorkCode+"%")
	}

	if w.WorkState != 0 {
		db = db.Where("work_state = ?", w.WorkState)
	}
	if len(workStateList) > 0 {
		db = db.Where("work_state in (?)", workStateList)
	}

	if w.PrintCount == 1 {
		db = db.Where("print_count = 1")
	}

	if w.PrintCount == 2 {
		db = db.Where("print_count > 1")
	}
	if w.ColorType == "0" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if w.ColorType == "2" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if len(w.LogisticsType) > 0 {
		db = db.Where("logistics_type like ?", "%"+w.LogisticsType+"%")
	}

	//if w.SuspendState > 0 {
	//	db = db.Where("suspend_state = ? AND resume_time < 10", w.SuspendState)
	//}

	suspendSqlStr := ""
	for _, oneSuspendState := range suspendStates {
		switch oneSuspendState {
		case 0: // 未暂停
			sqlStr := "((suspend_state < 1 && resume_time < 1) OR resume_time > 10)"
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		case 2, 100: // 全部暂停， 发货暂停
			sqlStr := fmt.Sprintf("(suspend_state = %d AND resume_time < 10)", oneSuspendState)
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		}
	}

	log.Debug("suspendSqlStr: ", suspendSqlStr)

	if len(suspendSqlStr) > 0 {
		db = db.Where(suspendSqlStr)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (w *WorkOrder) GetListByWorkCodeList(pn, ps int, timeS, timeE int64, suspendStates []int, workCodeList []string, craft int) (list []*WorkOrder, count int, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("work_code in (?)", workCodeList)

	// Craft filtering logic
	switch craft {
	case 1: // 仅DTG
		db = db.Where("print_count > 0 AND emb_count = 0")
	case 2: // 仅刺绣
		db = db.Where("print_count = 0 AND emb_count > 0")
	case 3: // DTG和刺绣都有
		db = db.Where("print_count > 0 AND emb_count > 0")
	case 0: // 默认全部，不做筛选
		// 不做任何操作
	}

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if w.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+w.OrderCode+"%")
	}
	if w.SPU != "" {
		db = db.Where("spu LIKE ?", "%"+w.SPU+"%")
	}
	if w.SKU != "" {
		db = db.Where("sku LIKE ?", "%"+w.SKU+"%")
	}
	if w.WorkCode != "" {
		db = db.Where("work_code LIKE ?", "%"+w.WorkCode+"%")
	}

	if w.WorkState != 0 {
		db = db.Where("work_state = ?", w.WorkState)
	}

	if w.PrintCount == 1 {
		db = db.Where("print_count = 1")
	}

	if w.PrintCount == 2 {
		db = db.Where("print_count > 1")
	}
	if w.ColorType == "0" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if w.ColorType == "2" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if len(w.LogisticsType) > 0 {
		db = db.Where("logistics_type like ?", "%"+w.LogisticsType+"%")
	}

	//if w.SuspendState > 0 {
	//	db = db.Where("suspend_state = ? AND resume_time < 10", w.SuspendState)
	//}

	suspendSqlStr := ""
	for _, oneSuspendState := range suspendStates {
		switch oneSuspendState {
		case 0: // 未暂停
			sqlStr := "((suspend_state < 1 && resume_time < 1) OR resume_time > 10)"
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		case 2, 100: // 全部暂停， 发货暂停
			sqlStr := fmt.Sprintf("(suspend_state = %d AND resume_time < 10)", oneSuspendState)
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		}
	}

	log.Debug("suspendSqlStr: ", suspendSqlStr)

	if len(suspendSqlStr) > 0 {
		db = db.Where(suspendSqlStr)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 排除部分工单号查询工单
func (w *WorkOrder) GetListByExclusionWorkCodeList(pn, ps int, timeS, timeE int64, suspendStates []int, excludedWorkCodeList []string, craft int) (list []*WorkOrder, count int, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("work_code NOT IN (?)", excludedWorkCodeList)

	// Craft filtering logic
	switch craft {
	case 1: // 仅DTG
		db = db.Where("print_count > 0 AND emb_count = 0")
	case 2: // 仅刺绣
		db = db.Where("print_count = 0 AND emb_count > 0")
	case 3: // DTG和刺绣都有
		db = db.Where("print_count > 0 AND emb_count > 0")
	case 0: // 默认全部，不做筛选
		// 不做任何操作
	}

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if w.OrderCode != "" {
		db = db.Where("order_code LIKE ?", "%"+w.OrderCode+"%")
	}
	if w.SPU != "" {
		db = db.Where("spu LIKE ?", "%"+w.SPU+"%")
	}
	if w.SKU != "" {
		db = db.Where("sku LIKE ?", "%"+w.SKU+"%")
	}
	if w.WorkCode != "" {
		db = db.Where("work_code LIKE ?", "%"+w.WorkCode+"%")
	}

	if w.WorkState != 0 {
		db = db.Where("work_state = ?", w.WorkState)
	}

	if w.PrintCount == 1 {
		db = db.Where("print_count = 1")
	}

	if w.PrintCount == 2 {
		db = db.Where("print_count > 1")
	}
	if w.ColorType == "0" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if w.ColorType == "2" {
		db = db.Where("color_type = ?", w.ColorType)
	}
	if len(w.LogisticsType) > 0 {
		db = db.Where("logistics_type like ?", "%"+w.LogisticsType+"%")
	}

	// Handling suspend states
	suspendSqlStr := ""
	for _, oneSuspendState := range suspendStates {
		switch oneSuspendState {
		case 0: // 未暂停
			sqlStr := "((suspend_state < 1 && resume_time < 1) OR resume_time > 10)"
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		case 2, 100: // 全部暂停，发货暂停
			sqlStr := fmt.Sprintf("(suspend_state = %d AND resume_time < 10)", oneSuspendState)
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		}
	}

	log.Debug("suspendSqlStr: ", suspendSqlStr)

	if len(suspendSqlStr) > 0 {
		db = db.Where(suspendSqlStr)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (w *WorkOrder) GetTimeoutList(pn, ps int, suspendStates, workStateList []int, craft int) (list []*WorkOrder, count int, err error) {
	db := mysql.NewConn().Table(w.TableName())
	// Craft filtering logic
	switch craft {
	case 1: // 仅DTG
		db = db.Where("print_count > 0 AND emb_count = 0")
	case 2: // 仅刺绣
		db = db.Where("print_count = 0 AND emb_count > 0")
	case 3: // DTG和刺绣都有
		db = db.Where("print_count > 0 AND emb_count > 0")
	case 0: // 默认全部，不做筛选
		// 不做任何操作
	}

	if w.ProduceFactory != 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	//if w.WorkState != 0 {
	//	db = db.Where("work_state = ?", w.WorkState)
	//}
	//或条件多选各状态
	var workStateSQLList []string
	for _, state := range workStateList {
		var sqlStr string
		switch state {
		case YSE_PRINT:
			sqlStr = fmt.Sprintf("(work_state = %d AND print_time <= %d)", YSE_PRINT, time.Now().Add(-30*time.Hour).Unix())
		case IN_PRODUCTION:
			sqlStr = fmt.Sprintf("(work_state = %d AND produce_s_time <= %d)", IN_PRODUCTION, time.Now().Add(-24*time.Hour).Unix())
		case OK_PRODUCTION:
			sqlStr = fmt.Sprintf("(work_state = %d AND produce_e_time <= %d)", OK_PRODUCTION, time.Now().Add(-24*time.Hour).Unix())
		case TESTING_OK:
			sqlStr = fmt.Sprintf("(work_state = %d AND testing_time <= %d)", TESTING_OK, time.Now().Add(-24*time.Hour).Unix())
		case HAVE_WAYBILL:
			sqlStr = fmt.Sprintf("(work_state = %d AND waybill_time <= %d)", HAVE_WAYBILL, time.Now().Add(-24*time.Hour).Unix())
		default:
			continue
		}
		workStateSQLList = append(workStateSQLList, sqlStr)
	}
	if len(workStateSQLList) > 0 {
		db = db.Where(strings.Join(workStateSQLList, " or "))
	} else if w.WorkState != 0 {
		db = db.Where("work_state = ?", w.WorkState)
	}

	suspendSqlStr := ""
	for _, oneSuspendState := range suspendStates {
		switch oneSuspendState {
		case 0: // 未暂停
			sqlStr := "((suspend_state < 1 && resume_time < 1) OR resume_time > 10)"
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		case 2, 100: // 全部暂停， 发货暂停
			sqlStr := fmt.Sprintf("(suspend_state = %d AND resume_time < 10)", oneSuspendState)
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		}
	}

	if len(suspendSqlStr) > 0 {
		db = db.Where(suspendSqlStr)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (w *WorkOrder) GetWorkInfo() error {
	return mysql.NewConn().Model(w).Preload("OrderItem").Preload("OrderItem.SkuInfo").Preload("OrderInfo").First(w).Error
}

func (w *WorkOrder) GetWorkInfoByCondition() (err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_code = ?", w.WorkCode)

	err = db.Preload("OrderItem").Preload("OrderItem.SkuInfo").Preload("OrderInfo").First(w).Error
	return
}

func (w *WorkOrder) GetWorkInfoWithParam(id uint, workCode string) error {
	db := mysql.NewConn().Table(w.TableName())
	if (id > 0) && (len(workCode) > 0) {
		db = db.Where("id = ? OR work_code = ?", id, workCode)
	} else if id > 0 {
		db = db.Where("id = ?", id)
	} else if len(workCode) > 0 {
		db = db.Where("work_code = ?", workCode)
	}
	return db.Preload("OrderItem").Preload("OrderItem.SkuInfo").Preload("OrderInfo").First(w).Error
}

func (w *WorkOrder) GetRescanCheckoutInfo() error {
	return mysql.NewConn().Model(w).First(w).Error
}

func (w *WorkOrder) GetScanCheckoutInfo() error {
	return mysql.NewConn().Model(w).Preload("OrderInfo").First(w).Error
}

func (w *WorkOrder) GetCheckoutInfo() error {
	return mysql.NewConn().Model(w).Preload("OrderItem").Preload("OrderInfo").First(w).Error
}

// 获取所有 已出工单 && 未生成arxp文件 的工单
func (w *WorkOrder) GetToBeGenerateArxpFileList( /*printTime int64*/ ) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Model(w)

	// 工单为 已出工单 状态
	//db = db.Where("work_state = ?", YSE_PRINT)

	// 工单为 未打印 或 已出工单 状态
	db = db.Where("work_state = ? OR work_state = ? OR work_state = ?", NOT_PRINT, YSE_PRINT, IN_PRODUCTION)

	// 未生成 arxp 文件
	db = db.Where("generated_arxp_file = ?", w.GeneratedArxpFile)

	//if printTime > 0 {
	//	db = db.Where("print_time > ?", printTime)
	//}

	if w.ID > 0 {
		db = db.Where("id > ?", w.ID)
	}

	err = db.Preload("OrderItem").Preload("OrderInfo").Order("id asc").Find(&list).Error
	return
}

// 获取所有 已出工单 && 未生成arxp文件 的工单
// => 获取所有未下载质检图片的工单
func (w *WorkOrder) GetTodDownloadCheckoutFileList( /*printTime int64*/ ) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Model(w)

	// 工单为 已出工单 状态
	//db = db.Where("work_state = ?", YSE_PRINT)

	// 工单为 未打印 或 已出工单 状态
	db = db.Where("work_state = ? OR work_state = ? OR work_state = ? OR work_state = ? OR work_state = ? OR work_state = ? OR work_state = ?", NOT_PRINT,
		YSE_PRINT, IN_PRODUCTION, OK_PRODUCTION, UNQUALIFIED, TESTING_OK, HAVE_WAYBILL)

	// 未下载质检图片的
	//db = db.Where("generated_arxp_file = ?", w.GeneratedArxpFile)
	db = db.Where("download_checkout_image_file = ?", w.DownloadCheckoutImageFile)

	if w.CreatedAt.Unix() > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", w.CreatedAt.Unix())
	}

	//if printTime > 0 {
	//	db = db.Where("print_time > ?", printTime)
	//}

	if w.ID > 0 {
		db = db.Where("id > ?", w.ID)
	}

	err = db.Preload("OrderItem").Preload("OrderInfo").Order("id asc").Find(&list).Error
	return
}

// 获取所有未生成打印预览组合图的工单
func (w *WorkOrder) GetCombinationPictrueList() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Model(w)

	// 工单为 未打印 或 已出工单 状态
	db = db.Where("work_state = ? OR work_state = ? OR work_state = ? OR work_state = ? OR work_state = ? OR work_state = ?", NOT_PRINT,
		YSE_PRINT, IN_PRODUCTION, OK_PRODUCTION, UNQUALIFIED, TESTING_OK /*, HAVE_WAYBILL*/)

	if w.CreatedAt.Unix() > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", w.CreatedAt.Unix())
	}

	db = db.Where("special_state & ? < 1", SpecialStateCombinationPicture)

	if w.ID > 0 {
		db = db.Where("id > ?", w.ID)
	}

	err = db.Preload("OrderItem").Preload("OrderInfo").Order("id asc").Find(&list).Error
	return
}

func (w *WorkOrder) GetPrintInfo(idList []uint) (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("id IN (?)", idList).Preload("OrderInfo").Preload("OrderItem").Preload("OrderItem.SkuInfo").
		Preload("OrderItem.SkuInfo.ProductInfo").
		Find(&list).Error
	return
}

// 更改工单状态和时间
func (w *WorkOrder) PutWorkState(idList []uint, updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	//todo:这里有点问题
	db = db.Model(&WorkOrder{}).Where("id IN (?)", idList)

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Updates(updateMap).Error
	return
}

// 兼容取消工单的逻辑
func (w *WorkOrder) PutWorkStateWithoutCancel(idList []uint, updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("work_state != ?", CANCEL_STATE).Where("id IN (?)", idList)

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Updates(updateMap).Error
	return
}

// 更改工单状态和时间
func (w *WorkOrder) PutPrintWork(idList []uint, updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	//todo:这里有点问题
	db = db.Model(&WorkOrder{}).Where("id IN (?)", idList)

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Where("work_state < 2").Updates(updateMap).Error
	return
}

func (w *WorkOrder) PutTest() error {
	return mysql.NewConn().Model(w).Where("id = ?", w.ID).Update("design_bit", w.DesignBit).Error
}

func (w *WorkOrder) GetListByOrderCode() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).Find(&list).Error
	return
}

type WorkStateHead struct {
	ID               uint   `json:"id"`
	OrderCode        string `json:"order_code"`         //平台内订单号
	WorkCode         string `json:"work_code"`          //工单号
	WorkState        int    `json:"work_state"`         //工单状态
	SpecialState     uint64 `json:"special_state"`      // 特殊状态记录 --目前用来处理包装吊牌的生产，linxb 2023-09-14
	BrandDeliveryCar string `json:"brand_delivery_car"` // 分配的吊牌和包装拣车号
}

// 根据订单号，查询所有工单的状态
func (w *WorkOrder) GetHeadListByOrderCodes(orderCodes []string) (list []*WorkStateHead, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code IN (?)", orderCodes).Find(&list).Error
	return
}

func (w *WorkOrder) GetVerifyListByOrderCode() (list []*DeliveryCar, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).Find(&list).Error
	return
}

type WorkCodeItem struct {
	WorkCode         string `json:"work_code"`          // 工单号
	BrandDeliveryCar string `json:"brand_delivery_car"` // 分配的吊牌和包装拣车号
	WorkState        int    `json:"work_state"`         // 工单状态
}

type WorkCodeAndDeliveryCarItem struct {
	WorkCode         string `json:"work_code"`                        // 工单号
	DeliveryCar      string `json:"delivery_car"`                     // 拣车号，前端追加到质检合格后
	BrandDeliveryCar string `json:"brand_delivery_car"`               // 分配的吊牌和包装拣车号
	SpecialState     uint64 `json:"special_state" gorm:"default:0"`   // 特殊状态记录 --目前用来处理包装吊牌的生产，linxb 2023-09-14
	WorkState        int    `json:"work_state"`                       // 工单状态 用来判断是否加库存 2024年9月9日14:44:17-zc
	ProduceFactory   int    `json:"produce_factory" gorm:"default:0"` // 生产工厂id，目前直接使用【主键id】
}

// 根据订单号获取所有工单号
func (w *WorkOrder) GetWorkCodesByOrderCode() (list []*WorkCodeAndDeliveryCarItem, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).Find(&list).Error
	return
}

func (w *WorkOrder) GetListByOrderCodeAndSku() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).
		Where("sku = ?", w.SKU).Find(&list).Error
	return
}

func (w *WorkOrder) QueryByOrderItemId() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_item_id = ?", w.OrderItemID).First(w).Error
	return
}

type SkuStaticListItem struct {
	Total int64  `json:"total"`
	SKU   string `json:"sku"` //sku信息，额外的筛选项
}

type SkuStaticListItems []*SkuStaticListItem

func (d SkuStaticListItems) Len() int { // 重写 Len() 方法
	return len(d)
}
func (d SkuStaticListItems) Swap(i, j int) { // 重写 Swap() 方法
	d[i], d[j] = d[j], d[i]
}
func (d SkuStaticListItems) Less(i, j int) bool { // 重写 Less() 方法， 从大到小排序
	return d[j].Total < d[i].Total
}

func (w *WorkOrder) StaticSku(orderCodes []string, workCodes []string) (list []*SkuStaticListItem, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(orderCodes) > 0 {
		db = db.Where("order_code IN (?)", orderCodes)
	}

	if len(workCodes) > 0 {
		db = db.Where("work_code IN (?)", workCodes)
	}

	err = db.Select("count(*) as total,sku").
		Group("sku").Scan(&list).Error

	if err != nil {
		return nil, err
	}
	sort.Sort(SkuStaticListItems(list))
	return
}

func (w *WorkOrder) QueryListByOrderCode() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).Find(&list).Error
	return
}

type FilterCheckoutImg struct {
	SKU      string `json:"sku"`       //sku信息，额外的筛选项
	WorkCode string `json:"work_code"` //工单号
}

func (w *WorkOrder) QueryListToFilterCheckoutImg() (list []*FilterCheckoutImg, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("spu = ?", w.SPU).Find(&list).Error
	return
}

func (w *WorkOrder) QuerySingleListByOrderCodes(orderCodes []string) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("order_code IN (?)", orderCodes).Where("work_code NOT LIKE '%-%'")
	if w.WorkState > 0 {
		db = db.Where("work_state = ?", w.WorkState)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if w.FactoryIdCheckout > 0 {
		db = db.Where("factory_id_checkout = ?", w.FactoryIdCheckout)
	}

	err = db.Order("testing_time asc").Find(&list).Error
	return
}

func (w *WorkOrder) QueryListByOrderCodes(orderCodes []string) (list []*DeliveryCar, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("order_code IN (?)", orderCodes)
	err = db.Order("testing_time asc").Find(&list).Error
	return
}

func (w *WorkOrder) QueryListByWorkCodes(workCodes []string) (list []*DeliveryCar, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("work_code IN (?)", workCodes)
	err = db.Order("testing_time asc").Find(&list).Error
	return
}

// 用来更新包装吊牌生产工单筛选，将 打印时间小于 1699407010 和 工单状态 等于未打印的工单筛选出来
func (w *WorkOrder) QueryListForUpdateSpecialState() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if (w.WorkState > 0) && (w.PrintTime > 0) {
		db = db.Where("work_state = ? OR print_time > ?", w.WorkState, w.PrintTime)
	}

	err = db.Find(&list).Error
	return
}

// 根据订单号或工单号查询所有工单
func (w *WorkOrder) GetListWithOrderCodeOrWorkCode(workCodes, orderCodes []string) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(workCodes) > 0 {
		db = db.Where("work_code IN (?)", workCodes)
	}

	if len(orderCodes) > 0 {
		db = db.Where("order_code IN (?)", orderCodes)
	}

	err = db.Find(&list).Error
	return
}

type WorkCode struct {
	WorkCode string `json:"work_code"` // 工单号
}

func (w *WorkOrder) QueryWorkCodesByOrderCode() (workCode string, itemCount int, err error) {

	db := mysql.NewConn().Table(w.TableName())

	workCodeStruct := make([]*WorkCode, 0)

	// 去除【已取消】的工单
	db = db.Where("work_state != ?", CANCEL_STATE)

	err = db.Select("work_code").Where("order_code = ?", w.OrderCode).Find(&workCodeStruct).Error

	if err == nil {
		itemCount = len(workCodeStruct)
		if itemCount > 0 {
			workCode = workCodeStruct[0].WorkCode
		}
	}
	return
}

func (w *WorkOrder) QueryByWorkOrderId() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_code = ?", w.WorkCode).First(w).Error
	return
}

func (w *WorkOrder) GetListDeliveriedButRetainBrandDeliveryCar() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 已贴运单状态，还占用包装吊牌拣车的工单
	db = db.Where("work_state = ?", HAVE_WAYBILL).Where("special_state & ? > 0", SpecialStateRetainBrandDeliveryCar)

	err = db.Find(&list).Error
	return
}

func (w *WorkOrder) ReleaseEmptyBrandDeliveryCar() (err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 已贴运单状态，还占用包装吊牌拣车的工单
	db = db.Where("work_state = ?", HAVE_WAYBILL).Where("special_state & ? > 0", SpecialStateRetainBrandDeliveryCar)

	err = db.UpdateColumn("special_state", gorm.Expr("special_state & ?", ^(SpecialStateRetainBrandDeliveryCar))).Error
	return
}

func (w *WorkOrder) Query() (err error) {

	db := mysql.NewConn().Table(w.TableName())

	if w.CreatedAt.Unix() > 0 {
		db = db.Where("created_at >= ?", w.CreatedAt)
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(w.WorkCode) > 1 {
		db = db.Where("work_code LIKE ?", "%"+w.WorkCode+"%")
	}

	err = db.First(w).Error

	return
}

func (w *WorkOrder) QueryByWorkCode() (err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_code = ?", w.WorkCode)

	err = db.First(w).Error

	return
}

func (w *WorkOrder) GetListTest() (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("LENGTH(brand_delivery_car) > 0").Where("work_state < ?", HAVE_WAYBILL)

	err = db.Find(&list).Error

	return
}

func (w *WorkOrder) GetListInWorkStates(workStates []int) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(workStates) < 1 {
		return
	}

	db = db.Where("work_state IN (?)", workStates)

	if len(w.SKU) > 0 {
		db = db.Where("sku like ?", "%"+w.SKU+"%")
	}

	err = db.Group("order_code").Find(&list).Error

	return
}

func (w *WorkOrder) GetListInWorkStatesByCreatedAt(timeS, timeE int64, workStates []int, suspendStates []int) (list []*WorkOrder, count int, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(workStates) > 0 {
		db = db.Where("work_state IN (?)", workStates)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	suspendSqlStr := ""
	for _, oneSuspendState := range suspendStates {
		switch oneSuspendState {
		case 0: // 未暂停
			sqlStr := "((suspend_state < 1 && resume_time < 1) OR resume_time > 10)"
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		case 2, 100: // 全部暂停， 发货暂停
			sqlStr := fmt.Sprintf("(suspend_state = %d AND resume_time < 10)", oneSuspendState)
			if len(suspendSqlStr) > 0 {
				suspendSqlStr = fmt.Sprintf("%s OR %s", suspendSqlStr, sqlStr)
			} else {
				suspendSqlStr = sqlStr
			}
		}
	}

	err = db.Count(&count).Find(&list).Error

	return
}

// 根据拣车号查询所有未发货的工单
func (w *WorkOrder) QueryNotDeliveryListByBrandCarBox() (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(w.BrandDeliveryCar) > 0 {
		db = db.Where("brand_delivery_car = ?", w.BrandDeliveryCar)
	}

	//db = db.Where("work_state <= ?", TESTING_OK)
	db = db.Where("special_state & ? > 0", SpecialStateRetainBrandDeliveryCar) // 如果还有留在包装吊牌拣车上的

	err = db.Find(&list).Error

	return
}

// 根据多个拣车号查询所有未发货的工单
func (w *WorkOrder) QueryNotDeliveryListByBrandCarBoxs(brandDeliveryCars []string) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("brand_delivery_car IN (?)", brandDeliveryCars)

	//db = db.Where("work_state <= ?", TESTING_OK)
	db = db.Where("special_state & ? > 0", SpecialStateRetainBrandDeliveryCar) // 如果还有留在包装吊牌拣车上的

	err = db.Find(&list).Error

	return
}

// 查询
func (w *WorkOrder) GetUnreleasedListWithBrandDeliveryCar() (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(w.BrandDeliveryCar) > 0 {
		db = db.Where("brand_delivery_car = ?", w.BrandDeliveryCar)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	err = db.Find(&list).Error

	return
}

func (w *WorkOrder) QueryDetail() (err error) {

	db := mysql.NewConn().Table(w.TableName())

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code LIKE ?", "%"+w.WorkCode+"%")
	}

	err = db.Preload("OrderItem").
		Preload("OrderItem.TagDesignInfo").Preload("OrderItem.TagDesignInfo.HangTagInfo").
		Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		First(w).Error

	return
}

func (w *WorkOrder) GetListBySkuLike(skus []string) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	sqlStr := ""
	for _, oneSku := range skus {
		if len(sqlStr) < 1 {
			sqlStr = fmt.Sprintf("sku LIKE '%s%%'", oneSku)
			continue
		}
		sqlStr = fmt.Sprintf("%s OR sku LIKE '%s%%'", sqlStr, oneSku)
	}

	db = db.Where(sqlStr).Where("work_state < ?", HAVE_WAYBILL)

	err = db.Group("order_code").Find(&list).Error

	return
}

// 用来清除 衣服拣车 和 包装吊牌拣车
func (w *WorkOrder) QueryListByDeliveryCar() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.BrandDeliveryCar) > 0 {
		db = db.Where("brand_delivery_car = ?", w.BrandDeliveryCar)
	}

	if len(w.DeliveryCar) > 0 {
		db = db.Where("delivery_car = ?", w.DeliveryCar)
	}

	err = db.Where("work_state <= ?", TESTING_OK).Find(&list).Error
	return
}

type DeliveryCar struct {
	OrderCode    string `json:"order_code"`                     //平台内订单号
	WorkCode     string `json:"work_code"`                      //工单号
	OrderItemID  uint   `json:"order_item_id"`                  //订单子项id 直接关联
	DeliveryCar  string `json:"delivery_car"`                   // 拣车号，前端追加到质检合格后
	WorkState    int    `json:"work_state"`                     //工单状态
	SpecialState uint64 `json:"special_state" gorm:"default:0"` // 特殊状态记录 --目前用来处理包装吊牌的生产，linxb 2023-09-14
}

// 用来查询要恢复的拣车号
func (w *WorkOrder) GetListToRecoverDeliveryCar() (list []*DeliveryCar, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("LENGTH(delivery_car) > 0").Where("(work_state < ?) OR (work_state = ? AND (special_state & ?) > 0) OR (work_state = ? AND (special_state & ?) > 0)",
		HAVE_WAYBILL, HAVE_WAYBILL, SpecialStateRetainDeliveryCar, CANCEL_STATE, SpecialStateRetainDeliveryCar)

	err = db.Find(&list).Error

	return
}

// 获取准备释放的包装吊牌拣车
func (w *WorkOrder) GetListToRleaseBrandDeliveryCar() (list []*DeliveryCar, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db. /*.Where("work_state <= ?", TESTING_OK)*/ Where("LENGTH(brand_delivery_car) > 0 AND LENGTH(brand_delivery_car) < 20")

	// 并且是占用包装吊牌拣车的
	db = db.Where("(special_state & ? > 0) AND (work_state = ?)", SpecialStateRetainBrandDeliveryCar, HAVE_WAYBILL)

	err = db.Find(&list).Error

	return
}

// 根据拣车号来筛选工单
func (w *WorkOrder) GetListByDeliveryCar() (list []*DeliveryCar, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.DeliveryCar) < 1 {
		return
	}

	db = db.Where("delivery_car = ?", w.DeliveryCar)

	db = db.Where("(work_state < ?) OR (work_state = ? AND (special_state | ?) > 0)",
		HAVE_WAYBILL, HAVE_WAYBILL, SpecialStateRetainDeliveryCar)

	err = db.Order("work_state asc").Find(&list).Error

	return
}

// 用来清除 衣服拣车 和 包装吊牌拣车
func (w *WorkOrder) GetListUnreleasedBrandBox() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 存在包装吊牌拣车
	db = db. /*.Where("work_state <= ?", TESTING_OK)*/ Where("LENGTH(brand_delivery_car) > 0 AND LENGTH(brand_delivery_car) < 20")

	// 并且是占用包装吊牌拣车的
	db = db.Where("(special_state & ? > 0) OR (work_state < ?)", SpecialStateRetainBrandDeliveryCar, TESTING_OK)

	err = db.Find(&list).Error

	return
}

// 根据工单状态筛选，目前用于清除 已贴运单 但是没回回收包装吊牌拣车的
func (w *WorkOrder) GetUnreleasedBrandBoxWithState(workStates []int) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 存在包装吊牌拣车
	db = db. /*.Where("work_state <= ?", TESTING_OK)*/ Where("LENGTH(brand_delivery_car) > 0 AND LENGTH(brand_delivery_car) < 20")

	// 并且是占用包装吊牌拣车的
	db = db.Where("(special_state & ? > 0) AND (work_state IN(?))", SpecialStateRetainBrandDeliveryCar, workStates)

	err = db.Find(&list).Error

	return
}

func (w *WorkOrder) QueryCheckBrandDelivery() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("work_state < ?", TESTING_OK)

	if len(w.BrandDeliveryCar) > 0 {
		db = db.Where("brand_delivery_car = ?", w.BrandDeliveryCar)
	}

	err = db.Find(&list).Error
	return
}

func (w *WorkOrder) QueryToBrandProduce(sTime time.Time) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("created_at <= FROM_UNIXTIME(?)", w.CreatedAt.Unix())
	db = db.Where("created_at >= FROM_UNIXTIME(?)", sTime.Unix())

	db = db.Where("tag_design_id > 0 OR pack_design_id > 0 ")

	err = db.Find(&list).Error

	return
}

// 获取未打印的工单
func (w *WorkOrder) GetNotPrintBrand() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_state = ?", NOT_PRINT).
		Where("brand_delivery_car = ?", "品牌领标和自定义包装当前无法找到符合条件的盒子了").Where("tag_design_id > 0 OR pack_design_id > 0").
		Order("id desc").Find(&list).Error
	return
}

// 吊牌损耗重新生成
func (w *WorkOrder) SetHangTagMinusReproduce(workCodes []string, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	updateMap["tag_produce_minus"] = BrandMinusStateReduce
	updateMap["updated_at"] = time.Now()

	err = db.Model(&WorkOrder{}).Where("work_code IN (?)", workCodes).Updates(updateMap).Error

	return
}

// 包装设置为已生产
func (w *WorkOrder) SetPackagingProduced(workCodes []string, isMinus bool, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	//if len(workCodes) > 0 {
	db = db.Where("work_code IN (?)", workCodes)
	//}

	if !isMinus {
		// 不是损耗
		// 包装
		err = db.Where("special_state & ? > 0", SpecialStatePackagingHas).
			UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStatePackagingHasProduced)).Error
		if err != nil {
			return
		}

		// 吊牌
		err = db.Where("special_state & ? > 0", SpecialStateTagHas).
			UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateTagHasProduced)).Error
		if err != nil {
			return
		}
	} else {
		// 损耗的生产
		err = db.UpdateColumn("special_state", gorm.Expr("special_state & ?", ^(SpecialStatePackagingMinus+SpecialStateTagMinus))).Error
	}

	return
}

// 目前来说，如果要更新拣车状态的话，就必须要穿 订单号，工单号，工单id 中的至少一个（一般是传其中一个）
func (w *WorkOrder) UpdateRetainDeliveryCar(isRetain bool, workIdList []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	if len(w.OrderCode) > 0 {
		db = db.Where("order_code = ?", w.OrderCode)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(workIdList) > 0 {
		db = db.Where("id IN (?)", workIdList)
	}

	if isRetain {
		err = db.UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateRetainDeliveryCar)).Error
	} else {
		err = db.UpdateColumn("special_state", gorm.Expr("special_state & ?", ^SpecialStateRetainDeliveryCar)).Error
	}

	if err == nil {
		if !w.UpdatedAt.IsZero() {
			err = db.UpdateColumn("updated_at", w.UpdatedAt).Error
		} else {
			err = db.UpdateColumn("updated_at", time.Now()).Error
		}
	}

	return
}

// 吊牌设置为已生产
func (w *WorkOrder) SetTagProduced(workCodes []string, isMinus bool, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	//if len(workCodes) > 0 {
	db = db.Where("work_code IN (?)", workCodes)
	//}

	if !isMinus {
		err = db.UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateTagHasProduced)).Error
	} else {
		err = db.UpdateColumn("special_state", gorm.Expr("special_state & ?", ^SpecialStateTagMinus)).Error
	}

	return
}

// 包装损耗重新生成
func (w *WorkOrder) SetPackagingMinusReproduce(workCodes []string, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}
	updateMap["packaging_produce_minus"] = BrandMinusStateReduce
	updateMap["updated_at"] = time.Now()

	err = db.Model(&WorkOrder{}).Where("work_code IN (?)", workCodes).Updates(updateMap).Error

	return
}

// 吊牌和包装进行重新生产校验
func (w *WorkOrder) CheckReGenerateBrandProduce() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_code = ?", w.WorkCode).
		// TODO 给极文测试使用
		/*Where("work_state = ? OR work_state = ?", OK_PRODUCTION, UNQUALIFIED).*/ First(w).Error
	return
}

func (w *WorkOrder) GetBrandProduce() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if w.TagProduceMinus > 0 {
		db = db.Where("tag_produce_minus = ?", w.TagProduceMinus)
	}

	if w.TagDesignType > 0 {
		db = db.Where("tag_design_type = ?", w.TagDesignType)
	}

	if w.PackagingProduceMinus > 0 {
		db = db.Where("packaging_produce_minus = ?", w.PackagingProduceMinus)
	}

	if w.PackDesignType > 0 {
		db = db.Where("pack_design_type = ?", w.PackDesignType)
	}

	err = db.Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error
	return
}

func (w *WorkOrder) QueryByWorkCodesWithBrandProduce(workCodes []string) (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_code IN (?)", workCodes).Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error
	return
}

// 根据工单号获取工单号，用于校验工单是否被删除
func (w *WorkOrder) GetWorkCodesByWorkCodes(workCodes []string) (list []*WorkCodeItem, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_code IN (?)", workCodes).Find(&list).Error
	return
}

type OrderItemIDMapWorkCode struct {
	OrderItemID uint   `json:"order_item_id"` //订单子项id 直接关联
	WorkCode    string `json:"work_code"`     //工单号
}

func (w *WorkOrder) QueryByOrderItemIDs(orderItems []uint) (res map[uint]string, err error) {
	orderItemIDMapWorkCode := make([]*OrderItemIDMapWorkCode, 0)

	err = mysql.NewConn().Table(w.TableName()).Select("order_item_id,work_code").Where("order_item_id IN (?)", orderItems).Find(&orderItemIDMapWorkCode).Error

	if err == nil {
		res = make(map[uint]string)
		for _, one := range orderItemIDMapWorkCode {
			res[one.OrderItemID] = one.WorkCode
		}
	}
	return
}

func (w *WorkOrder) QueryByWorkOrderIdWithBrandInfo() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("work_code = ?", w.WorkCode).Preload("OrderItem").
		Preload("OrderItem.TagDesignInfo").Preload("OrderItem.TagDesignInfo.HangTagInfo").
		Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		First(w).Error
	return
}

// 查找所有带有包装或吊牌的工单
func (w *WorkOrder) QueryAllWithBrand(workStates []int) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("tag_design_id > 0 OR pack_design_id > 0")

	// 根据工单状态筛选，没传的话就查全部
	if len(workStates) > 0 {
		db = db.Where("work_state IN (?)", workStates)
	}
	err = db.Find(&list).Error
	return
}

func (w *WorkOrder) QueryPrintWorkOrder(time_s, time_e int64) (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())
	//db = db.Select("work_order,order_item.tag_design_id,order_item.tag_price,order_item.tag_design,order_item.pack_design_id,order_item.pack_price,order_item.pack_design,order_item.tag_design_info,order_item.pack_design_info").
	//	Joins("left join order_item on work_order.order_item_id = order_item.id").Where("print_time >= ? AND print_time <= ?", time_s, time_e).Scan(list)
	//err = db.Error

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	err = db.Where("print_time >= ? AND print_time <= ?", time_s, time_e).Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error

	//err = mysql.NewConn().Table(w.TableName()).Where("print_time >= ? AND print_time <= ?", time_s, time_e).
	//	Where("tag_design_id > 0 OR pack_design_id > 0").Order("id asc").Find(&list).Error
	return
}

// 查找是否该订单所有工单都质检合格，并排除已取消的工单
func (w *WorkOrder) GetTestingOKCount() (count int, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).
		Where("testing_time = 0 and work_state != ?", CANCEL_STATE).Count(&count).Error
	return
}

// 查找是质检不合格的工单数量
func (w *WorkOrder) GetTestingNotOKCount() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).
		Where("testing_time = 0 || work_state = ?", UNQUALIFIED).Where("work_state != ?", CANCEL_STATE).Find(&list).Error
	return
}

func (w *WorkOrder) GetProductionOKCount() (count int, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).
		Where("produce_e_time = 0").Count(&count).Error
	return
}

// 根据订单 ID 删除工单
func (w *WorkOrder) DeleteByOrderCode() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode).Delete(w).Error
	return
}

// 获取所有工单
func (w *WorkOrder) GetAllWorkOrder() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Order("id desc").Find(&list).Error
	return
}

// 更新工单内容
func (w *WorkOrder) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("id = ?", w.ID)

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).First(w).Error
	return
}

// 打印工单时，专属拣车相关的更新
func (w *WorkOrder) UpdatePickingCar(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("id = ?", w.ID)

	updateMap := make(map[string]interface{})

	if w.DbVersion > 0 {
		//w.DbVersion = w.DbVersion + 1
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		//w.DbVersion = workOrderDbVersion.DbVersion + 1
		updateMap["db_version"] = workOrderDbVersion.DbVersion + 1
	}

	// 更新拣车号
	updateMap["brand_delivery_car"] = w.BrandDeliveryCar
	// 包装和吊牌标记为已生产
	// 不需要清除占用普通包装吊牌拣车的标记，因为在包装吊牌质检时分配拣车的时候，才会标记为【占用拣车】
	specialState := SpecialStatePickingCar | SpecialStateRetainBrandDeliveryCar
	if w.PackDesignID > 0 {
		//updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStatePackagingHasProduced)
		specialState = specialState | SpecialStatePackagingHasProduced
	}
	if w.TagDesignID > 0 {
		//updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStateTagHasProduced)
		specialState = specialState | SpecialStateTagHasProduced
	}
	updateMap["special_state"] = gorm.Expr("special_state | ?", specialState)

	err = db.Updates(updateMap).First(w).Error
	return
}

// 批量更新工单内容
func (w *WorkOrder) UpdateBatch(workCodes []string, mapData map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	if w.DbVersion > 0 {
		mapData["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		mapData["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Model(&WorkOrder{}).Where("work_code IN (?)", workCodes).Updates(mapData).Error
	return
}

// 追加字符
func (w *WorkOrder) Concat(picture string, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if (len(w.WorkCode) < 1) && (w.ID < 1) {
		err = gorm.ErrRecordNotFound
	}

	// 如果有最新更新时间，就更新
	if !w.UpdatedAt.IsZero() {
		db = db.UpdateColumn("updated_at = ?", w.UpdatedAt)
	}

	err = db.UpdateColumn("checkout_pictures", gorm.Expr(fmt.Sprintf("CONCAT(checkout_pictures,'|%s')", picture))).Error

	return
}

// 更新特殊标记位
func (w *WorkOrder) UpdateSpecialState(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["special_state"] = w.SpecialState

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Model(&WorkOrder{}).Where("id = ?", w.ID).Updates(updateMap).Error
	return
}

// 包装吊牌拣车回收了
func (w *WorkOrder) ReleaesBrandDeliveryCar(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	if (w.ID < 1) && (len(w.WorkCode) < 1) {
		err = gorm.ErrRecordNotFound
		return
	}

	// 如果有最新更新时间，就更新
	if !w.UpdatedAt.IsZero() {
		db = db.UpdateColumn("updated_at = ?", w.UpdatedAt)
	}

	err = db.UpdateColumn("special_state", gorm.Expr("special_state & ?", ^SpecialStateRetainBrandDeliveryCar)).Error
	return
}

// 包装吊牌拣车回收了
func (w *WorkOrder) AllocBrandDeliveryCar(workCodes []string, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	if len(workCodes) < 1 {
		err = gorm.ErrRecordNotFound
		return
	}

	db = db.Where("work_code IN (?)", workCodes)

	err = db.UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateRetainBrandDeliveryCar)).Error
	return
}

// 更新工单内容
func (w *WorkOrder) UpdateSuspend(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(w.OrderCode) > 0 {
		db = db.Where("order_code = ?", w.OrderCode)
	}

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).Error
	return
}

type WorkOrderDbVersion struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	OrderCode string    `json:"updated_at"`                  //平台内订单号
	WorkCode  string    `json:"work_code"`                   //工单号
	DbVersion int64     `json:"db_version" gorm:"default:0"` // 数据版本
}

func (w *WorkOrder) UpdateWithCondition(condition map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{})

	for conditionKey, conditionValue := range condition {
		db = db.Where(fmt.Sprintf("%s = ?", conditionKey), conditionValue)
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).Error
	return
}

func (w *WorkOrder) CombinationPictureGenerated(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{})

	db = db.Where("id = ?", w.ID)

	// 如果有最新更新时间，就更新
	if !w.UpdatedAt.IsZero() {
		db = db.UpdateColumn("updated_at = ?", w.UpdatedAt)
	}

	err = db.UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateCombinationPicture)).Error
	return
}

// 更新工单内容
//func (w *WorkOrder) SetBrandProduce(workCodes []string, tx ...*gorm.DB) (err error) {
//	db := mysql.NewConn()
//	if len(tx) != 0 {
//		db = tx[0]
//	}
//	db = db.Table(w.TableName()).Where("order_code IN (?)", workCodes)
//
//	updateMap := make(map[string]interface{})
//	if w.TagProduceState > 0 {
//		updateMap["tag_produce_state"] = w.TagProduceState
//	}
//	if w.PackProduceState > 0 {
//		updateMap["pack_produce_state"] = w.PackProduceState
//	}
//
//	err = db.Updates(updateMap).Error
//	return
//}

// 更新工单内容
func (w *WorkOrder) UpdateByWorkCode(tx ...*gorm.DB) (affectedCount int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{}).Where("work_code = ?", w.WorkCode)

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	db = db.Updates(w)

	affectedCount = db.RowsAffected
	err = db.Error
	return
}

// 分配拣车后的更新，更新【拣车号】和【special_state 中是否占用包装吊牌拣车】
func (w *WorkOrder) UpdateBrandDeliveryCar(tx ...*gorm.DB) (affectedCount int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("work_code = ?", w.WorkCode)

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).Error
	if err != nil {
		return
	}

	updateMap := make(map[string]interface{})
	updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStateRetainBrandDeliveryCar)
	w.DbVersion++
	updateMap["db_version"] = w.DbVersion
	err = db.Updates(updateMap).Error
	if err != nil {
		return
	}

	affectedCount = db.RowsAffected
	err = db.Error
	return
}

// 更新工单内容
func (w *WorkOrder) UpdateInfo(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&WorkOrder{})
	updateMap := make(map[string]interface{})

	if len(w.OrderCode) > 0 {
		updateMap["order_code"] = w.OrderCode
		db = db.Where("order_code = ?", w.OrderCode)
	}

	if w.WorkState > 0 {
		updateMap["work_state"] = w.WorkState
	}

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Updates(updateMap).Error

	return
}

// 包装袋的库存损耗
func (w *WorkOrder) PackagingMinus(isPickingCar bool, tx ...*gorm.DB) (affectedCount int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	//updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStatePackagingMinus)
	updateMap["minus_time"] = time.Now().Unix()
	// 清除专属拣车标记即专属拣车号
	if isPickingCar {
		updateMap["special_state"] = gorm.Expr("special_state & ?", ^SpecialStatePickingCar)
		updateMap["brand_delivery_car"] = ""
	}

	if !w.UpdatedAt.IsZero() {
		updateMap["updated_at"] = w.UpdatedAt
	}

	db = db.Model(&WorkOrder{}).Where("work_code = ?", w.WorkCode).Where("special_state & ? > 0", SpecialStatePackagingHas).
		UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStatePackagingMinus)).
		//UpdateColumn("minus_time", time.Now().Unix()).
		Updates(updateMap)

	affectedCount = db.RowsAffected
	err = db.Error
	return
}

// 吊牌的库存损耗
func (w *WorkOrder) HangeTagMinus(isPickingCar bool, tx ...*gorm.DB) (affectedCount int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	//updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStateTagMinus)
	updateMap["minus_time"] = time.Now().Unix()
	// 清除专属拣车标记即专属拣车号
	if isPickingCar {
		updateMap["special_state"] = gorm.Expr("special_state & ?", ^SpecialStatePickingCar)
		updateMap["brand_delivery_car"] = ""
	}

	if !w.UpdatedAt.IsZero() {
		updateMap["updated_at"] = w.UpdatedAt
	}

	db = db.Model(&WorkOrder{}).Where("work_code = ?", w.WorkCode).Where("special_state & ? > 0", SpecialStateTagHas).
		UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateTagMinus)).
		//UpdateColumn("minus_time", time.Now().Unix()).
		Updates(updateMap)

	affectedCount = db.RowsAffected
	err = db.Error
	return
}

// 吊牌和吊牌的库存损耗
func (w *WorkOrder) BrandMinus(isPickingCar bool, tx ...*gorm.DB) (affectedCount int64, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	//updateMap["special_state"] = gorm.Expr("special_state | ?", SpecialStatePackagingMinus+SpecialStateTagMinus)
	updateMap["minus_time"] = time.Now().Unix()

	// 清除专属拣车标记即专属拣车号
	if isPickingCar {
		updateMap["special_state"] = gorm.Expr("special_state & ?", ^SpecialStatePickingCar)
		updateMap["brand_delivery_car"] = ""
	}

	db = db.Model(&WorkOrder{}).Where("work_code = ?", w.WorkCode).Where("special_state & ? > 0", SpecialStatePackagingHas).
		Where("special_state & ? > 0", SpecialStateTagHas).
		UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStatePackagingMinus+SpecialStateTagMinus)).
		//UpdateColumn("minus_time", time.Now().Unix()).
		Updates(updateMap)

	affectedCount = db.Updates(updateMap).RowsAffected
	err = db.Error
	return
}

// 根据工单主键id查找
func (w *WorkOrder) GetByID() (err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("id = ?", w.ID).First(w).Error
	return
}

type SkuCount struct {
	Sku   string `json:"sku"`
	Total int    `json:"total"`
}

func (w *WorkOrder) GetSkuCount(timeS, timeE int64) (list []*SkuCount, err error) {
	db := mysql.NewConn().Table(w.TableName()).Select("sku, count(*) as total")

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	err = db.Group("sku").Find(&list).Error

	return
}

// 根据日期查找sku销量
func (w *WorkOrder) GetSkuCountByDays(timeS, timeE int64) (count int, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("sku like ?", "%"+w.SKU+"%")
	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	err = db.Count(&count).Error
	return
}

type listTest struct {
	OrderCode string `json:"order_code"`
}

func (w *WorkOrder) GetYUNTest() (list []*listTest, err error) {
	err = mysql.NewConn().Table(w.TableName()).Select("order_code").Where("created_at >= FROM_UNIXTIME(?)", 1675221446).
		Where("work_state < 7").Where("logistics_type = ?", "YUN").Group("order_code").Find(&list).Error
	return
}

// 获取未生成 arxp 文件的工单，目前一次调用只返回最旧的十个
func (w *WorkOrder) QueryToGenerate() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("generated_arxp_file = ?", ArxpGeneratedSyncStateNo).Where("work_state < 7").
		Order("id asc").Limit(10).Find(&list).Error
	return
}

// 获取未生成 arxp 文件的工单，目前一次调用只返回最旧的十个
func (w *WorkOrder) QueryToGenerateReverse() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("generated_arxp_file = ?", ArxpGeneratedSyncStateNo).Where("work_state < 7").
		Order("id desc").Limit(10).Find(&list).Error
	return
}

func (w *WorkOrder) GetListBetweenDeliveryTime(deliveryTimeS, deliveryTimeE int64) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if deliveryTimeS > 0 && deliveryTimeE > 0 {
		db = db.Where("waybill_time >= ? AND waybill_time < ?", deliveryTimeS, deliveryTimeE)
	}

	err = db.Find(&list).Error

	return
}

// 获取未下载质检图片的工单
func (w *WorkOrder) QueryToDownloadCheckImage() (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("download_checkout_image_file = ?", CheckoutImageDownloadNo).Order("id asc").Limit(50).Find(&list).Error
	return
}

// QueryWaybillCount 根据orderCode 获取已贴运单的数量和 质检合格以后状态的数量
func (w *WorkOrder) QueryWaybillCount() (testingCount, waybillCount int, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("order_code = ?", w.OrderCode)

	err = db.Where("work_state = ?", HAVE_WAYBILL).Count(&waybillCount).Error

	err = db.Where("work_state >= ?", TESTING_OK).Where("work_state != ?", PROBLEM_STATE).Count(&testingCount).Error

	return
}

type WorkOrderCount struct {
	OrderCode    string `json:"order_code"`
	Count        int    `json:"count"`
	TestingCount int    `json:"testing_count"` // 已质检合格件数
	WaybillCount int    `json:"waybill_count"` // 已贴运单件数
}

// QueryCountByOrderCode 根据orderCode 分组查询已贴运单的数量和 质检合格以后状态的数量 以及总数量
func (w *WorkOrder) QueryCountByOrderCode(orderCodeList []string) (list map[string]*WorkOrderCount, err error) {
	db := mysql.NewConn().Table(w.TableName()).Select("order_code, count(*) as count," +
		"sum(case when work_state >= 6 and work_state != 15 then 1 else 0 end) as testing_count," +
		"sum(case when work_state = 7 then 1 else 0 end) as waybill_count")

	var result []*WorkOrderCount

	err = db.Where("order_code in (?)", orderCodeList).Group("order_code").Find(&result).Error

	list = make(map[string]*WorkOrderCount)

	for _, count := range result {
		list[count.OrderCode] = count
	}

	return list, nil
}

func (w *WorkOrder) GetListByZc() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName())

	//db = db.Where("spu = ?", "130U006").Where("sku LIKE ?", "%Y99%").Preload("OrderInfo")
	db = db.Where("spu IN (?)", []string{"130U006", "230U006", "130U007", "230U007"}).Preload("OrderInfo")
	db = db.Where("work_state <= ?", OK_PRODUCTION)

	err = db.Find(&list).Error

	return
}

// 列表，含有包装袋和吊牌
func (w *WorkOrder) GetPackagingWithTagList(endTime int64, isMinus bool) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if isMinus {
		// 确保是有设计，并且是库存损耗过的
		minusHasBitSum := SpecialStatePackagingMinus + SpecialStateTagMinus
		db = db.Where("(special_state & ?) = ?", minusHasBitSum, minusHasBitSum)
	} else {
		// 确保是有设计，并且没有生产过
		hasBitSum := SpecialStatePackagingHas + SpecialStatePackagingHasProduced + SpecialStateTagHas + SpecialStateTagHasProduced
		producedBitSum := SpecialStatePackagingHas + SpecialStateTagHas
		db = db.Where("(special_state & ?) = ?", hasBitSum, producedBitSum)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	if endTime > 0 {
		if isMinus {
			db = db.Where("minus_time < ?", endTime)
		} else {
			// todo linxb testing
			db = db.Where("created_at < FROM_UNIXTIME(?)", endTime)
		}
	}

	// 排除暂停的订单
	//db = db.Where("((suspend_state < 1 && resume_time < 1) OR resume_time > 10)")
	// 排除 全部暂停的工单，对应的状态是：已恢复，未暂停，全部暂停
	db = db.Where("resume_time > 10 OR ((suspend_state < 1 && resume_time < 1) OR (suspend_state = ?) )", order.OrderSuspendStateDelivery)

	// 只筛选发货之前的工单
	db = db.Where("work_state > ? AND work_state < ?", NOT_PRINT, HAVE_WAYBILL)

	err = db.Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error

	return
}

// 列表，只有包装袋
func (w *WorkOrder) GetPackagingWithoutTagList(endTime int64, isMinus bool) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if isMinus {
		// 确保是有设计，并且是库存损耗过的
		minusHasBitSum := SpecialStatePackagingMinus + SpecialStateTagMinus
		compareBitSum := SpecialStatePackagingMinus
		db = db.Where("(special_state & ?) = ?", minusHasBitSum, compareBitSum)
	} else {
		// 确保是有设计，并且没有生产过
		hasBitSum := SpecialStatePackagingHas + SpecialStatePackagingHasProduced + SpecialStateTagHas + SpecialStateTagHasProduced
		producedBitSum := SpecialStatePackagingHas
		db = db.Where("(special_state & ?) = ?", hasBitSum, producedBitSum)
	}

	if endTime > 0 {
		if isMinus {
			db = db.Where("minus_time < ?", endTime)
		} else {
			// todo linxb testing
			db = db.Where("UNIX_TIMESTAMP(created_at) < ?", endTime)
		}
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	// 排除暂停的订单
	//db = db.Where("((suspend_state < 1 && resume_time < 1) OR resume_time > 10)")
	// 排除 全部暂停的工单，对应的状态是：已恢复，未暂停，全部暂停
	db = db.Where("resume_time > 10 OR ((suspend_state < 1 && resume_time < 1) OR (suspend_state = ?) )", order.OrderSuspendStateDelivery)

	// 只筛选发货之前的工单
	db = db.Where("work_state > ? AND work_state < ?", NOT_PRINT, HAVE_WAYBILL)

	err = db.Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error

	return
}

// 列表，只有吊牌
func (w *WorkOrder) GetOnlyTagList(endTime int64, isMinus bool) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if isMinus {
		// 确保是有设计，并且是库存损耗过的
		minusHasBitSum := SpecialStatePackagingMinus + SpecialStateTagMinus
		compareBitSum := SpecialStateTagMinus
		db = db.Where("(special_state & ?) = ?", minusHasBitSum, compareBitSum)
	} else {
		// 确保是有设计，并且没有生产过
		hasBitSum := SpecialStatePackagingHas + SpecialStatePackagingHasProduced + SpecialStateTagHas + SpecialStateTagHasProduced
		producedBitSum := SpecialStateTagHas
		db = db.Where("(special_state & ?) = ?", hasBitSum, producedBitSum)
	}

	if endTime > 0 {
		if isMinus {
			db = db.Where("minus_time < ?", endTime)
		} else {
			// todo linxb testing
			db = db.Where("created_at < FROM_UNIXTIME(?)", endTime)
		}
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	// 排除暂停的订单
	//db = db.Where("((suspend_state < 1 && resume_time < 1) OR resume_time > 10)")
	// 排除 全部暂停的工单，对应的状态是：已恢复，未暂停，全部暂停
	db = db.Where("resume_time > 10 OR ((suspend_state < 1 && resume_time < 1) OR (suspend_state = ?) )", order.OrderSuspendStateDelivery)

	// 只筛选发货之前的工单
	db = db.Where("work_state > ? AND work_state < ?", NOT_PRINT, HAVE_WAYBILL)

	err = db.Preload("OrderItem").Preload("OrderItem.TagDesignInfo").
		Preload("OrderItem.TagDesignInfo.HangTagInfo").Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Find(&list).Error

	return
}

type MinusListItem struct {
	ID           uint   `gorm:"PRIMARY_KEY" json:"id"`
	OrderCode    string `json:"order_code"`                     //平台内订单号
	WorkCode     string `json:"work_code"`                      //工单号
	SpecialState uint64 `json:"special_state" gorm:"default:0"` // 特殊状态记录 --目前用来处理包装吊牌的生产，linxb 2023-09-14
	MinusTime    int64  `json:"minus_time"`                     // 损耗的时间
}

// 获取库存损耗列表，minusType=1：包装袋，minusType=2：吊牌
func (w *WorkOrder) GetBrandMinus(pn, ps int, minusType int, timeS, timeE int64, orderType int) (list []*MinusListItem, packagingCount, packagingTagCount, tagCount, total int64, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code like ?", "%"+w.WorkCode+"%")
	}

	if timeS > 0 {
		db = db.Where("minus_time >= ?", timeS)
	}

	if timeE > 0 {
		db = db.Where("minus_time < ?", timeE)
	}

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	switch minusType {
	case 1: // 包装袋（纯包装袋）
		db = db.Where("(special_state & ?) > 0 AND ((special_state & ?) < 1)", SpecialStatePackagingMinus, SpecialStateTagMinus)
		// 包装袋数量
		err = db.Count(&packagingCount).Error
		if err != nil {
			return
		}
		total = packagingCount
	case 2: // 吊牌
		db = db.Where("((special_state & ?) > 0) AND ((special_state & ?) < 1)", SpecialStateTagMinus, SpecialStatePackagingMinus)
		// 吊牌数量
		err = db.Count(&tagCount).Error
		if err != nil {
			return
		}
		total = tagCount
	case 4: // 包装袋任务（包装+吊牌）
		db = db.Where("(special_state & ?) > 0 AND ((special_state & ?) > 0)", SpecialStatePackagingMinus, SpecialStateTagMinus)
		// 包装袋数量
		err = db.Count(&packagingTagCount).Error
		if err != nil {
			return
		}
		total = packagingTagCount
	default:
		// 包装袋数量
		err = db.Where("(special_state & ?) > 0 AND ((special_state & ?) < 1)", SpecialStatePackagingMinus, SpecialStateTagMinus).Count(&packagingCount).Error
		if err != nil {
			return
		}

		// 包装袋吊牌数量
		err = db.Where("(special_state & ?) > 0 AND ((special_state & ?) > 0)", SpecialStatePackagingMinus, SpecialStateTagMinus).Count(&packagingTagCount).Error
		if err != nil {
			return
		}

		// 吊牌数量
		err = db.Where("((special_state & ?) > 0) AND ((special_state & ?) < 1)", SpecialStateTagMinus, SpecialStatePackagingMinus).Count(&tagCount).Error
		if err != nil {
			return
		}

		// 包装袋 或 吊牌
		db = db.Where("((special_state & ?) > 0) OR ((special_state & ?) > 0)", SpecialStatePackagingMinus, SpecialStateTagMinus)
		err = db.Count(&total).Error
		if err != nil {
			return
		}
	}

	// 先根据类型排序（单面排在前面），然后根据 id 进行排序
	if orderType == 1 {
		err = db.Order("minus_time desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	} else {
		err = db.Order("minus_time asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	}

	return
}

// 根据sku 获取未打印工单的数量
func (w *WorkOrder) GetNotPrintCountBySku(location string) (count int, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("sku LIKE ?", "%"+location+"%").Where("work_state = ?", NOT_PRINT).Count(&count).Error
	return
}

// 根据orderCodeList 获取相关未打印的工单
func (w *WorkOrder) GetNotPrintListByOrderCodeList(orderCodeList []string) (list []*WorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("order_code IN (?)", orderCodeList).Where("work_state = ?", NOT_PRINT).Find(&list).Error
	return
}

// 获取 全部暂停的未打印工单
func (w *WorkOrder) GetNotPrintListBySuspend() (list []*WorkOrder, err error) {
	db := mysql.NewConn().Table(w.TableName()).Where("work_state = ?", NOT_PRINT)

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	err = db.Where("suspend_state = 2 AND resume_time < 10").Find(&list).Error
	return
}

// 更新工单内容
func (w *WorkOrder) UpdateByWorkCodes(workCodes []string, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("work_code IN (?)", workCodes)

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).Error
	return
}

// 工单由质检合格转为取消
func (w *WorkOrder) CheckoutToCancel(workCodes, orderCodes []string, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{})

	if len(workCodes) > 0 {
		db = db.Where("work_code IN (?)", workCodes)
	}

	if len(orderCodes) > 0 {
		db = db.Where("order_code IN (?)", orderCodes)
	}

	if len(w.OrderCode) > 0 {
		db = db.Where("order_code = ?", w.OrderCode)
	}

	if len(w.WorkCode) > 0 {
		db = db.Where("work_code = ?", w.WorkCode)
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}

	w.CancelTime = time.Now().Unix()
	err = db.UpdateColumn("special_state", gorm.Expr("special_state | ?", SpecialStateCheckoutToCancel)).Error

	if err != nil {
		return
	}

	if w.DbVersion > 0 {
		w.DbVersion = w.DbVersion + 1
	} else {
		workOrderDbVersion := new(WorkOrderDbVersion)
		err = db.Select("id,created_at,updated_at,work_code,order_code,db_version").Limit(1).Scan(workOrderDbVersion).Error
		if err != nil {
			return
		}
		w.DbVersion = workOrderDbVersion.DbVersion + 1
	}

	err = db.Updates(w).Error

	return
}

type BrandWorkOrder struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	OrderCode   string    `json:"order_code"`   //平台内订单号
	WorkCode    string    `json:"work_code"`    //工单号
	SKU         string    `json:"sku"`          //sku信息，额外的筛选项
	SPU         string    `json:"spu"`          //spu信息，额外的筛选项
	PrintCount  int       `json:"print_count"`  //0 1 2 全部 单面 多面
	ColorType   string    `json:"color_type"`   //颜色类型 0浅色  2深色
	ProductType int       `json:"product_type"` // 商品的类型 目前 0衣服，1长裤  2短裤  2022-11-23 16:52:00 zc  增加短裤 2023年3月26日17:21:03 zc

	OrderItemID   uint   `json:"order_item_id"`   //订单子项id 直接关联
	IsDeliver     bool   `json:"is_deliver"`      //是否打印发货信息
	DesignBit     uint32 `json:"design_bit"`      //生产面的位运算
	PantDesignBit uint32 `json:"pant_design_bit"` // 裤子的生产面的位运算  2022-11-23 16:51:53 zc

	WorkState     int    `json:"work_state"`     //工单状态
	LogisticsType string `json:"logistics_type"` // 物流类型，用于物流揽件区分

	//吊牌和包装需要的信息
	TagDesignID           uint   `json:"tag_design_id" gorm:"default:0"`      // 吊牌设计ID
	TagDesignType         int    `json:"tag_design_type" gorm:"default:1"`    // 打印类型，1：单面，2：双面，默认1单面
	TagProduceState       int    `json:"tag_produce_state" gorm:"default:1"`  // 自定义吊牌的生产情况，1：未生产，2：已生成，默认1未生产
	TagProduceMinus       int    `json:"tag_produce_minus"`                   // 1：自定义吊牌进行了库存损耗，2：库存损耗又进行了生产
	PackDesignID          uint   `json:"pack_design_id" gorm:"default:0"`     // 包装设计ID
	PackDesignType        int    `json:"pack_design_type" gorm:"default:1"`   // 打印类型，1：单面，2：双面，默认1单面
	PackProduceState      int    `json:"pack_produce_state" gorm:"default:1"` // 自定义包装的生产情况，1：未生产，2：已生成，默认1未生产
	PackagingProduceMinus int    `json:"packaging_produce_minus"`             // 1：自定义包装进行了库存损耗，2：库存损耗又进行了生产
	BrandDeliveryCar      string `json:"brand_delivery_car"`                  // 分配的吊牌和包装拣车号
	ProductCName          string `json:"product_c_name"`                      // 商品中文名

}

// 获取 全部暂停的未打印工单
func (w *WorkOrder) GetBrandList() (list []*BrandWorkOrder, err error) {
	err = mysql.NewConn().Table(w.TableName()).Where("tag_design_id > 0 AND pack_design_id > 0").Find(&list).Error
	return
}

// 是否存在损耗（包装、吊牌）
func (w *WorkOrder) HasBrandPorduced() (res bool) {
	res = false
	if ((w.SpecialState & SpecialStateTagHas) > 0) && ((w.SpecialState & SpecialStateTagHasProduced) > 0) {
		// 因为现在包装和吊牌是一起生产的，所以不会存在吊牌或包装单独生产的问题
		res = true
		return
	}
	if ((w.SpecialState & SpecialStatePackagingHas) > 0) && ((w.SpecialState & SpecialStatePackagingHasProduced) > 0) {
		res = true
		return
	}
	return
}

// 是否存在损耗（包装、吊牌）
func (w *WorkOrder) HasMinus() (res bool) {
	res = false
	if ((w.SpecialState & SpecialStateTagMinus) > 0) || ((w.SpecialState & SpecialStatePackagingMinus) > 0) {
		res = true
	}
	return
}

// 是否存在包装损耗
func (w *WorkOrder) HasPackMinus() (res bool) {
	res = false
	if (w.SpecialState & SpecialStatePackagingMinus) > 0 {
		res = true
	}
	return
}

// 是否存在吊牌损耗
func (w *WorkOrder) HasTagMinus() (res bool) {
	res = false
	if (w.SpecialState & SpecialStateTagMinus) > 0 {
		res = true
	}
	return
}

// 批量插入 暂时无用 gorm2新功能，目前相关的事务都是gorm1的
func (w *WorkOrder) BatchInsert(list []*WorkOrder, tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) > 0 {
		db = tx[0]
	}
	err = db.Table(w.TableName()).CreateInBatches(list, len(list)).Error
	return
}

type WorkingWorkOrderCount struct {
	OrderCode string `json:"order_code"` //平台内订单号
	Count     int    `json:"count"`      // 除了【已取消】工单的数量
}

func (w *WorkOrder) GetWorkCodes(orderCodes []string) (list []*WorkingWorkOrderCount, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("order_code IN (?)", orderCodes)

	// 排除【已取消的工单】
	db = db.Where("work_state != ?", CANCEL_STATE)

	err = db.Select("order_code, COUNT(*) as count").Group("order_code").Find(&list).Error

	return
}

func (w *WorkOrder) CountByWorkState(timeS, timeE int64, workState []int) (count int, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(workState) > 0 {
		db = db.Where("work_state in (?)", workState)
	}

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	err = db.Count(&count).Error
	return
}

// 绩效大屏，读取质检合格的数量
func (w *WorkOrder) GetScreenCheckoutCount(targetTime int64) (count int, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_state = ? OR work_state = ?", TESTING_OK, HAVE_WAYBILL)

	db = db.Where("testing_time >= ?", targetTime)

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	err = db.Count(&count).Error

	return
}

// 绩效大屏，读取完成的数量
func (w *WorkOrder) GetScreenDeliveryCount(targetTime int64) (count int, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_state = ?", HAVE_WAYBILL)

	db = db.Where("waybill_time >= ?", targetTime)

	if w.ProduceFactory > 0 {
		db = db.Where("produce_factory = ?", w.ProduceFactory)
	}

	err = db.Count(&count).Error

	return
}

type WorkOrderBase struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	OrderCode string    `json:"order_code"` //平台内订单号
	WorkCode  string    `json:"work_code"`  //工单号
}

func (w *WorkOrder) GetIdsByOrderCode() (list []*WorkOrderBase, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_code = ?", w.WorkCode)

	err = db.Find(&list).Error
	return
}

func (w *WorkOrder) PutOneWorkState(id int64, updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkOrder{}).Where("id = ?", id)

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Updates(updateMap).Error

	return
}

func (w *WorkOrder) ClearBrandDeliveryCar(codes []string) error {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("work_code IN (?)", codes)

	updateMap := make(map[string]interface{})

	if w.DbVersion > 0 {
		updateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		updateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	updateMap["brand_delivery_car"] = ""

	err := db.Updates(updateMap).Error
	return err
}

func (w *WorkOrder) UpdateMap(workCodes []string, stateMap map[string]interface{}, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(w.TableName())

	db = db.Where("work_code IN (?)", workCodes)

	if w.DbVersion > 0 {
		stateMap["db_version"] = w.DbVersion + 1
	} else {
		//db = db.Update("db_version", gorm.Expr("db_version + ?", 1))
		stateMap["db_version"] = gorm.Expr("db_version + ?", 1)
	}

	err = db.Updates(stateMap).Error

	return
}

type WorkSkuItems struct {
	Location string `json:"location"`
	Count    int    `json:"count"`
}

// 根据 locationList 查询未打印工单的数据
func (w *WorkOrder) GetNotPrintListByLocationList(locationList []string) (resp map[string]int, err error) {

	list := make([]*WorkSkuItems, 0)

	db := mysql.NewConn().Table(w.TableName())

	// 查询 work_state = 1 的未打印工单
	db = db.Where("work_state = ?", 1)

	// 使用 LIKE 结合 Location 查询
	if len(locationList) > 0 {
		likeConditions := make([]string, 0, len(locationList))
		for _, location := range locationList {
			likeConditions = append(likeConditions, fmt.Sprintf("sku LIKE '%%%s'", location))
		}
		db = db.Where(strings.Join(likeConditions, " OR "))
	}

	// 查询并按 Location 分组统计数量
	err = db.Select("SUBSTRING(sku, -6) as location, COUNT(*) as count").
		Group("location").
		Find(&list).Error

	// 将结果转换为 map
	resp = make(map[string]int)
	for _, wo := range list {
		resp[wo.Location] = wo.Count
	}

	return
}

// UpdateWorkByOrderCode
func (w *WorkOrder) UpdateWorkByOrderCode(orderCode []string, factory_id int, stateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(w.TableName())

	db = db.Where("order_code in (?)", orderCode).Where("produce_factory = ?", 0)

	err = db.Updates(stateMap).Error

	return
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (w *WorkOrder) QueryToGenerateDtg() (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// dtg_product_id 为 0
	db = db.Where("dtg_product_id = ?", 0)

	// 只筛选dtg版本管理之后的工单
	db = db.Where("created_at >= ?", time.Date(2024, 7, 1, 7, 12, 43, 0, time.Now().Location())) // 2024-07-01 07:12:43

	// 未缓存的工单
	db = db.Where("generated_arxp_file = ?", 1)

	err = db.Limit(10).Find(&list).Error

	return
}

const (
	BrandTypePack = 1
	BrandTypeTag  = 2
)

type BrandSales struct {
	Total             int    `json:"total"`                                // 总销售额
	TagDesignID       uint   `json:"tag_design_id" gorm:"default:0"`       // 吊牌设计ID
	TagDesignVersion  int    `json:"tag_design_version" gorm:"default:0"`  // 吊牌设计版本，默认0
	PackDesignID      uint   `json:"pack_design_id" gorm:"default:0"`      // 包装设计ID
	PackDesignVersion int    `json:"pack_design_version" gorm:"default:0"` // 包装设计版本，默认0
	ProduceFactory    int    `json:"produce_factory" gorm:"default:0"`     // 所属工厂
	SPU               string `json:"spu"`                                  //spu信息
}

// brandTyp：1 包装，2 吊牌
func (w *WorkOrder) GetBrandSales(timeS, timeE time.Time, brandType int, designIdMapVersions map[int64][]int) (list []*BrandSales, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 最近15天
	if timeS.Unix() > 0 {
		db = db.Where("created_at >= ?", timeS)
	}
	if timeE.Unix() > 0 {
		db = db.Where("created_at <= ?", timeE)
	}

	// 去掉已取消的工单
	db = db.Where("work_state != ?", CANCEL_STATE)

	db = db.Select("count(*) as total, tag_design_id, tag_design_version, pack_design_id, pack_design_version, produce_factory, spu")

	switch brandType {
	case BrandTypePack:
		sqlStr := ""
		for k, v := range designIdMapVersions {
			versions, _ := json.Marshal(v)
			versionsStr := string(versions)
			if len(versionsStr) < 3 {
				log.Info("designIdMapVersions 长度小于3： ", versionsStr)
				continue
			}
			if len(sqlStr) < 1 {
				sqlStr = fmt.Sprintf("(pack_design_id = %d AND pack_design_version IN (%s))", k, versionsStr[1:len(versionsStr)-1])
				continue
			}
			sqlStr = fmt.Sprintf("%s OR (pack_design_id = %d AND pack_design_version IN (%s))", sqlStr, k, versionsStr[1:len(versionsStr)-1])
		}
		db = db.Where(sqlStr)
		err = db.Group("pack_design_id, pack_design_version, produce_factory, spu").Find(&list).Error

	case BrandTypeTag:
		sqlStr := ""
		for k, v := range designIdMapVersions {
			versions, _ := json.Marshal(v)
			versionsStr := string(versions)
			if len(versionsStr) < 3 {
				log.Info("designIdMapVersions 长度小于3： ", versionsStr)
				continue
			}
			if len(sqlStr) < 1 {
				sqlStr = fmt.Sprintf("(tag_design_id = %d AND tag_design_version IN (%s))", k, versionsStr[1:len(versionsStr)-1])
				continue
			}
			sqlStr = fmt.Sprintf("%s OR (tag_design_id = %d AND tag_design_version IN (%s))", sqlStr, k, versionsStr[1:len(versionsStr)-1])
		}
		db = db.Where(sqlStr)
		err = db.Group("tag_design_id, tag_design_version, produce_factory").Find(&list).Error
	}

	return
}

type TendencyReq struct {
	TemplateId string `form:"template_id" json:"template_id"` // 模板ID
	Type       int    `form:"type" json:"type"`               // 类型，包装或者吊牌
	Version    int    `form:"version" json:"version"`         // 版本
	FactoryId  int    `form:"factory_id" json:"factory_id"`   // 工厂ID
}

// brandTyp：1 包装，2 吊牌
func (w *WorkOrder) GetSalesCount(timeS, timeE time.Time, req *TendencyReq) (count, countBig, countSmall int, err error) {
	templateId := int64(0)
	if len(req.TemplateId) > 1 {
		templateId, err = strconv.ParseInt(req.TemplateId[1:], 10, 64)
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(errors.New("template_id 不合法"), define.PARAMS_INVALID, define.CodeMsgName, "The template_id is invalid")
			return
		}
	}

	db := mysql.NewConn().Table(w.TableName())

	// 时间段
	if timeS.Unix() > 0 {
		db = db.Where("created_at >= ?", timeS)
	}
	if timeE.Unix() > 0 {
		db = db.Where("created_at <= ?", timeE)
	}

	// 工厂筛选
	db = db.Where("produce_factory = ?", req.FactoryId)

	// 去掉已取消的工单
	db = db.Where("work_state != ?", CANCEL_STATE)

	switch req.Type {
	case BrandTypePack:
		db = db.Where("pack_design_id = ? AND pack_design_version = ?", templateId, req.Version)
		err = db.Where("spu LIKE '130%' OR spu LIKE '140%' OR spu LIKE '170%' OR spu LIKE '230%' OR spu LIKE '240%' OR spu LIKE '270%' ").Count(&countBig).Error
		if err != nil {
			log.Error(err)
			return
		}
	case BrandTypeTag:
		db = db.Where("tag_design_id = ? AND tag_design_version = ?", templateId, req.Version)
	}
	err = db.Count(&count).Error
	if err != nil {
		log.Error(err)
		return
	}
	countSmall = count - countBig

	return
}

// 判断是否为专属拣车
func (w *WorkOrder) IsPickingCar() (result bool) {
	result = w.SpecialState&SpecialStatePickingCar > 0
	return
}

// 根据创建时间段筛选工单
func (w *WorkOrder) GetWorkOrdersByCreateTime(minId, maxId uint, timeS, timeE time.Time) (list []*WorkOrder, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 去掉已取消的工单
	//db = db.Where("work_state != ?", CANCEL_STATE)

	if timeS.Unix() > 0 {
		db = db.Where("created_at >= ?", timeS)
	}

	if timeE.Unix() > 0 {
		db = db.Where("created_at <= ?", timeE)
	}

	if minId > 0 {
		db = db.Where("id > ?", minId)
	}

	if maxId > 0 {
		db = db.Where("id < ?", maxId)
	}

	// 只筛选有包装或吊牌的
	db = db.Where("tag_design_id > 0 OR pack_design_id > 0")

	// 袋牌生产计划，只筛选包装吊牌版本管理上线之前的数据
	//db = db.Where("pack_design_version < 1 AND tag_design_version < 1")

	err = db.Preload("OrderItem", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_pod_sku = ? AND is_ignore = ?", true, false)
	}).
		Preload("OrderItem.SkuInfo").
		Preload("OrderItem.SkuInfo.ProductSize").
		Preload("OrderItem.SkuInfo.ProductInfo").
		Preload("OrderItem.TagDesignInfo").Preload("OrderItem.TagDesignInfo.HangTagInfo").
		Preload("OrderItem.PackDesignInfo").Preload("OrderItem.PackDesignInfo.PackagingInfo").
		Preload("OrderInfo").Order("id asc").Find(&list).Error

	return
}

type WorkOrderTagStatics struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	OrderCode   string    `json:"order_code"`                     //平台内订单号
	WorkCode    string    `json:"work_code"`                      //工单号
	TagDesignID uint      `json:"tag_design_id" gorm:"default:0"` // 吊牌设计ID
}

func (w *WorkOrder) QueryByCreateTime(timeS, timeE int64) (list []*WorkOrderTagStatics, err error) {

	db := mysql.NewConn().Table(w.TableName())
	if timeS > 0 {
		db = db.Where("created_at >= ?", time.Unix(timeS, 0))
	}
	if timeE > 0 {
		db = db.Where("created_at <= ?", time.Unix(timeE, 0))
	}
	// 去掉已取消的工单
	db = db.Where("work_state != ?", CANCEL_STATE)
	err = db.Find(&list).Error
	return
}
