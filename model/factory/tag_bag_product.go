package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/global"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*TagBagProduct)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*TagBagProduct)(nil))
}

const (
	TagBagProductTypePack = 1 // 包装袋
	TagBagProductTypeTag  = 2 // 吊牌
)

type TagBagProduct struct {
	ID                uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`                                                                                                                                 // 更新时间，！！！创建索引
	UserId            int64     `json:"user_id"`                                                                                                                                    // 所属用户ID
	CnName            string    `json:"cn_name"`                                                                                                                                    // 中文名称
	BlankId           int64     `json:"blank_id"`                                                                                                                                   // 款式id
	Type              int       `json:"type"`                                                                                                                                       // 参考 const TagBagProductType*                                                                                                                                       // 类型，参考 Const TagBagProductType
	TemplateId        string    `json:"template_id"`                                                                                                                                // 模板ID
	Versions          int       `json:"versions" gorm:"default:0"`                                                                                                                  // 版本号  1 2 3 4累加，取自来源模板设计的版本号
	DesignInfo        string    `json:"design_info"  gorm:"type:BLOB;"`                                                                                                             // 具体的生产信息
	ModelImg          string    `json:"model_img"   gorm:"type:BLOB;"`                                                                                                              // 模特展示图信息
	LastUsedTime      int64     `json:"last_used_time" gorm:"default:0"`                                                                                                            // 最近使用时间
	UsedCount         int       `json:"used_count" gorm:"default:0"`                                                                                                                // 使用次数
	Inventory         int       `json:"inventory"  gorm:"type: INT GENERATED ALWAYS AS (inventory_one_big + inventory_one_small + inventory_two_big + inventory_two_small) STORED"` // 专属拣车的库存数量
	InventoryOne      int       `json:"inventory_one" gorm:"type: INT GENERATED ALWAYS AS (inventory_one_big + inventory_one_small) STORED"`                                        // 工厂1的库存
	InventoryOneBig   int       `json:"inventory_one_big"gorm:"default:0"`                                                                                                          // 工厂1【包装|吊牌】的库存，如果是包装袋的话，就是大包装袋
	InventoryOneSmall int       `json:"inventory_one_small" gorm:"default:0"`                                                                                                       // 工厂1【包装】的库存，如果是包装袋的话，就是小包装袋
	InventoryTwo      int       `json:"inventory_two" gorm:"type: INT GENERATED ALWAYS AS (inventory_two_big + inventory_two_small) STORED"`                                        // 工厂2的库存
	InventoryTwoBig   int       `json:"inventory_two_big"  gorm:"default:0"`                                                                                                        // 工厂2【包装|吊牌】的库存，如果是包装袋的话，就是大包装袋
	InventoryTwoSmall int       `json:"inventory_two_small" gorm:"default:0"`                                                                                                       // 工厂2【包装】的库存，如果是包装袋的话，就是小包装袋
	TotalSales        int       `json:"total_sales"  gorm:"default:0"`                                                                                                              // 总销售数量，15天
	One               int       `json:"one"  gorm:"default:0"`
	Two               int       `json:"two"  gorm:"default:0"`
	Three             int       `json:"three"  gorm:"default:0"`
	Four              int       `json:"four"  gorm:"default:0"`
	Five              int       `json:"five"  gorm:"default:0"`
	Six               int       `json:"six"  gorm:"default:0"`
	Seven             int       `json:"seven"  gorm:"default:0"`
	Eight             int       `json:"eight"  gorm:"default:0"`
	Nine              int       `json:"nine"  gorm:"default:0"`
	Ten               int       `json:"ten"  gorm:"default:0"`
	Eleven            int       `json:"eleven"  gorm:"default:0"`
	Twelve            int       `json:"twelve"  gorm:"default:0"`
	Thirteen          int       `json:"thirteen"  gorm:"default:0"`
	Fourteen          int       `json:"fourteen"  gorm:"default:0"`
	Fifteen           int       `json:"fifteen"  gorm:"default:0"`
	Sixteen           int       `json:"sixteen"  gorm:"default:0"`
	Seventeen         int       `json:"seventeen"  gorm:"default:0"`

	RiskState        int    `json:"risk_state"`         // 0 1 正常 2 有侵权
	RiskReason       string `json:"risk_reason"`        // 侵权描述
	DetectedTime     int64  `json:"detected_time"`      // 检测时间
	IsManualOverride int    `json:"is_manual_override"` // 是否人工解除侵权  0否 1是
	AdminName        string `json:"admin_name"`         // 操作人
}

const LoopDays = 17

func (t *TagBagProduct) TableName() string {
	return "tag_bag_product"
}

// 新增
func (t *TagBagProduct) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(t.TableName()).Omit("inventory", "inventory_one", "inventory_two").Create(t).Error
}

func (t *TagBagProduct) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(t.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*TagBagProduct, 0)

	err := db.Find(&list).Error

	return list, err
}

func (t *TagBagProduct) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&TagBagProduct{}).Where("id IN (?)", ids).Delete(t).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (t *TagBagProduct) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(t.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", t.ID).Updates(t).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", t.ID).Update("updated_at", t.UpdatedAt).Error

	return
}

func (t *TagBagProduct) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(TagBagProduct)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (t *TagBagProduct) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(t.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 生成工单时候的刷新销量数据
func (t *TagBagProduct) FlushOneDaySales(curTime int64, count int, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 先上锁
	db = db.Model(&TagBagProduct{}) //.Set("gorm:query_option", "FOR UPDATE")
	db = db.Where("id = ?", t.ID)
	err = db.First(t).Error
	if err != nil {
		log.Error(err)
		return
	}

	// 映射到对应的字段
	day := global.SecondsToDay(curTime, LoopDays) + 1
	updateMap := make(map[string]interface{})
	updateMap[numberMapEn[day]] = gorm.Expr(numberMapEn[day]+" + ?", count)
	updateMap["total_sales"] = gorm.Expr("total_sales + ?", count)

	err = db.Updates(updateMap).Error

	return
}

// 根据创建时间获取数量
func (t *TagBagProduct) Count(createdTime time.Time) (count int, err error) {

	db := mysql.NewConn().Table(t.TableName())

	db = db.Where("created_at < ?", createdTime)

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	return
}

// 每天0点定时更新15天的销量数据
func (t *TagBagProduct) FlushTotal(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 先上锁
	db = db.Model(&TagBagProduct{}) //.Set("gorm:query_option", "FOR UPDATE")
	//err = db.First(t).Error
	//if err != nil {
	//	log.Error(err)
	//	return
	//}

	tNow := time.Now()
	tNow = time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), tNow.Minute(), tNow.Second(), tNow.Nanosecond(), time.UTC)
	startTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day()-15, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, time.UTC)
	db = db.Where("updated_at >= ?", startTime).Where("updated_at < ?", endTime)

	daySub := global.SecondsToDay(tNow.Unix()+2*24*60*60, LoopDays) + 1
	dayClear := global.SecondsToDay(tNow.Unix()+24*60*60, LoopDays) + 1

	updateMap := make(map[string]interface{})
	updateMap[numberMapEn[dayClear]] = 0
	updateMap["total_sales"] = gorm.Expr("total_sales - " + numberMapEn[daySub])

	err = db.Updates(updateMap).Error

	return
}

const (
	RankAll     = 1 // 全部包装吊牌的排行，额外添加条件 【15 天用量 ≥ 50】或【库存＞0】
	RankOneUser = 2 // 一个客户的所有包装吊牌设计排行，不需要额外的筛选条件
)

const (
	SalesDesc     = 0 // 销量降序
	SalesAsc      = 1 // 销量升序
	InventoryDesc = 2 // 库存升序
	InventoryAsc  = 3 // 库存降序
)

func (t *TagBagProduct) QueryList(pn, ps int, rankType, orderType uint) (list []*TagBagProduct, count int, err error) {

	db := mysql.NewConn().Table(t.TableName())

	// 筛选：包装，吊牌
	if t.Type > 0 {
		db = db.Where("type = ?", t.Type)
	}

	// 筛选：用户id
	if t.UserId > 0 {
		db = db.Where("user_id = ?", t.UserId)
	}

	// 筛选：模板id
	if len(t.TemplateId) > 0 {
		db = db.Where("template_id = ?", t.TemplateId)
	}

	// 筛选：销量最小值
	if t.TotalSales > 0 && rankType != RankAll {
		db = db.Where("total_sales >= ?", t.TotalSales)
	}

	// 额外的条件，在全部包装吊牌排行时需要添加的 固定条件
	switch rankType {
	case RankAll:
		db = db.Where("total_sales >= ? OR inventory > 0", t.TotalSales)
	case RankOneUser:
	default:
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	// 排序
	switch orderType {
	case SalesDesc:
		db = db.Order("total_sales desc")
	case SalesAsc:
		db = db.Order("total_sales asc")
	case InventoryDesc:
		db = db.Order("inventory desc")
	case InventoryAsc:
		db = db.Order("inventory asc")
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

var numberMapEn = map[int]string{
	1:  "one",
	2:  "two",
	3:  "three",
	4:  "four",
	5:  "five",
	6:  "six",
	7:  "seven",
	8:  "eight",
	9:  "nine",
	10: "ten",
	11: "eleven",
	12: "twelve",
	13: "thirteen",
	14: "fourteen",
	15: "fifteen",
	16: "sixteen",
	17: "seventeen",
}

const (
	InventoryAdd   = 1 << 0 // 增加库存
	InventorySub   = 1 << 1 // 减少库存
	SaleTotalAdd   = 1 << 2 // 增加15天销量
	SaleTotalSub   = 1 << 3 // 减少15天销量
	InventoryAbs   = 1 << 4 // 库存绝对值
	SalesAddOneDay = 1 << 5 // 对应天数的增加
	SalesSubOneDay = 1 << 6 // 对应天数的增加
)

// 更新库存，count：0:库存更新数值，1:销售额更新的数值。 count 数据：{【index=0】：库存的更新值；【index=1】：销售额（单日、总销售额）的更新值；【index=2】：小包装袋（库存）的更新值}
func (t *TagBagProduct) UpdateInventory(updateType, factoryId int, count []int, ids []int64, tx ...*gorm.DB) (err error) {

	createAt := t.CreatedAt

	if updateType < 1 {
		err = errors.New(fmt.Sprintf("Inventory update type error, type: %d", updateType))
		log.Error(err)
		return
	}

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 先上锁
	db = db.Model(&TagBagProduct{}).Set("gorm:query_option", "FOR UPDATE")
	// 根据 id 更新
	//db = db.Where("id = ?", t.ID)

	if len(t.TemplateId) > 0 && t.Versions > 0 {
		db = db.Where("template_id = ?", t.TemplateId).Where("versions = ?", t.Versions)
	} else if t.ID > 0 {
		db = db.Where("id = ?", t.ID)
	} else if len(ids) > 0 {
		db = db.Where("id IN (?)", ids)
	} else {
		return
	}

	err = db.First(t).Error
	if err != nil {
		log.Error(err)
		return
	}

	updateMap := make(map[string]interface{})

	// 更新专属客户库存
	if updateType&InventoryAdd > 0 {
		if factoryId == 1 {
			updateMap["inventory_one_big"] = gorm.Expr("inventory_one_big + ?", count[0])
			updateMap["inventory_one_small"] = gorm.Expr("inventory_one_small + ?", count[1])
		} else if factoryId == 2 {
			updateMap["inventory_two_big"] = gorm.Expr("inventory_two_big + ?", count[0])
			updateMap["inventory_two_small"] = gorm.Expr("inventory_two_small + ?", count[1])
		}
	} else if updateType&InventorySub > 0 {
		if factoryId == 1 {
			updateMap["inventory_one_big"] = gorm.Expr("inventory_one_big - ?", count[0])
			updateMap["inventory_one_small"] = gorm.Expr("inventory_one_small - ?", count[1])
		} else if factoryId == 2 {
			updateMap["inventory_two_big"] = gorm.Expr("inventory_two_big - ?", count[0])
			updateMap["inventory_two_small"] = gorm.Expr("inventory_two_small - ?", count[1])
		}
	} else if updateType&InventoryAbs > 0 {
		if factoryId == 1 {
			if count[0] >= 0 {
				updateMap["inventory_one_big"] = count[0]
			}
			if count[1] >= 0 {
				updateMap["inventory_one_small"] = count[1]
			}
		} else if factoryId == 2 {
			if count[0] >= 0 {
				updateMap["inventory_two_big"] = count[0]
			}
			if count[1] >= 0 {
				updateMap["inventory_two_small"] = count[1]
			}
		}
	}

	// 更新对应天数的库存
	tNow := time.Now()
	tNow = time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), tNow.Minute(), tNow.Second(), tNow.Nanosecond(), time.UTC)

	dayNum := global.SecondsToDay(tNow.Unix(), LoopDays) + 1
	if createAt.Unix() > 0 {
		createAt = time.Date(createAt.Year(), createAt.Month(), createAt.Day(), createAt.Hour(), createAt.Minute(), createAt.Second(), createAt.Nanosecond(), time.UTC)
		dayNum = global.SecondsToDay(createAt.Unix(), LoopDays) + 1
	}
	if updateType&SalesAddOneDay > 0 {
		updateMap[numberMapEn[dayNum]] = gorm.Expr(numberMapEn[dayNum]+" + ?", count[2])
	} else if updateType&SalesSubOneDay > 0 {
		updateMap[numberMapEn[dayNum]] = gorm.Expr(numberMapEn[dayNum]+" - ?", count[2])
	}

	// 更新15天销量
	if updateType&SaleTotalAdd > 0 {
		updateMap["total_sales"] = gorm.Expr("total_sales + ?", count[2])
	} else if updateType&SaleTotalSub > 0 {
		updateMap["total_sales"] = gorm.Expr("total_sales - ?", count[2])
	}

	err = db.Updates(updateMap).Error

	return
}

// 绑定和绑定专属拣车之前，清一下对应工厂的库存
func (t *TagBagProduct) ClearInventory(factoryId int, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	// 先上锁
	db = db.Model(&TagBagProduct{})
	// 根据 id 更新
	db = db.Where("user_id = ?", t.UserId)

	err = db.First(t).Error
	if err != nil {
		log.Error(err)
		return
	}

	updateMap := make(map[string]interface{})

	switch factoryId {
	case 1:
		updateMap["inventory_one_big"] = 0
		updateMap["inventory_one_small"] = 0
	case 2:
		updateMap["inventory_two_big"] = 0
		updateMap["inventory_two_small"] = 0
	}

	err = db.Updates(updateMap).Error

	return
}

// 查找
func (t *TagBagProduct) Query() (err error) {

	db := mysql.NewConn().Table(t.TableName())

	// 模板id
	db = db.Where("template_id = ?", t.TemplateId)

	// 版本
	db = db.Where("versions = ?", t.Versions)

	err = db.First(t).Error

	return
}

// 查找
func (t *TagBagProduct) QueryByCondition(conditions []map[string]interface{}) (list []*TagBagProduct, err error) {

	db := mysql.NewConn().Table(t.TableName())

	sqlTotal := ""
	for _, condition := range conditions {
		if len(condition) < 1 {
			continue
		}

		sqlOne := ""
		for key, value := range condition {
			if len(sqlOne) < 1 {
				switch v := value.(type) {
				case string:
					sqlOne = fmt.Sprintf("%s = '%s'", key, v) // 字符串需要包裹在单引号中
				default:
					sqlOne = fmt.Sprintf("%s = %v", key, v) // 其他类型不需要特殊处理
				}
				continue
			}

			switch v := value.(type) {
			case string:
				sqlOne += fmt.Sprintf(" AND %s = '%s'", key, v) // 字符串需要包裹在单引号中
			default:
				sqlOne += fmt.Sprintf(" AND %s = %v", key, v) // 其他类型不需要特殊处理
			}
		}

		if len(sqlTotal) < 1 {
			sqlTotal = "(" + sqlOne + ")"
			continue
		}
		sqlTotal += fmt.Sprintf(" OR (%s)", sqlOne)
	}

	log.Debug("sqlTotal: ", sqlTotal)

	if len(sqlTotal) > 0 {
		db = db.Where(sqlTotal)
	}

	err = db.Find(&list).Error

	return
}

func FormatPackTemplateId(id int64) (templateId string) {
	templateId = fmt.Sprintf("B%0d", id)
	return
}

func FormatTagTemplateId(id int64) (tagId string) {
	tagId = fmt.Sprintf("D%0d", id)
	return
}

// 根据 map 更新
func (t *TagBagProduct) UpdateByMap(maps map[string]interface{}) error {
	db := mysql.NewConn().Model(&TagBagProduct{})
	if t.TemplateId != "" {
		db = db.Where("template_id = ?", t.TemplateId)
	}
	if t.Versions != 0 {
		db = db.Where("versions = ?", t.Versions)
	}
	if t.RiskState != 0 {
		db = db.Where("risk_state = ?", t.RiskState)
	}

	err := db.Updates(maps).Error
	return err
}

func (t *TagBagProduct) QueryByTemplateIdsAndVersions(templateIds []string, versions []int) (list []TagBagProduct, err error) {
	db := mysql.NewConn().Table(t.TableName())
	err = db.Where("template_id in (?) AND versions in (?)", templateIds, versions).
		Find(&list).Error
	return
}
