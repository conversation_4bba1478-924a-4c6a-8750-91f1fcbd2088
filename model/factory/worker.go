package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Worker)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Worker)(nil))
}

const (
	WorkerLevelSuper = 100 // 厂长，目前只有厂长可以扫库存损耗二维码
)

type Worker struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Name        string    `json:"name"`                                // 名字
	PhoneNumber string    `json:"phone_number" gorm:"unique;not null"` // 手机号
	WorkTypeId  int       `json:"work_type_id"`                        // 工种
	Password    string    `json:"password"       gorm:"not null"`      // 密码
	FactoryId   int       `json:"factory_id" gorm:"default:0"`         // 生产工厂id，目前直接使用【主键id】
	Permission  uint64    `json:"permission"`                          // 当前工人权限
}

type WorkerListResp struct {
	*Worker
	FactoryName string `json:"factory_name"` // 对应个工厂名称
}

type WorkerInfoResp struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Name        string    `json:"name"`                                // 名字
	PhoneNumber string    `json:"phone_number" gorm:"unique;not null"` // 手机号
	WorkTypeId  int       `json:"work_type_id"`                        // 工种
	Permission  uint64    `json:"permission"`                          // 当前工人等级
}

func (w *Worker) TableName() string {
	return "worker"
}

// 新增
func (w *Worker) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

// 查找
func (w *Worker) Query() (err error) {
	db := mysql.NewConn().Table(w.TableName())

	if w.ID < 1 && len(w.PhoneNumber) < 1 {
		return nil
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}
	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number = ?", w.PhoneNumber)
	}
	if len(w.Name) > 0 {
		db = db.Where("name = ?", w.Name)
	}

	err = db.First(w).Error

	return
}

// 查找
func (w *Worker) QueryByName() (err error) {
	db := mysql.NewConn().Table(w.TableName())

	if w.Name != "" {
		db = db.Where("name = ?", w.Name)
	}

	err = db.First(w).Error

	return
}

// 更新
func (w *Worker) Update(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&Worker{}).Where("id = ?", w.ID).Updates(w).Error
}

// 查询
func (w *Worker) GetList(pn, ps int) (list []*Worker, count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number like ?", "%"+w.PhoneNumber+"%")
	}
	if len(w.Name) > 0 {
		db = db.Where("name like ?", "%"+w.Name+"%")
	}
	if w.WorkTypeId > 0 {
		db = db.Where("work_type_id = ?", w.WorkTypeId)
	}

	if w.FactoryId > 0 {
		db = db.Where("factory_id = ?", w.FactoryId)
	}

	// 不展示厂长的账号
	db = db.Where("permission < 200")

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id asc"). /*.Offset((pn - 1) * ps).Limit(ps)*/ Find(&list).Error
	return
}

// 查询，不分页
func (w *Worker) GetListSimple() (list []*Worker, count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number like ?", "%"+w.PhoneNumber+"%")
	}
	if len(w.Name) > 0 {
		db = db.Where("name like ?", "%"+w.Name+"%")
	}
	if w.WorkTypeId > 0 {
		db = db.Where("work_type_id = ?", w.WorkTypeId)
	}

	if w.FactoryId > 0 {
		db = db.Where("factory_id = ?", w.FactoryId)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id asc").Find(&list).Error
	return
}

// 根据电话号码获取列表
func (w *Worker) GetListWithCondition(phoneNumbers []string) (list []*Worker, err error) {
	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("phone_number IN (?)", phoneNumbers)

	err = db.Order("id asc").Find(&list).Error
	return
}

func (c *Worker) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Worker, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *Worker) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Worker{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

func (c *Worker) GetListByIds(ids []uint) ([]*Worker, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Worker, 0)

	err := db.Find(&list).Error

	return list, err
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Worker) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Worker) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Worker)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Worker) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
