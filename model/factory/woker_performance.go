package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*WorkerPerformance)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*WorkerPerformance)(nil))
}

type WorkerPerformance struct {
	ID            uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	WorkerID      int64     `json:"worker_id"`       // 工人 id
	PhoneNumber   string    `json:"phone_number"`    // 手机号
	WorkerName    string    `json:"worker_name"`     // 工人名字
	FaceCount     int64     `json:"face_count"`      // 扫码的面
	CheckOutCount int64     `json:"check_out_count"` // 质检数量
	DeliveryCount int64     `json:"delivery_count"`  // 发货数量
	MinusCount    int64     `json:"minus_count"`     // 库存损耗数量
}

func (w *WorkerPerformance) TableName() string {
	return "worker_performance"
}

// 新增
func (w *WorkerPerformance) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

// 查找
func (w *WorkerPerformance) Query() (err error) {
	db := mysql.NewConn().Table(w.TableName())

	if w.ID < 1 && len(w.PhoneNumber) < 1 {
		return nil
	}

	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
	}
	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number = ?", w.PhoneNumber)
	}

	err = db.First(w).Error

	return
}

// 查找列表
func (w *WorkerPerformance) QueryAll(pn, ps int, timeS, timeE int64) (list []*WorkerPerformance, count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	db = db.Order("id desc")
	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (c *WorkerPerformance) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*WorkerPerformance, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *WorkerPerformance) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&WorkerPerformance{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *WorkerPerformance) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *WorkerPerformance) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(WorkerPerformance)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *WorkerPerformance) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
