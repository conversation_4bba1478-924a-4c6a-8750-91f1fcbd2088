package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*HangTagProduceV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// 常量定义
const (
	ProduceStateNotV2TagP    = 1  // 未生产
	ProduceStateYesV2TagP    = 2  // 已生产
	ProduceStateCancelV2TagP = 10 // 取消生产
)

const (
	ProduceTypeSingleV2TagP = 1 // 单面
	ProduceTypeDoubleV2TagP = 2 // 双面
)

const (
	ProducingStateProducingV2TagP = 1 // 正在生产中
	ProducingStateCompleteV2TagP  = 2 // 生产完成
)

// HangTagProduceV2 吊牌生产表 - GORM v2版本
type HangTagProduceV2 struct {
	ID                 uint      `gorm:"primaryKey" json:"ID"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Type               int       `json:"type" gorm:"default:1"`                 // 打印类型，1：单面，2：双面
	ProduceState       int       `json:"produce_state" gorm:"default:1"`        // 状态，1：未完成，2：已完成，10：已取消
	CompleteTime       int64     `json:"complete_time"`                         // 完成时间
	WorkCodes          string    `json:"work_codes" gorm:"type:BLOB"`           // 所有工单的工单id
	HangTagItemsJson   string    `json:"hang_tag_item" gorm:"type:BLOB"`        // 对应每个工单的详细信息
	ProducingState     int       `json:"producing_state" gorm:"default:0"`      // 生产状态，1：正在生产中
	JumpTime           int64     `json:"jump_time" gorm:"default:0"`            // 插队时间，时间戳
	LatestProduceState int64     `json:"latest_produce_state" gorm:"default:0"` // 最近一次生产的时间
	ProduceBit         uint64    `json:"produce_bit" gorm:"default:0"`          // 生产面
	PriorityB          uint64    `json:"priority_b" gorm:"default:0"`           // 优先分配到B区
	FactoryId          int       `json:"factory_id" gorm:"default:1"`           // 工厂id
}

// HangTagProduceItemV2 吊牌生产项目详情
type HangTagProduceItemV2 struct {
	Version              int    `json:"version" gorm:"default:0"` // 版本号
	DeliveryCar          string `json:"delivery_car"`             // 拣车号
	InventoryStorageName string `json:"inventory_storage_name"`   // 库存储位名称
	DesignInfo           string `json:"design_info"`              // 设计信息
	OrderCode            string `json:"order_code"`               // 订单号
	WorkCode             string `json:"work_code"`                // 工单号
	CName                string `json:"c_name"`                   // 中文名称
	HangTagInfoId        int64  `json:"hang_tag_info_id"`         // 吊牌信息ID
	TagModel             string `json:"tag_model"`                // 吊牌的模特图
	State                int    `json:"state"`                    // 0：正常状态，10：已取消
	SKU                  string `json:"sku"`                      // sku信息
	OrderItemId          uint   `json:"order_item_id"`            // 订单子项id
}

func (h *HangTagProduceV2) TableName() string {
	return "hang_tag_produce"
}

// 统一数据库接口方法
func (h *HangTagProduceV2) Create(db mysql.DBInterface) error {
	return db.Create(h).Error()
}

func (h *HangTagProduceV2) Query(db mysql.DBInterface) error {
	return db.Where(h).First(h).Error()
}

func (h *HangTagProduceV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*HangTagProduceV2, int64, error) {
	var list []*HangTagProduceV2
	var count int64

	query := db.Model(&HangTagProduceV2{})
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	// 排除已取消的任务
	query = query.Where("produce_state != ?", 10)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Order("type asc, produce_state asc, id desc").Offset(offset).Limit(ps).Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (h *HangTagProduceV2) Update(db mysql.DBInterface) error {
	return db.Save(h).Error()
}

func (h *HangTagProduceV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(h).Error()
}

func (h *HangTagProduceV2) Delete(db mysql.DBInterface) error {
	return db.Delete(h).Error()
}

// 根据ID查询详情
func (h *HangTagProduceV2) GetByID(db mysql.DBInterface, id uint) error {
	return db.Where("id = ?", id).First(h).Error()
}

// 根据工单号查询
func (h *HangTagProduceV2) GetByWorkCodes(db mysql.DBInterface, workCodes []string) ([]*HangTagProduceV2, error) {
	var list []*HangTagProduceV2
	query := db.Where("produce_state != ?", ProduceStateCancelV2TagP)

	for _, workCode := range workCodes {
		query = query.Or("work_codes LIKE ?", "%"+workCode+"%")
	}

	err := query.Find(&list).Error
	return list, err()
}

// 获取未完成的生产任务
func (h *HangTagProduceV2) GetNotProducedList(db mysql.DBInterface, factoryId int) ([]*HangTagProduceV2, error) {
	var list []*HangTagProduceV2
	query := db.Where("produce_state = ? AND factory_id = ? AND producing_state != ?",
		ProduceStateNotV2TagP, factoryId, ProducingStateProducingV2TagP)
	err := query.Order("type asc, id asc").Find(&list).Error
	return list, err()
}

//// 获取可生产的任务（排除某个ID）
//func (h *HangTagProduceV2) GetAvailableTask(db mysql.DBInterface, excludeId uint, factoryId int) error {
//	return db.Where("produce_state = ? AND factory_id = ? AND id != ? AND producing_state != ?",
//		ProduceStateNotV2TagP, factoryId, excludeId, ProducingStateProducingV2TagP).
//		Order("type asc, id asc").First(h).Error
//}

//// 根据优先级获取可生产任务
//func (h *HangTagProduceV2) GetAvailableTaskWithPriority(db mysql.DBInterface, excludeId uint, factoryId int) error {
//	return db.Where("produce_state = ? AND factory_id = ? AND id != ? AND producing_state != ?",
//		ProduceStateNotV2TagP, factoryId, excludeId, ProducingStateProducingV2TagP).
//		Order("priority_b desc, type asc, id asc").First(h).Error
//}

// 更新生产状态
func (h *HangTagProduceV2) UpdateProduceState(db mysql.DBInterface, state int) error {
	updateData := map[string]interface{}{
		"produce_state": state,
	}

	if state == ProduceStateYesV2TagP {
		updateData["complete_time"] = time.Now().Unix()
	}

	return db.Model(h).Updates(updateData).Error()
}

// 批量更新生产状态
func (h *HangTagProduceV2) BatchUpdateProduceState(db mysql.DBInterface, ids []uint, state int) error {
	updateData := map[string]interface{}{
		"produce_state": state,
	}

	if state == ProduceStateYesV2TagP {
		updateData["complete_time"] = time.Now().Unix()
	}

	return db.Model(&HangTagProduceV2{}).Where("id IN ?", ids).Updates(updateData).Error()
}

// 更新生产中状态
func (h *HangTagProduceV2) UpdateProducingState(db mysql.DBInterface, state int) error {
	return db.Model(h).Updates(map[string]interface{}{
		"producing_state":      state,
		"latest_produce_state": time.Now().Unix(),
	}).Error()
}

// 取消生产任务
func (h *HangTagProduceV2) CancelProduce(db mysql.DBInterface) error {
	return db.Model(h).Update("produce_state", ProduceStateCancelV2TagP).Error()
}

// 设置插队时间
func (h *HangTagProduceV2) SetJumpTime(db mysql.DBInterface, jumpTime int64) error {
	return db.Model(h).Update("jump_time", jumpTime).Error()
}

// 更新生产面
func (h *HangTagProduceV2) UpdateProduceBit(db mysql.DBInterface, produceBit uint64) error {
	return db.Model(h).Update("produce_bit", produceBit).Error()
}

// 获取统计数据
func (h *HangTagProduceV2) GetStatistics(db mysql.DBInterface, factoryId int) (total, notProduce int64, err error) {
	// 总数
	err = db.Model(&HangTagProduceV2{}).
		Where("factory_id = ? AND produce_state != ?", factoryId, ProduceStateCancelV2TagP).
		Count(&total).Error()
	if err != nil {
		return
	}

	// 未完成数
	err = db.Model(&HangTagProduceV2{}).
		Where("factory_id = ? AND produce_state = ?", factoryId, ProduceStateNotV2TagP).
		Count(&notProduce).Error()

	return
}

// 解析吊牌项目JSON
func (h *HangTagProduceV2) ParseHangTagItems() ([]*HangTagProduceItemV2, error) {
	if h.HangTagItemsJson == "" {
		return nil, nil
	}

	var items []*HangTagProduceItemV2
	if err := json.Unmarshal([]byte(h.HangTagItemsJson), &items); err != nil {
		return nil, err
	}

	return items, nil
}

// 设置吊牌项目JSON
func (h *HangTagProduceV2) SetHangTagItems(items []*HangTagProduceItemV2) error {
	data, err := json.Marshal(items)
	if err != nil {
		return err
	}

	h.HangTagItemsJson = string(data)
	return nil
}

// 获取未完成数量
func (h *HangTagProduceV2) GetNotProduceCount(db mysql.DBInterface, factoryId int) (int64, error) {
	var count int64
	err := db.Model(&HangTagProduceV2{}).
		Where("factory_id = ? AND produce_state = ?", factoryId, ProduceStateNotV2TagP).
		Count(&count).Error
	return count, err()
}

// 工厂验证头部信息
func (h *HangTagProduceV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) ([]*model.VerifyHead, int64, error) {
	var list []*model.VerifyHead
	var count int64

	db := mysql.NewUnifiedDB().Table(h.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	if err := db.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	if idMax < 1 && idMin < 1 {
		offset := (pn - 1) * ps
		db = db.Offset(offset).Limit(ps)
	}

	err := db.Find(&list).Error
	return list, count, err()
}

// 创建或更新 - 工厂同步
func (h *HangTagProduceV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	log.Debug("HangTagProduceV2 detailData: ", string(detailData))

	detailObj := new(HangTagProduceV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingHangTagProduce HangTagProduceV2
	err = db.Where("id = ?", detailObj.ID).First(&existingHangTagProduce).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建HangTagProduce失败: ", err)
			return err()
		}
		log.Info("创建HangTagProduce成功: ", detailObj.ID)
	} else {
		// 更新现有记录
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新HangTagProduce失败: ", err)
			return err()
		}
		log.Info("更新HangTagProduce成功: ", detailObj.ID)
	}

	return nil
}
