package factory

import (
	"encoding/json"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*DtgProduct)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*DtgProduct)(nil))
}

const (
	DtgProductCacheNo  = 1
	DtgProductCacheYes = 2
)

const DefaultWorkCode = "dtg"

// 数码直喷的生产信息表
type DtgProduct struct {
	ID             uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	UserID         uint      `json:"user_id"`                           // 所属用户ID
	Source         string    `json:"source"`                            // 来源 template、shop
	DeignID        int       `json:"deign_id"`                          // 模板ID 或 商店商品ID
	Versions       int       `json:"versions"`                          // 版本号  1 2 3 4累加，取自来源模板设计的版本号
	SPU            string    `json:"spu"`                               // spu
	SizeID         string    `json:"size_id"`                           // 尺码，每个尺码图片缩放不一致
	ColorType      string    `json:"color_type"`                        // 颜色类型 0浅色  2深色 | 不同颜色打印参数不一致
	LabelColorType int       `json:"label_color_type" gorm:"default:0"` // 1 浅色领标 2深色领标  0 代表设计模板没有领标
	ProductType    int       `json:"product_type"`                      // 商品的类型 目前 0衣服，1长裤  2短裤  2022-11-23 16:52:00 zc  增加短裤 2023年3月26日17:21:03 zc

	DesignInfo    string `json:"design_info"  gorm:"type:BLOB;"` // 具体的生产信息
	DesignBit     uint32 `json:"design_bit"`                     // 生产面的位运算
	PantDesignBit uint32 `json:"pant_design_bit"`                // 裤子的生产面的位运算  2022-11-23 16:51:53 zc
	PrintCount    int    `json:"print_count"`                    //0 1 2 全部 单面 多面

	Cache        int   `json:"cache" gorm:"default:1"`          // 是否缓存 1 未缓存  2已缓存
	LastUsedTime int64 `json:"last_used_time" gorm:"default:0"` // 最近使用时间
	UsedCount    int   `json:"used_count" gorm:"default:0"`     // 使用次数
}

func (d *DtgProduct) TableName() string {
	return "dtg_product"
}

// Create a new DtgProduct
func (d *DtgProduct) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(d.TableName()).Create(d).Error
}

// 刷新使用时间和使用次数
func (d *DtgProduct) FlushUsed(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Table(d.TableName())

	db = db.Where("id = ?", d.ID)

	updateMap := map[string]interface{}{
		"last_used_time": d.LastUsedTime,
		"used_count":     gorm.Expr("used_count + ?", 1),
	}
	err = db.Updates(updateMap).Error
	return
}

// 根据 source、deign_id、versions 查询
func (d *DtgProduct) QueryBySource() (err error) {
	db := mysql.NewConn().Table(d.TableName())
	db = db.Where("source = ?", d.Source)
	db = db.Where("deign_id = ?", d.DeignID)
	db = db.Where("versions = ?", d.Versions)

	err = db.First(d).Error
	return
}

// GetDesignInfo 根据信息查询记录是否存在，存在返回详细信息
func (d *DtgProduct) GetDesignInfo() error {
	db := mysql.NewConn().Table(d.TableName())
	db = db.Where("user_id = ?", d.UserID)
	db = db.Where("source = ?", d.Source)
	db = db.Where("deign_id = ?", d.DeignID)
	db = db.Where("versions = ?", d.Versions)
	db = db.Where("spu = ?", d.SPU)
	db = db.Where("size_id = ?", d.SizeID)
	db = db.Where("color_type = ?", d.ColorType)
	db = db.Where("product_type = ?", d.ProductType)
	db = db.Where("label_color_type = ?", d.LabelColorType)

	err := db.First(d).Error
	return err
}

// 根据map更新
func (d *DtgProduct) UpdateWithMap(data map[string]interface{}, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&DtgProduct{})

	db = db.Where("user_id = ?", d.UserID)
	db = db.Where("source = ?", d.Source)
	db = db.Where("deign_id = ?", d.DeignID)
	db = db.Where("versions = ?", d.Versions)
	db = db.Where("spu = ?", d.SPU)
	db = db.Where("size_id = ?", d.SizeID)
	db = db.Where("color_type = ?", d.ColorType)
	db = db.Where("product_type = ?", d.ProductType)
	db = db.Where("label_color_type = ?", d.LabelColorType)

	err = db.Updates(data).Error
	return
}

func (d *DtgProduct) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&DtgProduct{})
	db = db.Where("id = ?", d.ID)

	err = db.Updates(d).Error

	return
}

func (d *DtgProduct) UpdateBatch(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&DtgProduct{})
	db = db.Where("id IN (?)", ids)

	err = db.Updates(d).Error

	return
}

// 获取要清除的缓存
func (d *DtgProduct) GetOldData(lastUsedTime int64) (list []*DtgProduct, err error) {

	db := mysql.NewConn().Table(d.TableName())

	db = db.Where("last_used_time < ? and cache = ?", lastUsedTime, DtgProductCacheYes)

	err = db.Limit(10).Find(&list).Error

	return
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (d *DtgProduct) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(d.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (d *DtgProduct) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(DtgProduct)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (d *DtgProduct) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(d.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", d.ID).Updates(d).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", d.ID).Update("updated_at", d.UpdatedAt).Error

	return
}

func (d *DtgProduct) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(d.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*DtgProduct, 0)

	err := db.Find(&list).Error

	return list, err
}

func (d *DtgProduct) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&DtgProduct{}).Where("id IN (?)", ids).Delete(d).Error
	return
}

func (d *DtgProduct) QueryToGenerate(lastUsedTime int64, isReverse bool) (list []*DtgProduct, err error) {

	db := mysql.NewConn().Table(d.TableName()).Where("cache = ?", DtgProductCacheNo)

	db = db.Where("last_used_time >= ?", lastUsedTime).Where("print_count > ?", 0)

	if isReverse {
		db = db.Order("id desc")
	} else {
		db = db.Order("id asc")
	}

	err = db.Limit(10).Find(&list).Error

	return
}

func (d *DtgProduct) Query() (err error) {
	db := mysql.NewConn().Table(d.TableName())

	db = db.Where("id = ?", d.ID)

	err = db.First(d).Error

	return
}
