package factory

import (
	"encoding/json"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*EmbProduct)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*EmbProduct)(nil))
}

const (
	PcfCacheNo  = 1
	PcfCacheYes = 2
)

// 刺绣生产文件表
type EmbProduct struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      uint      `json:"user_id"`      // 所属用户ID
	Source      string    `json:"source"`       // 来源 template、shop
	DeignID     uint      `json:"deign_id"`     // 模板ID 或 商店商品ID
	Versions    int       `json:"versions"`     // 版本号  1 2 3 4累加，取自来源模板设计的版本号
	SPU         string    `json:"spu"`          // spu
	SurfaceName string    `json:"surface_name"` // 对应可打印面名称
	CanvasName  string    `json:"canvas_name"`  // 设计区名称

	DSTName        string `json:"dst_name"`        // 生产文件名
	DSTUrl         string `json:"dst_url"`         // 输出板带路径
	ProductColors  string `json:"product_colors"`  // 生产线色信息json
	PositionImg    string `json:"position_img"`    // 定位图片
	PaperLocation  string `json:"paper_location"`  // 衬纸库位
	ProductPreview string `json:"product_preview"` // 绣框预览图
	FrameName      string `json:"frame_name"`      // 绣框名称 1号 2号| 内部名称

	AuditState  int    `json:"audit_state"`                    // 审核状态 1 待审核  2 已审核  3 审核不通过
	IsComellent int    `json:"is_comellent"`                   // 是否销售强制通过 1、0 否  2 是
	AuditName   string `json:"audit_name"`                     // 审核人
	AuditTime   int64  `json:"audit_time" gorm:"default:0"`    // 审核时间
	AuditRemark string `json:"audit_remark" gorm:"type:BLOB;"` // 审核原因、备注 | 富文本
	Danger      int    `json:"danger"`                         // 损害机器风险  1 无  2 有

	PcfOK int `json:"pcf_ok" gorm:"default:1"` // 是否生成PCF文件 1 未生成  2已生成 由工厂端决定

	// 以下字段是替换板带新增字段  zc 2024年10月15日16:04:04
	ReplaceDSTName string `json:"replace_dst_name"`                  // 替换板带文件名 （源文件-Original）
	ReplaceRemark  string `json:"replace_remark"  gorm:"type:BLOB;"` // 替换说明
	ReplaceJson    string `json:"replace_json"  gorm:"type:BLOB;"`   // 替换板带的详细信息，用于强制通过后，添加到用户素材库
}

func (e *EmbProduct) TableName() string {
	return "emb_product"
}

// Create a new EmbProduct
func (e *EmbProduct) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(e.TableName()).Create(e).Error
}

type EmbProductPage struct {
	UserId       uint   `json:"user_id"`
	SourceList   string `json:"source_list"`
	DeignIdList  string `json:"deign_id_list"`
	VersionsList string `json:"versions_list"`
}

type countSrr struct {
	Count int `json:"count"`
}

// 分页列表数据
func (e *EmbProduct) QueryPage(pn, ps int) (count int, list []*EmbProductPage, err error) {
	db := mysql.NewConn().Table(e.TableName())

	// Set the group_concat_max_len session variable
	err = db.Exec("SET SESSION group_concat_max_len = 10000000").Error
	if err != nil {
		return
	}

	if e.UserID != 0 {
		db = db.Where("user_id = ?", e.UserID)
	}

	if e.AuditState != 0 {
		db = db.Where("audit_state = ?", e.AuditState)
	}

	countCtrs := countSrr{}

	//获取总数
	//err = db.Select("count(distinct user_id)").Find(&count).Error
	err = db.Select("COUNT(distinct user_id) as count").Scan(&countCtrs).Error
	if err != nil {
		return
	}
	count = countCtrs.Count
	//用户ID分组
	clos := "user_id,group_concat(source) source_list,group_concat(deign_id) deign_id_list,group_concat(versions) versions_list"
	db = db.Select(clos).Group("user_id")
	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	if err != nil {
		return
	}

	return count, list, nil
}

type SauditStateData struct {
	UserId uint  `json:"user_id"`
	Count  int64 `json:"count"`
}

// 获取用户指定审核状态数量
func (e *EmbProduct) GetListCountByUserIdsSauditState(userIds []uint, auditState int) (list []*SauditStateData, err error) {
	db := mysql.NewConn().Table(e.TableName())

	//子查询 - 获取去重的source, deign_id, versions数量
	subQuery := db.
		Select("count(distinct source, deign_id, versions) count, user_id").
		Where("user_id in (?)", userIds).
		Where("audit_state = ?", auditState).
		Group("source, deign_id, versions").
		SubQuery()

	//将子查询的结果集进行分组，获取每个user_id的count总和
	//err = db.Raw("select user_id,sum(count) count from (?) as cui group by user_id", subQuery).Find(&list).Error

	// 使用 db.Raw 执行带子查询的 SQL 语句
	err = db.Raw("SELECT user_id, SUM(count) as count FROM (?) as cui GROUP BY user_id", subQuery).
		Scan(&list).Error

	return list, err
}

// 刺绣生产文件表
type EmbProductCache struct {
	ID            uint   `gorm:"PRIMARY_KEY" json:"id"`
	DSTName       string `json:"dst_name"`       // 生产文件名
	DSTUrl        string `json:"dst_url"`        // 输出板带路径
	ProductColors string `json:"product_colors"` // 生产线色信息json
}

// 获取所有待缓存的任务
func (e *EmbProduct) GetCacheTask() (count int, list []*EmbProductCache, err error) {

	db := mysql.NewConn().Table(e.TableName())

	db = db.Where("pcf_ok = ?", PcfCacheNo).Where("audit_state = ?", 2)

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Limit(50).Find(&list).Error

	return
}

// 根据DSTName查询EmbProduct，不存在返回错误
func (e *EmbProduct) GetByDSTName(DSTName string) error {
	db := mysql.NewConn()
	return db.Table(e.TableName()).Where("dst_name = ?", DSTName).First(e).Error
}

func (e *EmbProduct) FinishCache(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["pcf_ok"] = PcfCacheYes

	// 如果有最新更新时间，则更新
	if !e.UpdatedAt.IsZero() {
		updateMap["updated_at"] = e.UpdatedAt
	}

	db = db.Where("dst_name = ?", e.DSTName)

	return db.Model(&EmbProduct{}).Updates(updateMap).Error
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (e *EmbProduct) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(e.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (e *EmbProduct) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(EmbProduct)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (e *EmbProduct) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(e.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", e.ID).Updates(e).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", e.ID).Update("updated_at", e.UpdatedAt).Error

	return
}

func (e *EmbProduct) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(e.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*EmbProduct, 0)

	err := db.Find(&list).Error

	return list, err
}

func (e *EmbProduct) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&EmbProduct{}).Where("id IN (?)", ids).Delete(e).Error
	return
}

type EmbProductList struct {
	ID          uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      uint      `json:"user_id"`      // 所属用户ID
	Source      string    `json:"source"`       // 来源 template、shop
	DeignID     uint      `json:"deign_id"`     // 模板ID 或 商店商品ID
	Versions    int       `json:"versions"`     // 版本号  1 2 3 4累加，取自来源模板设计的版本号
	SPU         string    `json:"spu"`          // spu
	SurfaceName string    `json:"surface_name"` // 对应可打印面名称
	CanvasName  string    `json:"canvas_name"`  // 设计区名称

	FrameName string `json:"frame_name"` // 绣框名称 1号 2号| 内部名称

	AuditState     int    `json:"audit_state"`                     // 审核状态 1 待审核  2 已审核通过  3 审核不通过
	IsComellent    int    `json:"is_comellent"`                    // 是否销售强制通过 1、0 否  2 是
	AuditName      string `json:"audit_name"`                      // 审核人
	AuditTime      int64  `json:"audit_time" gorm:"default:0"`     // 审核时间
	AuditRemark    string `json:"audit_remark" gorm:"type:BLOB;"`  // 审核原因、备注 | 富文本
	Danger         int    `json:"danger"`                          // 损害机器风险  1 无  2 有
	ReplaceDSTName string `json:"replace_dst_name"`                // 替换板带文件名 （源文件-Original）
	ReplaceJson    string `json:"replace_json"  gorm:"type:BLOB;"` // 替换板带的详细信息，用于强制通过后，添加到用户素材库
}

// 根据审核状态|审核任务ID 获取列表
func (e *EmbProduct) GetDetailListByAuditState(pn, ps int, sortState string) (list []*EmbProductList, count int64, err error) {
	db := mysql.NewConn().Table(e.TableName())

	if e.ID != 0 {
		db = db.Where("id = ?", e.ID)
	}
	if e.UserID != 0 {
		db = db.Where("user_id = ?", e.UserID)
	}

	if e.AuditState != 0 {
		if e.AuditState == 4 {
			db = db.Where("audit_state != ?", 1)
		} else {
			db = db.Where("audit_state = ?", e.AuditState)

		}
	}

	if sortState != "" {
		db = db.Order(sortState)
	} else {
		// 默认按创建时间升序
		db = db.Order("created_at asc")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	db = db.Offset((pn - 1) * ps).Limit(ps)

	err = db.Find(&list).Error

	return
}

// GetDetailById
func (e *EmbProduct) GetDetailById(id int) (err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Where("id = ?", id).First(e).Error

	return
}

// UpdateByMapData
func (e *EmbProduct) UpdateByMapData(data map[string]interface{}) (err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Model(e).Updates(data).First(e).Error

	return
}

func (e *EmbProduct) UpdateIdsByMapData(ids []uint, data map[string]interface{}) (err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Model(e).Where("id in (?)", ids).Updates(data).First(e).Error

	return
}

// GetCountBySourceDesignIDVersions
func (e *EmbProduct) GetCountBySourceDesignIDVersions() (count int, err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Where("source = ? AND deign_id = ? AND versions = ?", e.Source, e.DeignID, e.Versions).
		Where("audit_state != 2").
		Count(&count).Error

	return
}

func (e *EmbProduct) GetCountBySourcesDesignIdsVersions(sources []string, designIds, versions []int) (list []*EmbProduct, err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Where("source in (?) AND deign_id in (?) AND versions in (?)", sources, designIds, versions).
		Find(&list).Error

	return
}

// GetNotPassedByUserId
func (e *EmbProduct) GetNotPassedByUserId() (list []*EmbProduct, err error) {
	db := mysql.NewConn().Table(e.TableName())

	err = db.Where("user_id = ? AND audit_state != ?", e.UserID, e.AuditState).Find(&list).Error

	return
}

func (e *EmbProduct) GetListByInfo() (list []*EmbProductList, err error) {
	db := mysql.NewConn().Table(e.TableName())

	if e.ID != 0 {
		db = db.Where("id = ?", e.ID)
	}
	if e.Source != "" {
		db = db.Where("source = ?", e.Source)
	}
	if e.DeignID != 0 {
		db = db.Where("deign_iD = ?", e.DeignID)
	}
	if e.Versions != 0 {
		db = db.Where("versions = ?", e.Versions)
	}
	if e.UserID != 0 {
		db = db.Where("user_id = ?", e.UserID)
	}

	err = db.Find(&list).Error

	return
}

// GetFirstNotAudit 获取最久远的2个未审核任务
func (e *EmbProduct) GetFirstNotAudit() (list []*EmbProduct, err error) {

	db := mysql.NewConn().Table(e.TableName())

	err = db.Where("audit_state = ?", 1).Order("id asc").Limit(2).Find(&list).Error

	return
}
