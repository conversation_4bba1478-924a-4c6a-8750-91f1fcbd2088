package outbound

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/global"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

const (
	_                         = iota
	OutboundStateNormal       = 1 // 正常出库
	OutboundStateCancel       = 2 // 取消出库
	OutboundStateRepeat       = 3 // 运单重复
	OutboundStateUpdateWeight = 4 // 更新重量
)

const (
	CarrierTypeSF     = 1 // 顺丰
	CarrierTypeKuaYue = 2 // 跨越
	CarrierTypeZiTi   = 3 // 自提
)

var CarrierNameMap = map[int]string{
	CarrierTypeSF:     "顺丰",
	CarrierTypeKuaYue: "跨越",
	CarrierTypeZiTi:   "自提",
}

const (
	PodCarrierTypeLt     = 1 // 乐天
	PodCarrierTypeCne    = 2 // cne
	PodCarrierTypeYun    = 3 // 云途
	PodCarrierTypeEquick = 4 // Equick
	PodCarrierTypeSF     = 5 // 顺丰
	PodCarrierTypeUbi    = 7
	PodCarrierTypeWish   = 8 // 纬狮物流
)

const (
	PodCarrierTypeOverSeaOne   = 101 // 海外仓 - 1号
	PodCarrierTypeOverSeaTwo   = 102 // 海外仓 - 2号
	PodCarrierTypeOverSeaThree = 103 // 海外仓 - 3号
)

var PodCarrierNameMap = map[int]string{
	PodCarrierTypeLt:     "乐天",
	PodCarrierTypeCne:    "CNE",
	PodCarrierTypeYun:    "云途",
	PodCarrierTypeEquick: "Equick",
	PodCarrierTypeSF:     "顺丰",
	PodCarrierTypeUbi:    "UBI",
	PodCarrierTypeWish:   "纬狮物流",
}

var OverSeaNameMap = map[int]string{
	PodCarrierTypeOverSeaOne:   "海外仓 - 1号",
	PodCarrierTypeOverSeaTwo:   "海外仓 - 2号",
	PodCarrierTypeOverSeaThree: "海外仓 - 3号",
}

var PodCarrierNameMapId = map[string]int{
	"LTIANEXP":   1, // 乐天
	"CNE":        2, // CNE
	"YUN":        3, // 云途
	"Equick":     4, // Equick
	"SF Express": 5, // 顺丰
	"UBI":        7, //UBI
	"WSH":        8, // 纬狮物流
}

// 注册自动建表
func init() {
	// 注册表的时候，指定表的options
	// 如果没有指定，默认为`mysql.OPTION_USE_INNODB_ENGINE`
	mysql.RegisterTable((*OutboundLog)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*OutboundLog)(nil))
}

type OutboundLog struct {
	ID                 int64     `json:"id"  gorm:"PRIMARY_KEY"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	OrderCode          string    `json:"order_code"`                            // 包裹订单 id
	WorkCode           string    `json:"work_code"`                             // 工单 id，去掉最后 -0x
	ItemCount          int       `json:"item_count" gorm:"default:0"`           // 件数
	DeliveryTime       int64     `json:"delivery_time" gorm:"default:0"`        // 贴单时间
	OutboundTime       int64     `json:"outbound_time" gorm:"default:0"`        // 出库扫描时间
	OutboundStateFirst int       `json:"outbound_state_first" gorm:"default:1"` // 第一次扫描出库时的状态，不随后面重复扫描而变动
	OutboundState      int       `json:"outbound_state" gorm:"default:1"`       // 是否正常出库，即出库状态，（正常出库，取消出库，运单重复）
	//IsOutbound         int       `json:"is_outbound" gorm:"default:1"`          // 是否为已确认出库，1：确认出库，2：未确认出库、
	Remark string `json:"remark"` // 备注
	//IsPushToPod int    `json:"is_push_to_pod"  gorm:"default:1"` // 是否已经推送到 pod 后端，1：未推送，10：已推送。----如果推送成功，就直接置为10，如果推送失败，就加1，直到等于 RetryTime
	OutboundID int64 `json:"outbound_id" gorm:"default:0"` // 出库单的 id，没有出库时为 0
	//OutboundLogID  int64  `json:"outbound_log_id"`                  // 这个字段是只有在 pod 后端才有的，等于关联工厂本地的创建 出库记录 的主键id，一般不会用到，预留
	RefNo          string `json:"ref_no"`                   // pod 订单的运单号，可能是 EQ 的标签号
	RefNoReal      string `json:"ref_no_real"`              // pod 订单的真实运单号，如果 EQ 的话，可能 refNo 只是标签号
	PodCarrierType int    `json:"pod_carrier_type"`         // pod 端的物流商, 1:乐天, 2:cne, 3:云途，4：顺丰，5：自提，10x:海外仓
	Name           string `json:"name"`                     // 扫描人名字
	Weight         string `json:"weight" gorm:"default:''"` // 重量
}

func (o *OutboundLog) TableName() string {
	return "outbound_log"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (o *OutboundLog) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (o *OutboundLog) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*OutboundLog, 0)

	err := db.Find(&list).Error

	return list, err
}

func (o *OutboundLog) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&OutboundLog{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

func (o *OutboundLog) DeleteByRefNo(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&OutboundLog{}).Where("ref_no = ?", o.RefNo).Delete(o).Error
	return
}

func (o *OutboundLog) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(OutboundLog)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[uint(detailObj.ID)] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (o *OutboundLog) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(o.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error

	return
}

// 创建
func (o *OutboundLog) Create(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Table(o.TableName()).Create(o).Error
	return
}

// 根据 id 判断是否存在
func (o *OutboundLog) Exist(tx ...*gorm.DB) (exist bool, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Table(o.TableName()).Where("id = ?", o.ID).First(o).Error
	if err == nil {
		exist = true
	} else if err == gorm.ErrRecordNotFound {
		exist = false
		err = nil
	} else {
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
	}
	return
}

// 获取列表
func (o *OutboundLog) List(pn, ps int) (total int64, list []*OutboundLog, err error) {
	db := mysql.NewConn().Table(o.TableName())

	//if o.IsOutbound > 0 {
	//	db = db.Where("is_outbound = ?", o.IsOutbound)
	//}

	if o.OutboundID > 0 {
		db = db.Where("outbound_id = ?", o.OutboundID)
	}

	err = db.Count(&total).Error
	if err != nil {
		log.Error(err)
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

// 获取将要同步到 pod 后端的数据
//func (o *OutboundLog) QueryToSync() (list []*OutboundLog, err error) {
//
//	db := mysql.NewConn().Table(o.TableName())
//	err = db.Where("is_push_to_pod <= ?", RetryTime).Find(&list).Error
//	return
//}

// 待出库的记录
func (o *OutboundLog) QueryToOutbound(getCount bool) (list []*OutboundLog, total, normalCount, repeatCount, cancelCount int64, err error) {
	db := mysql.NewConn().Table(o.TableName())
	if o.PodCarrierType > 0 {
		db = db.Where("pod_carrier_type = ?", o.PodCarrierType)
	}

	db = db.Where("outbound_id < 1")

	if getCount {
		err = db.Count(&total).Error
		if err != nil {
			return
		}

		err = db.Where("outbound_state = ?", OutboundStateNormal).Count(&normalCount).Error
		if err != nil {
			return
		}

		err = db.Where("outbound_state = ?", OutboundStateRepeat).Count(&repeatCount).Error
		if err != nil {
			return
		}

		err = db.Where("outbound_state = ?", OutboundStateCancel).Count(&cancelCount).Error
		if err != nil {
			return
		}
	}

	err = db.Order("id desc").Find(&list).Error
	return
}

// 尝试推送到 pod 后端，失败了
//func (o *OutboundLog) HasTryOne() (err error) {
//
//	db := mysql.NewConn().Model(&OutboundLog{})
//	if o.ID > 0 {
//		err = db.Where("id = ?", o.ID).UpdateColumn("is_push_to_pod", gorm.Expr("is_push_to_pod + ?", 1)).Error
//	}
//	return
//}

//func (o *OutboundLog) SetPushState(state int) (err error) {
//
//	db := mysql.NewConn().Model(&OutboundLog{})
//	if o.ID > 0 {
//		err = db.Where("id = ?", o.ID).Update("is_push_to_pod = ?", PushComplete).Error
//	}
//	return
//}

func (o *OutboundLog) QueryList() (list []*OutboundLog, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no = ?", o.RefNo)
	}

	if o.OutboundID > 0 {
		db = db.Where("outbound_id = ?", o.OutboundID)
	}

	err = db.Find(&list).Error
	return
}

func (o *OutboundLog) QueryListByRefNo(refNo []string) (list []*OutboundLog, err error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("ref_no IN (?)", refNo)

	err = db.Find(&list).Error
	return
}

func (o *OutboundLog) Check() (list []*OutboundLog, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no = ?", o.RefNo)
	}

	if o.OutboundID > 0 {
		db = db.Where("outbound_id = ?", o.OutboundID)
	}

	err = db.Find(&list).Error
	return
}

// 已出库，则将所有已出库
func (o *OutboundLog) UpdateOutbound(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&OutboundLog{})

	db = db.Where("outbound_id < 1")
	if o.PodCarrierType > 0 {
		db = db.Where("pod_carrier_type = ?", o.PodCarrierType)
	}

	err = db.Updates(o).Error
	return
}

// 已出库，则将所有已出库
func (o *OutboundLog) GetOutboundList() (list []*OutboundLog, err error) {
	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("outbound_id < 1")
	db = db.Where("pod_carrier_type = ?", o.PodCarrierType)

	err = db.Find(&list).Error
	return
}

type OutboundWeightSum struct {
	OrderCode string `json:"order_code"`               // 包裹订单 id
	WorkCode  string `json:"work_code"`                // 工单 id，去掉最后 -0x
	Weight    string `json:"weight" gorm:"default:''"` // 重量
}

// 根据 pod_carrier_type 计算所有重量
func (o *OutboundLog) GetOutboundWeightSum() (list []*OutboundWeightSum, err error) {

	fields := global.GetJSONFieldNames(OutboundWeightSum{}, "")

	db := mysql.NewConn().Table(o.TableName())

	// 查询字段
	db = db.Select(fields)

	db = db.Where("outbound_id < 1")
	db = db.Where("pod_carrier_type = ?", o.PodCarrierType)

	// 只筛选正常出库的重量
	db = db.Where("outbound_state = ?", OutboundStateNormal)

	err = db.Find(&list).Error
	return
}

// 更新
func (o *OutboundLog) Update() (err error) {
	db := mysql.NewConn().Model(&OutboundLog{})
	if o.ID > 0 {
		db = db.Where("id = ?", o.ID)
	}

	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}

	if len(o.RefNo) > 0 {
		db = db.Where("ref_no = ?", o.RefNo)
	}
	err = db.Updates(o).Error
	return
}

func (o *OutboundLog) GetListByWorkCodes(workCodes []string) (list []*OutboundLog, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if len(workCodes) > 0 {
		db = db.Where("work_code IN (?)", workCodes)
	}

	if o.OutboundState > 0 {
		db = db.Where("outbound_state = ?", o.OutboundState)
	}

	err = db.Find(&list).Error

	return
}

// 根据出库时间区间查询
func (o *OutboundLog) GetListWithOutboundTime(timeS, timeE int64) (list []*OutboundLog, err error) {

	db := mysql.NewConn().Table(o.TableName())

	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}

	if timeE > 0 {
		db = db.Where("created_at < FROM_UNIXTIME(?)", timeE)
	}

	err = db.Find(&list).Error

	return
}

// 判断是否为海外仓
func (o *OutboundLog) IsOversea() (isOversea bool) {
	isOversea = o.PodCarrierType > 100 && o.PodCarrierType < 200
	return
}

// 获取海外仓编号
func (o *OutboundLog) GetOverseaCode() (overseaCode int) {
	if o.PodCarrierType > 100 && o.PodCarrierType < 200 {
		overseaCode = o.PodCarrierType % 100
	}
	return
}

// 根据order_code 查询数据
func (o *OutboundLog) GetListByOrderCode() (err error) {
	db := mysql.NewConn().Table(o.TableName())

	if len(o.OrderCode) > 0 {
		db = db.Where("order_code = ?", o.OrderCode)
	}

	err = db.First(o).Error

	return
}
