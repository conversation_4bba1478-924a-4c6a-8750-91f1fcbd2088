package outbound

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

// 注册自动建表
func init() {
	// 注册表的时候，指定表的options
	// 如果没有指定，默认为`mysql.OPTION_USE_INNODB_ENGINE`
	mysql.RegisterTable((*Outbound)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Outbound)(nil))
}

const (
	RetryTime    = 5
	PushComplete = 10
)

// 出库单状态
const (
	OUTBOUND_STATE_DEFAULT  = 0 // 默认状态
	OUTBOUND_STATE_NORMAL   = 1 // 正常状态
	OUTBOUND_STATE_UNNORMAL = 2 // 异常状态
)

const (
	OutboundTypeNormal   = 1 // 出库单类型
	OutboundTypeOverseas = 2 // 海外仓
)

type Outbound struct {
	ID             int64     `json:"id"  gorm:"PRIMARY_KEY"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	OverseaNumber  int       `json:"oversea_number" gorm:"default:0"` // 海外仓编号
	CarrierType    int       `json:"carrier_type"`                    // 从工厂发货的物流商，例如：顺丰，跨越，自提
	OutboundTime   int64     `json:"outbound_time"`                   // 出库时间
	DeliveryTimeS  int64     `json:"delivery_time_s"`                 // 第一个贴单时间
	DeliveryTimeE  int64     `json:"delivery_time_e"`                 // 最后一个贴单时间
	PackageCount   int       `json:"package_count"`                   // 包裹数
	Weight         string    `json:"weight"`                          // 实际重量
	PodCarrierType int       `json:"pod_carrier_type"`                // pod 端的物流商, 1:乐天, 2:cne, 3:云途
	RefNo          string    `json:"ref_no"`                          // 运单号
	//IsPushToPod    int       `json:"is_push_to_pod"  gorm:"default:1"` // 是否已经推送到 pod 后端，1：未推送，10：已推送。----如果推送成功，就直接置为10，如果推送失败，就加1，直到等于 RetryTime
	//OutboundID     int64     `json:"outbound_id"`                      // 这个字段是只有在 pod 后段才有的，用来关联 出库记录 的，这个是工厂本地的创建 出库单 的主键 id
	State          int    `json:"state" gorm:"default:0"`             // 出库单状态，参考 const OUTBOUND_STATE_
	DiffCount      int    `json:"diff_count"  gorm:"default:0"`       // 出库差异数量
	BoxInfo        string `json:"box_info" gorm:"type:BLOB;"`         // 箱信息，json 格式，目前只有在海外仓时使用
	BoxCount       int    `json:"box_count" gorm:"default:0"`         // 箱数
	BoxTotalWeight string `json:"box_total_weight" gorm:"default:''"` // 箱总重量
	WaybillList    string `json:"waybill_list" gorm:"type:BLOB"`      // 出库时的首程运单列表，【字符串数组的json序列化】，目前在海外仓使用（2025年2月11日19:19:52）
}

type BoxInfoItem struct {
	Number int    `json:"number"` // 箱号
	Weight string `json:"weight"` // 箱重
}

func (o *Outbound) TableName() string {
	return "outbound"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (o *Outbound) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(o.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (o *Outbound) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(o.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Outbound, 0)

	err := db.Find(&list).Error

	return list, err
}

func (o *Outbound) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&Outbound{}).Where("id IN (?)", ids).Delete(o).Error
	return
}

func (o *Outbound) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Outbound)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[uint(detailObj.ID)] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (o *Outbound) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(o.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", o.ID).Updates(o).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", o.ID).Update("updated_at", o.UpdatedAt).Error

	return
}

// 创建
func (o *Outbound) Create(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Table(o.TableName()).Create(o).First(o).Error
	return
}

// 获取列表
func (o *Outbound) List(pn, ps int) (list []*Outbound, total int64, err error) {

	db := mysql.NewConn().Table(o.TableName())

	err = db.Count(&total).Error
	if err != nil {
		log.Error(err)
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

// 获取将要同步到 pod 后端的数据
//func (o *Outbound) QueryToSync() (list []*Outbound, err error) {
//
//	db := mysql.NewConn().Table(o.TableName())
//	err = db.Where("is_push_to_pod <= ?", RetryTime).Find(&list).Error
//	return
//}

func (o *Outbound) Query() (err error) {

	db := mysql.NewConn().Table(o.TableName())

	if o.ID > 0 {
		db = db.Where("id = ?", o.ID)
	}

	err = db.First(o).Error
	return
}

// 分页筛选获取列表
func (o *Outbound) ListByFilter(pn, ps, state, sort_state int) (list []*Outbound, total int64, err error) {

	db := mysql.NewConn().Table(o.TableName()).Where("oversea_number != ?", 0)
	if state == 1 {
		db = db.Where("ref_no = ?", "")
	} else {
		db = db.Where("ref_no != ?", "")
	}

	if sort_state != 0 {
		if sort_state == 1 {
			db = db.Order("created_at asc")
		} else {
			db = db.Order("created_at desc")
		}
	} else {
		if state == 1 {
			db = db.Order("created_at asc")
		} else {
			db = db.Order("created_at desc")
		}
	}

	err = db.Count(&total).Error
	if err != nil {
		log.Error(err)
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

// 根据map更新
func (o *Outbound) UpdateByMap(data map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Outbound{}).Where("id = ?", o.ID).Updates(data).Error
	return
}

// 尝试推送到 pod 后端，失败了
//func (o *Outbound) HasTryOne() (err error) {
//
//	db := mysql.NewConn().Table(o.TableName())
//	if o.ID > 0 {
//		err = db.Where("id = ?", o.ID).UpdateColumn("is_push_to_pod", gorm.Expr("is_push_to_pod + ?", 1)).Error
//	}
//	return
//}

//func (o *Outbound) SetPushState(state int) (err error) {
//
//	db := mysql.NewConn().Table(o.TableName())
//	if o.ID > 0 {
//		err = db.Where("id = ?", o.ID).Update("is_push_to_pod = ?", PushComplete).Error
//	}
//	return
//}
