package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	. "zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	RegisterTable((*PackagingProduce)(nil), OPTION_USE_INNODB_ENGINE,
		OPTION_USE_UTF8mb4_CHARSET,
		OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*PackagingProduce)(nil))
}

const (
	BrandProduceBitFront = 0 // 包装吊牌打印，正面
	BrandProduceBitBack  = 1 // 包装吊牌打印，白面
)

const (
	BrandProduceBitValueFront = uint64(1) << BrandProduceBitFront // 包装吊牌打印，正面
	BrandProduceBitValueBack  = uint64(1) << BrandProduceBitBack  // 包装吊牌打印，白面
)

const (
	PriorityBitAreaB = 0
)

const (
	PriorityAreaB = uint64(1) << PriorityBitAreaB // 优先分配到 A 区生产
)

type PackagingProduce struct {
	ID                 uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Type               int       `json:"type" gorm:"default:1"`                         // 打印类型，1：单面，2：双面，默认1单面
	ProduceState       int       `json:"produce_state" gorm:"default:1"`                // 状态，1：未完成，2：已完成，10：已取消
	CompleteTime       int64     `json:"complete_time"`                                 // 完成时间
	WorkCodes          string    `json:"work_codes"  gorm:"type:BLOB;"`                 // 所有工单的工单id，一维数组
	PackagingItemsJson string    `json:"packaging_items_json"  gorm:"type:MEDIUMBLOB;"` // 对应每个自定义包装的详细信息
	ProducingState     int       `json:"producing_state" gorm:"default:0"`              // 生产状态，1：正在生产中
	ProduceBit         uint64    `json:"produce_bit" gorm:"default:0"`                  // 生产面，如果等于0的话说明已经生产完成了
	JumpTime           int64     `json:"jump_time"  gorm:"default:0"`                   // 插队时间，时间戳
	LatestProduceState int64     `json:"latest_produce_state" gorm:"default:0"`         // 最近一次生产的时间
	PriorityB          uint64    `json:"priority_b"  gorm:"default:0"`                  // 优先分配到 B 区，优先级最高的排序，bit 位标识，也就是说可以标记 64 个优先级，并且越大，优先级越高，！！！逻辑搞反了，优先级越高，是越不要往B区分配
	FactoryId          int       `json:"factory_id"  gorm:"default:1"`                  // 工厂id
}

type PackagingProduceListResp struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Type         int       `json:"type" gorm:"default:1"`          // 打印类型，1：单面，2：双面，默认1单面
	ProduceState int       `json:"produce_state" gorm:"default:1"` // 状态，1：未完成，2：已完成
	CompleteTime int64     `json:"complete_time"`                  // 完成时间
	WorkCodes    string    `json:"work_codes"  gorm:"type:BLOB;"`  // 所有工单的工单id，一维数组
	JumpTime     int64     `json:"jump_time"`                      // 插队时间，时间戳
}

type PackagingItem struct {
	WithTag                 int    `json:"with_tag"` // 0： 旧数据，1：新数据
	DeliveryCar             string `json:"delivery_car"`
	InventoryStorageName    string `json:"inventory_storage_name"`
	TagInventoryStorageName string `json:"tag_inventory_storage_name"`
	DesignInfo              string `json:"design_info"`
	TagDesignInfo           string `json:"tag_design_info"`
	OrderCode               string `json:"order_code"`
	WorkCode                string `json:"work_code"`
	CName                   string `json:"c_name"`
	PackagingInfoId         int64  `json:"packaging_info_id"`
	TagInfoId               int64  `json:"packaging_info_id"`
	State                   int    `json:"state"`         // 0: 默认状态为正常状态（也为了适配旧数据），10：已取消
	TagModel                string `json:"tag_model"`     // 吊牌的模特图
	PackModel               string `json:"pack_model"`    // 包装袋的模特图
	SKU                     string `json:"sku"`           // sku信息，额外的筛选项
	OrderItemId             uint   `json:"order_item_id"` // 订单子项 id
}

func (p *PackagingProduce) TableName() string {
	return "packaging_produce"
}

// 新增
func (p *PackagingProduce) Create(tx ...*gorm.DB) error {
	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

// 查询
func (p *PackagingProduce) GetList(pn, ps int, timeS, timeE int64, orderType int) (list []*PackagingProduceListResp, totalCount int, notProduceCount int, err error) {
	db := NewConn().Table(p.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	dbEmpty := db

	// id
	if p.ID > 0 {
		db = db.Where("id = ?", p.ID)
	}

	// 生成时间
	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	// 工单号
	if len(p.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+p.WorkCodes+"%")
	}

	// 打印类型，单面双面
	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}

	// 是否已经完成
	if p.ProduceState > 0 {
		db = db.Where("produce_state = ?", p.ProduceState)
	}

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	err = db.Count(&totalCount).Error
	if err != nil {
		return
	}

	err = dbEmpty.Where("produce_state = ?", ProduceStateNot).Count(&notProduceCount).Error
	if err != nil {
		return
	}

	// 先根据类型排序（单面排在前面），然后根据 id 进行排序
	if orderType == 1 {
		err = db.Order("type asc,produce_state asc,id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	} else {
		err = db.Order("type asc,produce_state asc,id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	}

	return
}

// 查询
func (p *PackagingProduce) GetListV2(pn, ps int, timeS, timeE int64, orderType int) (list []*PackagingProduceListResp, totalCount int, notProduceCount int, err error) {
	db := NewConn().Table(p.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	dbEmpty := db

	// id
	if p.ID > 0 {
		db = db.Where("id = ?", p.ID)
	}

	// 生成时间
	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	// 工单号
	if len(p.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+p.WorkCodes+"%")
	}

	// 打印类型，单面双面
	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}

	// 完成和未完成改为必选的
	db = db.Where("produce_state = ?", p.ProduceState)

	err = db.Count(&totalCount).Error
	if err != nil {
		return
	}

	err = dbEmpty.Where("produce_state = ?", ProduceStateNot).Count(&notProduceCount).Error
	if err != nil {
		return
	}

	switch p.ProduceState {
	case 1: // 未生产完成
		// 先根据类型排序（单面排在前面），然后根据 id 进行排序
		if orderType == 1 {
			err = db.Order("jump_time desc, type asc,id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		} else {
			err = db.Order("jump_time desc, type asc,id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		}
	case 2: // 已生产完成
		// 先根据类型排序（单面排在前面），然后根据 id 进行排序
		if orderType == 1 {
			err = db.Order("type asc,id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		} else {
			err = db.Order("type asc,id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		}
	}

	return
}

func (p *PackagingProduce) Query() (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if len(p.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+p.WorkCodes+"%")
	}
	if p.ProduceState > 0 {
		db = db.Where("produce_state = ?", p.ProduceState)
	}
	if p.ID > 0 {
		db = db.Where("id = ?", p.ID)
	}
	if p.ProducingState > 0 {
		db = db.Where("producing_state = ?", p.ProducingState)
	}
	err = db.Find(&list).Error
	return
}

func (p *PackagingProduce) QueryOne() (err error) {
	db := NewConn().Table(p.TableName())
	if len(p.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+p.WorkCodes+"%")
	}
	if p.ProduceState > 0 {
		db = db.Where("produce_state = ?", p.ProduceState)
	}
	if p.ID > 0 {
		db = db.Where("id = ?", p.ID)
	}
	if p.ProducingState > 0 {
		db = db.Where("producing_state = ?", p.ProducingState)
	}
	err = db.Order("id desc").First(p).Error
	return
}

func (p *PackagingProduce) QueryListByIds(idList []uint) (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName())
	if len(p.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+p.WorkCodes+"%")
	}
	if p.ProduceState > 0 {
		db = db.Where("produce_state = ?", p.ProduceState)
	}
	//if p.ID > 0 {
	//	db = db.Where("id = ?", p.ID)
	//}

	if len(idList) > 0 {
		db = db.Where("id IN (?)", idList)
	}

	if p.ProducingState > 0 {
		db = db.Where("producing_state = ?", p.ProducingState)
	}
	err = db.Find(&list).Error
	return
}

// 获取损耗展示页面的数据
func (p *PackagingProduce) QueryPreMinus(idList []uint) (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	//if p.ID > 0 {
	//	db = db.Where("id != ?", p.ID)
	//}

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	if len(idList) > 0 {
		db = db.Where("id NOT IN (?)", idList)
	}

	db = db.Limit(2).Order("latest_produce_state desc")

	err = db.Find(&list).Error
	return
}

func (p *PackagingProduce) GetDetail() (err error) {
	db := NewConn().Table(p.TableName()).Where("id = ?", p.ID)
	return db.First(p).Error
}

// 获取未生产的数量
func (p *PackagingProduce) GetNotProduceCount() (notProduceCount int64, err error) {
	db := NewConn().Table(p.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	err = db.Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).Count(&notProduceCount).Error
	return
}

// 获取一个新的生产任务id，排除掉当前正在生产未完成的id（正在生产的有两个）
func (p *PackagingProduce) GetNotProduceExcludeId(id uint) (err error) {
	db := NewConn().Table(p.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b asc, jump_time desc,type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}
	db = db.Where("id != ?", id)
	return db.First(p).Error
}

// 完成一个包装吊牌生产任务后，获取一个新的任务，并根据 B 区优先级排序
func (p *PackagingProduce) GetNotProduceExcludeIdWithPriorityB(id uint) (err error) {
	db := NewConn().Table(p.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b desc, jump_time desc,type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}
	db = db.Where("id != ?", id)
	return db.First(p).Error
}

func (p *PackagingProduce) GetNotProduce() (err error) {
	db := NewConn().Table(p.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).Order("type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}
	if p.ID > 0 {
		db = db.Where("id != ?", p.ID)
	}
	return db.First(p).Error
}

func (p *PackagingProduce) GetListByIds(ids []uint) (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName())

	db = db.Where("id IN (?)", ids)

	err = db.Order("type asc,id asc").Find(&list).Error
	return
}

func (p *PackagingProduce) GetListByIdRange(idS, idE uint) (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName())

	db = db.Where("id >= ? AND id <= ?", idS, idE)

	err = db.Find(&list).Error
	return
}

// 获取多个生产序列
func (p *PackagingProduce) GetProduceList(count int) (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b desc, jump_time desc, type asc, id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.FactoryId > 0 {
		db = db.Where("factory_id = ?", p.FactoryId)
	}

	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}

	err = db.Limit(count).Find(&list).Error
	return
}

func (p *PackagingProduce) GetNotProduceList() (list []*PackagingProduce, err error) {
	db := NewConn().Table(p.TableName()).Where("produce_state = ?", ProduceStateNot).Order("type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if p.Type > 0 {
		db = db.Where("type = ?", p.Type)
	}
	err = db.Find(&list).Error
	return
}

// 根据工单号来查，目前是用来删除已退款的工单的查询，所以这里要排除掉生产到一半的
func (p *PackagingProduce) GetListByWorkCode(workCodes []string) (list []*HangTagProduce, err error) {
	db := NewConn().Table(p.TableName())

	//for _, oneWorkCode := range workCodes {
	//	if len(oneWorkCode) > 0 {
	//		db = db.Where("work_codes LIKE ?", "%"+oneWorkCode+"%")
	//	}
	//}

	sqlStr := ""

	for _, oneWorkCode := range workCodes {
		if len(oneWorkCode) > 0 {
			//db = db.Where("work_codes LIKE ?", "%"+oneWorkCode+"%")
			if len(sqlStr) > 0 {
				sqlStr = fmt.Sprintf("%s OR work_codes LIKE '%s'", sqlStr, "%"+oneWorkCode+"%")
			} else {
				sqlStr = fmt.Sprintf("work_codes LIKE '%s'", "%"+oneWorkCode+"%")
			}
		}
	}

	if len(sqlStr) < 1 {
		return
	}

	db = db.Where(sqlStr)

	// 目前是用来删除已退款的工单的查询，所以这里要排除掉生产到一半的
	//db = db.Where("latest_produce_state < 10")

	err = db.Order("type asc,id desc").Find(&list).Error
	return
}

// 更新
func (p *PackagingProduce) Update(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&PackagingProduce{}).Where("id = ?", p.ID).Updates(p).First(p).Error
	return
}

// 更新，并且更新0
func (p *PackagingProduce) UpdatePrduceState(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["produce_state"] = p.ProduceState
	updateMap["updated_at"] = p.UpdatedAt
	updateMap["complete_time"] = p.CompleteTime
	updateMap["producing_state"] = p.ProducingState
	updateMap["produce_bit"] = p.ProduceBit
	updateMap["latest_produce_state"] = p.LatestProduceState

	err = db.Table(p.TableName()).Where("id = ?", p.ID).Updates(updateMap).Error
	return
}

func (p *PackagingProduce) UpdateByIds(ids []uint, tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&PackagingProduce{}).Where("id IN (?)", ids).Updates(p).First(p).Error
	return
}

func (c *PackagingProduce) GetDetailList(ids []uint) (interface{}, error) {

	db := NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*PackagingProduce, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *PackagingProduce) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&PackagingProduce{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *PackagingProduce) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *PackagingProduce) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(PackagingProduce)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *PackagingProduce) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 更新
func (p *PackagingProduce) Delete(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&PackagingProduce{}).Where("id = ?", p.ID).Delete(p).Error
	return
}

// 将包装吊牌的生产任务设置为已取消，对应的工单均已取消
func (p *PackagingProduce) Cancel(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["updated_at"] = time.Now()
	updateMap["produce_state"] = ProduceStateCancel

	err = db.Table(p.TableName()).Where("id = ?", p.ID).Updates(updateMap).Error

	return
}
