package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ReplaceOrder)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ReplaceOrder)(nil))
}

type ReplaceOrder struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt    time.Time `json:"created_at,omitempty"`
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	CompletionAT time.Time `json:"completion_at"`

	OrderCode string `json:"order_code"`
	State     int    `json:"state"`

	PurchaseID  uint   `json:"purchase_id"`                        // 采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` // 工厂名

	RefNo        string `json:"ref_no"`        // 运单号
	PlanCount    int    `json:"plan_count"`    // 计划退货数量
	RealityCount int    `json:"reality_count"` // 真实退货数量

	RefundAmount string `json:"refund_amount"` // 退款金额

	ReplenishJson string `json:"replenish_json"  gorm:"type:BLOB;"` // 具体补货的json数组
}

type ReplaceResp struct {
	ReplaceOrder
	CreatedAtUnix    int64 `json:"created_at_unix"`
	CompletionATUnix int64 `json:"completion_at_unix"`
	StandingTime     int   `json:"standing_time"`
}

type ReplaceJson struct {
	ColorID         string `json:"color_id,omitempty"`   //颜色id
	ColorName       string `json:"color_name,omitempty"` //颜色名称
	SizeName        string `json:"size_name,omitempty"`  //尺码名称
	ProductCName    string `json:"product_c_name"`       //商品中文名
	Location        string `json:"location"`
	Number          int    `json:"number"`           // 计划退货数量
	RealityCount    int    `json:"reality_count"`    // 实际退货数量
	RepairCount     int    `json:"repair_count"`     // 返修数量
	SuccessfulCount int    `json:"successful_count"` // 返修成功数量

	Remark string `json:"remark"` //备注
}

func (r *ReplaceOrder) TableName() string {
	return "replace_order"
}

// 新增
func (r *ReplaceOrder) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(r.TableName()).Create(r).Error
}

// 查询
func (r *ReplaceOrder) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(r.TableName()).Where("id = ?", r.ID)

	return db.First(r).Error
}

// 生产订单号
func (r *ReplaceOrder) NewOrderCode() (err error) {
	var count int
	// 获取当前时间
	now := time.Now()

	// 将当前时间截取为当天0时0分0秒
	startOfToday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 将当前时间加一天再截取为当天0时0分0秒
	startOfTomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())

	err = mysql.NewConn().Table(r.TableName()).Where("purchase_id = ?", r.PurchaseID).
		Where("created_at BETWEEN ? AND ?", startOfToday, startOfTomorrow).Count(&count).Error
	if err != nil {
		return
	}

	r.OrderCode = fmt.Sprintf("%s%02d%02d", now.Format("20060102"), r.PurchaseID, count+1)

	return

}

// 获取列表
func (r *ReplaceOrder) GetList(pn, ps int, timeS, timeE int64) (list []*ReplaceResp, count int, err error) {
	db := mysql.NewConn().Table(r.TableName())

	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}
	if r.State != 0 {
		db = db.Where("state = ?", r.State)
	}

	if r.OrderCode != "" {
		db = db.Where("order_code like ?", "%"+r.OrderCode+"%")
	}

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	if timeS == 0 && timeE == 0 && r.State == 0 && r.OrderCode == "" && r.PurchaseID == 0 {
		db = db.Where("state != ?", OrderStatusCompleted)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Order("id desc").Find(&list).Error

	return
}

func (r *ReplaceOrder) DeleteByID() error {
	return mysql.NewConn().Table(r.TableName()).Where("id = ?", r.ID).Delete(r).Error
}

func (r *ReplaceOrder) PutReplaceOrder(updateMap map[string]interface{}, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ReplaceOrder{}).Where("id = ?", r.ID).Updates(updateMap).Error
	return
}

func (c *ReplaceOrder) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ReplaceOrder, 0)

	err := db.Find(&list).Error

	return list, err
}

func (r *ReplaceOrder) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ReplaceOrder{}).Where("id IN (?)", ids).Delete(r).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ReplaceOrder) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ReplaceOrder) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ReplaceOrder)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ReplaceOrder) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
