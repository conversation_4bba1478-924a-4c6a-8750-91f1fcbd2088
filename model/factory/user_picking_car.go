package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*UserPickingCar)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*UserPickingCar)(nil))
}

type UserPickingCar struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	UserId    int64     `json:"user_id"  gorm:"default:0"`    // 所属用户ID
	FactoryId int       `json:"factory_id"  gorm:"default:0"` // 所属工厂ID
	Name      string    `json:"name" gorm:"default:''"`       // 专属拣车名
	//Inventory int       `json:"inventory" gorm:"default:0"`   // 库存数量
}

func (u *UserPickingCar) TableName() string {
	return "user_picking_car"
}

func (u *UserPickingCar) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserPickingCar{}).Create(u).Error
}

func (u *UserPickingCar) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*UserPickingCar, 0)

	err := db.Find(&list).Error

	return list, err
}

func (u *UserPickingCar) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&UserPickingCar{}).Where("id IN (?)", ids).Delete(u).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (u *UserPickingCar) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(u.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", u.ID).Updates(u).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", u.ID).Update("updated_at", u.UpdatedAt).Error

	return
}

func (u *UserPickingCar) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(UserPickingCar)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (u *UserPickingCar) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(u.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

const (
	PickingCarAll    = 0
	PickingCarBind   = 1
	PickingCarUnbind = 2
)

func (u *UserPickingCar) GetList(pn, ps int, bindType int) (list []*UserPickingCar, err error) {
	db := mysql.NewConn().Table(u.TableName())

	// 根据工厂筛选
	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	switch bindType {
	case PickingCarAll: // 筛选全部
	case PickingCarBind: // 筛选已经绑定
		db = db.Where("user_id > 0")
	case PickingCarUnbind: // 筛选未绑定
		db = db.Where("user_id < 1")
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// Bind 绑定
func (u *UserPickingCar) Bind(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&UserPickingCar{}).Where("id = ?", u.ID)

	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	updateMap := make(map[string]interface{})
	updateMap["updated_at"] = time.Now()
	// 更新 user_id 字段
	updateMap["user_id"] = u.UserId
	// 清空库存
	//updateMap["inventory"] = 0

	err = db.Updates(updateMap).Error

	return
}

// Unbind 解绑
func (u *UserPickingCar) Unbind(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&UserPickingCar{}).Where("id = ?", u.ID)

	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	updateMap := make(map[string]interface{})
	updateMap["updated_at"] = time.Now()
	// 更新 user_id 字段
	updateMap["user_id"] = -1
	// 清空库存
	//updateMap["inventory"] = 0

	err = db.Updates(updateMap).Error

	return
}

func (u *UserPickingCar) QueryList() (list []*UserPickingCar, err error) {

	db := mysql.NewConn().Table(u.TableName())

	if u.UserId > 0 {
		db = db.Where("user_id = ?", u.UserId)
	}

	// 根据工厂筛选
	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	err = db.Find(&list).Error

	return
}

// 查询
func (u *UserPickingCar) Query() (err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id = ?", u.ID)

	err = db.First(u).Error
	return
}

// 查询
func (u *UserPickingCar) QueryByCondition() (err error) {

	db := mysql.NewConn().Table(u.TableName())

	// id
	if u.ID > 0 {
		db = db.Where("id = ?", u.ID)
	}

	// 专属拣车名
	if len(u.Name) > 0 {
		db = db.Where("name = ?", u.Name)
	}

	err = db.First(u).Error
	return
}

// 查询
func (u *UserPickingCar) QueryOne() (err error) {

	db := mysql.NewConn().Table(u.TableName())

	// 工厂id
	db = db.Where("factory_id = ?", u.FactoryId)

	// 用户id
	db = db.Where("user_id = ?", u.UserId)

	err = db.First(u).Error
	return
}

// 更新
func (u *UserPickingCar) Update(tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&UserPickingCar{}).Where("id = ?", u.ID)

	err = db.Update(u).Error
	return
}
