package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*UserPickingPlan)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*UserPickingPlan)(nil))
}

const (
	UserPickingPlanUnsubmitted = 1
	UserPickingPlanSubmitted   = 2
	UserPickingPlanCompleted   = 3
)

type UserPickingPlan struct {
	ID           uint      `json:"id" gorm:"PRIMARY_KEY"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	TemplateId   string    `json:"template_id"`   // 模板id
	Version      int       `json:"version"`       // 版本号
	Type         int       `json:"type"`          // 类型，参考 Const TagBagProductType
	CnName       string    `json:"cn_name"`       // 中文名称
	UserId       int64     `json:"user_id"`       // 用户id
	FactoryId    int       `json:"factory_id"`    // 工厂id
	PickingCar   string    `json:"picking_car"`   // 专属拣车号
	Count        int       `json:"count"`         // 计划数量
	CountSmall   int       `json:"count_small"`   // 计划数量, 如果是包装袋的话，就是小件数量
	Status       int       `json:"status"`        // 状态，参考 Const UserPickingPlan
	Founder      string    `json:"founder"`       // 创建人
	CompleteTime int64     `json:"complete_time"` // 完成时间
}

func (u *UserPickingPlan) TableName() string {
	return "user_picking_plan"
}

// 新增
func (u *UserPickingPlan) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&UserPickingPlan{}).Create(u).Error
}

func (u *UserPickingPlan) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*UserPickingPlan, 0)

	err := db.Find(&list).Error

	return list, err
}

func (u *UserPickingPlan) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&UserPickingPlan{}).Where("id IN (?)", ids).Delete(u).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (u *UserPickingPlan) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(u.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", u.ID).Updates(u).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", u.ID).Update("updated_at", u.UpdatedAt).Error

	return
}

func (u *UserPickingPlan) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(UserPickingPlan)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (u *UserPickingPlan) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(u.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 删除
func (u *UserPickingPlan) Delete(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&UserPickingPlan{}).Where("id IN (?)", ids).Delete(u).Error
	return
}

// 分页列表
func (u *UserPickingPlan) GetList(pn, ps, orderType int) (list []*UserPickingPlan, count int, err error) {

	db := mysql.NewConn().Table(u.TableName())

	// 工厂id
	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	// 类型
	if u.Type > 0 {
		db = db.Where("type = ?", u.Type)
	}

	// 状态
	if u.Status > 0 {
		db = db.Where("status = ?", u.Status)
	}

	// 用户id
	if u.UserId > 0 {
		db = db.Where("user_id = ?", u.UserId)
	}

	// 模板id
	if len(u.TemplateId) > 0 {
		db = db.Where("template_id LIKE (?)", "%"+u.TemplateId+"%")
	}

	// 版本
	if u.Version > 0 {
		db = db.Where("version = ?", u.Version)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	switch orderType {
	case 0: // 创建时间降序
		db = db.Order("created_at desc")
	case 1: // 创建时间升序
		db = db.Order("created_at asc")
	case 2: // 完成时间降序
		db = db.Order("complete_time desc")
	case 3: // 完成时间升序
		db = db.Order("complete_time asc")
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 根据 id 列表查询
func (u *UserPickingPlan) QueryList(ids []int64) (list []*UserPickingPlan, err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("id IN (?)", ids)

	err = db.Find(&list).Error

	return
}

// 根据状态，获取数量
func (u *UserPickingPlan) GetCountByStatus() (count int, err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("status = ?", u.Status)

	if len(u.TemplateId) > 0 {
		db = db.Where("template_id = ?", u.TemplateId)
	}

	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	if u.Version > 0 {
		db = db.Where("version = ?", u.Version)
	}

	err = db.Count(&count).Error

	return
}

// 获取未完成的数量，包括：未提交、已提交
func (u *UserPickingPlan) GetUnFinishedCount() (count int, err error) {

	db := mysql.NewConn().Table(u.TableName())

	db = db.Where("status = ? OR status = ?", UserPickingPlanUnsubmitted, UserPickingPlanSubmitted)

	if u.FactoryId > 0 {
		db = db.Where("factory_id = ?", u.FactoryId)
	}

	if u.UserId > 0 {
		db = db.Where("user_id = ?", u.UserId)
	}

	err = db.Count(&count).Error

	return
}

// 提交生产任务
func (u *UserPickingPlan) Submit(ids []int64, tx ...*gorm.DB) (err error) {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&UserPickingPlan{}).Where("id IN (?)", ids).Update("status", UserPickingPlanSubmitted).Error
	return
}

func (u *UserPickingPlan) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&UserPickingPlan{}).Where("id = ?", u.ID).Updates(u).Error
	return
}
