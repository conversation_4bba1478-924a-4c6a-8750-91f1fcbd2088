package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*EmbProductV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// EmbProductV2 刺绣产品表 - GORM v2版本
type EmbProductV2 struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      uint      `json:"user_id"`      // 所属用户ID
	Source      string    `json:"source"`       // 来源 template、shop
	DeignID     uint      `json:"deign_id"`     // 模板ID 或 商店商品ID
	Versions    int       `json:"versions"`     // 版本号
	SPU         string    `json:"spu"`          // spu
	SurfaceName string    `json:"surface_name"` // 对应可打印面名称
	CanvasName  string    `json:"canvas_name"`  // 设计区名称

	DSTName        string `json:"dst_name"`        // 生产文件名
	DSTUrl         string `json:"dst_url"`         // 输出板带路径
	ProductColors  string `json:"product_colors"`  // 生产线色信息json
	PositionImg    string `json:"position_img"`    // 定位图片
	PaperLocation  string `json:"paper_location"`  // 衬纸库位
	ProductPreview string `json:"product_preview"` // 绣框预览图
	FrameName      string `json:"frame_name"`      // 绣框名称

	AuditState  int    `json:"audit_state"`                   // 审核状态 1 待审核 2 已审核 3 审核不通过
	IsComellent int    `json:"is_comellent"`                  // 是否销售强制通过
	AuditName   string `json:"audit_name"`                    // 审核人
	AuditTime   int64  `json:"audit_time" gorm:"default:0"`   // 审核时间
	AuditRemark string `json:"audit_remark" gorm:"type:BLOB"` // 审核原因、备注
	Danger      int    `json:"danger"`                        // 损害机器风险

	PcfOK int `json:"pcf_ok" gorm:"default:1"` // 是否生成PCF文件

	// 替换板带相关字段
	ReplaceDSTName string `json:"replace_dst_name"`                // 替换板带文件名
	ReplaceRemark  string `json:"replace_remark" gorm:"type:BLOB"` // 替换说明
	ReplaceJson    string `json:"replace_json" gorm:"type:BLOB"`   // 替换板带的详细信息
}

func (e *EmbProductV2) TableName() string {
	return "emb_product"
}

// 统一数据库接口方法
func (e *EmbProductV2) Create(db mysql.DBInterface) error {
	return db.Create(e).Error()
}

func (e *EmbProductV2) Query(db mysql.DBInterface) error {
	return db.Where(e).First(e).Error()
}

func (e *EmbProductV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*EmbProductV2, int64, error) {
	var list []*EmbProductV2
	var count int64

	query := db.Model(&EmbProductV2{})
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (e *EmbProductV2) Update(db mysql.DBInterface) error {
	return db.Save(e).Error()
}

func (e *EmbProductV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(e).Error()
}

func (e *EmbProductV2) Delete(db mysql.DBInterface) error {
	return db.Delete(e).Error()
}

// 根据用户ID查询刺绣产品
func (e *EmbProductV2) GetListByUserID(db mysql.DBInterface, userID uint) ([]*EmbProductV2, error) {
	var list []*EmbProductV2
	err := db.Where("user_id = ?", userID).Find(&list).Error
	return list, err()
}

// 根据SPU查询
func (e *EmbProductV2) GetListBySPU(db mysql.DBInterface, spu string) ([]*EmbProductV2, error) {
	var list []*EmbProductV2
	err := db.Where("spu = ?", spu).Find(&list).Error
	return list, err()
}

// 根据审核状态查询
func (e *EmbProductV2) GetListByAuditState(db mysql.DBInterface, auditState int) ([]*EmbProductV2, error) {
	var list []*EmbProductV2
	err := db.Where("audit_state = ?", auditState).Find(&list).Error
	return list, err()
}

// 更新审核状态
func (e *EmbProductV2) UpdateAuditState(db mysql.DBInterface, auditState int, auditName, auditRemark string) error {
	return db.Model(e).Updates(map[string]interface{}{
		"audit_state":  auditState,
		"audit_name":   auditName,
		"audit_time":   time.Now().Unix(),
		"audit_remark": auditRemark,
	}).Error()
}

// 更新PCF状态
func (e *EmbProductV2) UpdatePcfStatus(db mysql.DBInterface, pcfOK int) error {
	return db.Model(e).Update("pcf_ok", pcfOK).Error()
}

// 强制通过审核
func (e *EmbProductV2) ForcePass(db mysql.DBInterface, adminName string) error {
	return db.Model(e).Updates(map[string]interface{}{
		"audit_state":  2,
		"is_comellent": 2,
		"audit_name":   adminName,
		"audit_time":   time.Now().Unix(),
	}).Error()
}

// 工厂验证头部信息
func (e *EmbProductV2) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) ([]*model.VerifyHead, int64, error) {
	var list []*model.VerifyHead
	var count int64

	db := mysql.NewUnifiedDB().Table(e.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		db = db.Where("id <= ?", idMax)
	}
	if idMin > 0 {
		db = db.Where("id >= ?", idMin)
	}
	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	if err := db.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	if idMax < 1 && idMin < 1 {
		offset := (pn - 1) * ps
		db = db.Offset(offset).Limit(ps)
	}

	err := db.Find(&list).Error
	return list, count, err()
}

// 创建或更新 - 工厂同步
func (e *EmbProductV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	detailObj := new(EmbProductV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingRecord EmbProductV2
	err = db.Where("id = ?", detailObj.ID).First(&existingRecord).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建EmbProduct失败: ", err)
			return err()
		}
		log.Info("创建EmbProduct成功: ", detailObj.ID)
	} else {
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新EmbProduct失败: ", err)
			return err()
		}
		log.Info("更新EmbProduct成功: ", detailObj.ID)
	}

	return nil
}
