package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*WorkLog)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*WorkLog)(nil))

}

type WorkLogInterface interface {
	GetFullCodeDesc() (fullCodeDesc string)
}

const (
	CodeTypePrint                     = 1
	CodeTypeCheckout                  = 2
	CodeTypeDelivery                  = 3
	CodeTypeMinus                     = 4
	CodeTypeHangTagPrint              = 5
	CodeTypeHangTagMinus              = 6
	CodeTypePackagingPrint            = 7
	CodeTypePackagingMinus            = 8
	CodeTypePrePrint                  = 9
	CodeTypePrePrintStamping          = 10 // 烫压，已弃用
	CodeTypeHangTagCheckout           = 11 // 吊牌质检
	CodeTypePackagingCheckout         = 12 // 包装袋质检
	CodeTypeEmbroidery                = 13 // 刺绣生产
	CodeTypeInspectionQualityCheckout = 14 // 来料质检
	CodeTypeDingma                    = 20 // 顶码操作
)

const (
	BrandFaceFront = 1
	BrandFaceBack  = 2
)

var CodeTypeDesc = map[int]string{
	CodeTypePrint:                     "打印",
	CodeTypeCheckout:                  "质检",
	CodeTypeDelivery:                  "发货",
	CodeTypeMinus:                     "库存损耗",
	CodeTypeHangTagPrint:              "吊牌打印",
	CodeTypeHangTagMinus:              "库存损耗（吊牌）",
	CodeTypePackagingPrint:            "包装打印",
	CodeTypePackagingMinus:            "库存损耗（包装）",
	CodeTypePrePrint:                  "前处理", // 2024-5-28 喷淋 更名 前处理
	CodeTypePrePrintStamping:          "烫压",
	CodeTypeHangTagCheckout:           "吊牌质检",
	CodeTypePackagingCheckout:         "包装质检",
	CodeTypeEmbroidery:                "刺绣生产",
	CodeTypeInspectionQualityCheckout: "来料质检",
	CodeTypeDingma:                    "顶码",
}

const (
	MinusReasonCodeMaterial  = 1
	MinusReasonCodeHuman     = 2
	MinusReasonCodeHangTag   = 11
	MinusReasonCodePackaging = 12

	MinusReasonCodeMaterialRetreat = 101
)

var MinusReasonDesc = map[int]string{
	MinusReasonCodeMaterial:  "原料-无法退货",
	MinusReasonCodeHuman:     "人为",
	MinusReasonCodeHangTag:   "吊牌",
	MinusReasonCodePackaging: "包装袋",

	MinusReasonCodeMaterialRetreat: "原料-可以退货",
}

const (
	CheckTypeSingleNoPackage = 1
	CheckTypeMultiNoPackage  = 2
	CheckTypeSinglePackage   = 3
	CheckTypeMultiPackage    = 4
)

var CheckTypeDesc = map[int]string{
	CheckTypeSingleNoPackage: "单件-无包装",
	CheckTypeMultiNoPackage:  "多件-无包装",
	CheckTypeSinglePackage:   "单件-有包装",
	CheckTypeMultiPackage:    "多件-有包装",
}

const (
	LastOperateNo  = 1
	LastOperateYes = 2
)

type WorkLog struct {
	ID                     uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`
	WorkerId               int64     `json:"worker_id"  gorm:"not null"`                // 工人 id
	PhoneNumber            string    `json:"phone_number"`                              // 手机号
	WorkerName             string    `json:"worker_name"`                               // 工人姓名
	WorkerOrderId          string    `json:"worker_order_id"`                           // 工单 id
	CodeType               int       `json:"code_type"`                                 // 二维码类型，1：打印，2：质检，3：发货，4：库存损耗，5：吊牌打印，6：库存损耗（吊牌），7：包装打印，8：库存损耗（包装）, 9: 投影仪前处理
	FaceCode               int       `json:"face_code"`                                 // 打印面码，如果是刺绣的话，就是第一个绣框
	CodeDesc               string    `json:"code_desc"`                                 // 发货，质检，正面-中部，目前用于记录衣服打印的是哪个面，例如，正面-左下
	MinusReason            int       `json:"minus_reason"`                              // 库存损耗原因
	MinusReasonRemark      string    `json:"minus_reason_desc"`                         // 库存损耗原因说明
	LastOperate            int       `json:"last_operate" gorm:"default:1"`             // 1：不是最后一次扫描，2：是最后一次扫描
	StationMac             string    `json:"station_mac"`                               // 工位 mac 地址
	StationDesc            string    `json:"station_desc"`                              // 工位名称
	FineAmount             float32   `json:"fine_amount"`                               // 人为损耗的罚款金额
	Price                  float32   `json:"price"`                                     // 单价
	CheckType              int       `json:"check_type" gorm:"default:0"`               // 质检分类 0-无 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	MinusTime              int64     `json:"minus_time" gorm:"default:0"`               // 库存损耗时间 0-无损耗 非0-有损耗
	QualityInspectionCount int       `json:"quality_inspection_count" gorm:"default:0"` // 来料质检数量
	FactoryId              int       `json:"factory_id"  gorm:"default:1"`              // 工作记录属于哪个工厂
}

type WorkLogListItem struct {
	ID                     uint      `gorm:"PRIMARY_KEY" json:"ID"`
	WorkerOrderId          string    `json:"worker_order_id"` // 工单 id
	CodeType               int       `json:"code_type"`       // 二维码类型
	PhoneNumber            string    `json:"phone_number"`    // 手机号
	WorkerName             string    `json:"worker_name"`     // 工人姓名
	CreatedAt              time.Time `json:"created_at"`
	CodeDesc               string    `json:"code_desc"`                                 // 发货，质检，正面-中部
	MinusReason            int       `json:"minus_reason"`                              // 库存损耗原因
	StationMac             string    `json:"station_mac"`                               // 工位 mac 地址
	StationDesc            string    `json:"station_desc"`                              // 工位名称
	MinusReasonRemark      string    `json:"minus_reason_desc"`                         // 库存损耗原因说明
	FineAmount             float32   `json:"fine_amount"`                               // 人为损耗的罚款金额
	Price                  float32   `json:"price"`                                     // 单价
	CheckType              int       `json:"check_type" gorm:"default:0"`               // 质检分类 0-无 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	QualityInspectionCount int       `json:"quality_inspection_count" gorm:"default:0"` // 来料质检数量
}

func (w *WorkLog) TableName() string {
	return "work_log"
}

// 新增
func (w *WorkLog) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(w.TableName()).Create(w).Error
}

// 查找列表
func (w *WorkLog) QueryAll(pn, ps int, timeS, timeE int64, workerIdList []int) (list []*WorkLogListItem, count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number LIKE (?)", "%"+w.PhoneNumber+"%")
	}

	if len(w.WorkerName) > 0 {
		db = db.Where("worker_name LIKE (?)", "%"+w.WorkerName+"%")
	}

	if w.WorkerId > 0 {
		db = db.Where("worker_id = ?", w.WorkerId)
	}

	if len(workerIdList) > 0 {
		db = db.Where("worker_id IN (?)", workerIdList)
	}

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id LIKE (?)", "%"+w.WorkerOrderId+"%")
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	db = db.Order("id desc")
	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

// 库存损耗列表列表
func (w *WorkLog) QueryMinusList(pn, ps int, timeS, timeE int64, minusType int) (list []*WorkLog, count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number LIKE (?)", "%"+w.PhoneNumber+"%")
	}

	if len(w.WorkerName) > 0 {
		db = db.Where("worker_name LIKE (?)", "%"+w.WorkerName+"%")
	}

	if w.WorkerId > 0 {
		db = db.Where("worker_id = ?", w.WorkerId)
	}

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id LIKE (?)", "%"+w.WorkerOrderId+"%")
	}

	// 0: 所有的库存损耗，1：衣服的库存损耗，2：包装和吊牌的库存损耗
	switch minusType {
	case 1: // 衣服的库存损耗
		db = db.Where("code_type = ?", CodeTypeMinus)
	case 2: // 包装吊牌的库存损耗
		db = db.Where("code_type = ? OR code_type = ?", CodeTypeHangTagMinus, CodeTypePackagingMinus)
	default:
		db = db.Where("code_type = ? OR code_type = ? OR code_type = ?", CodeTypeMinus, CodeTypeHangTagMinus, CodeTypePackagingMinus)
	}

	//if w.CodeType > 0 {
	//	db = db.Where("code_type = ?", w.CodeType)
	//}

	err = db.Count(&count).Error
	if err != nil {
		return
	}
	db = db.Order("id desc")
	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (w *WorkLog) HasPrePrinted() (hasPrePrinted bool, err error) {

	db := mysql.NewConn().Table(w.TableName())

	count := 0
	// 工作类型为 前处理，并且为有效的工作记录

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	err = db.Where("last_operate = ?", w.LastOperate).Count(&count).Error
	if err == nil {
		hasPrePrinted = count > 0
	}
	return
}

func (w *WorkLog) PreWorkList() (list []*WorkLog, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if w.MinusTime == 100 {
		db = db.Where("minus_time < 1")
	}

	err = db.Where("last_operate = ?", w.LastOperate).Order("id desc").Find(&list).Error

	return
}

// 统计某一个操作
func (w *WorkLog) Count(timeS, timeE int64, operateType int) (count int64, err error) {
	db := mysql.NewConn().Table(w.TableName())

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if operateType > 0 {
		db = db.Where("code_type = ?", operateType)
	}

	if w.WorkerId > 0 {
		db = db.Where("worker_id = ?", w.WorkerId)
	}

	db = db.Where("last_operate = ?", LastOperateYes)

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	return
}

// 统计某个绩效
func (w *WorkLog) CountOperator() (count int64, err error) {

	db := mysql.NewConn().Table(w.TableName())

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	return
}

type ItemCount struct {
	WorkerId    int64   `json:"worker_id"  gorm:"not null"` // 工人 id
	CodeType    int     `json:"code_type"`                  // 二维码类型，1：打印，2：质检，3：发货，4：库存损耗，5：吊牌打印，6：库存损耗（吊牌），7：包装打印，8：库存损耗（包装）, 9: 投影仪前处理
	Total       int64   `json:"total"`                      // 总数，根据 worker_id,code_type 分类
	SumPrice    float32 `json:"sum_price"`                  // 总价格
	SumQuantity int64   `json:"sum_quantity"`               // 来料质检特殊统计
	SumMinus    float32 `json:"sum_minus"`                  // 总损耗
}

func (w *WorkLog) CountPerformance(timeS, timeE int64, operateTypes []int) (resp map[int64]map[int]int64, priceResp map[int64]map[int]float32, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(w.WorkerName) > 0 {
		db = db.Where("worker_name LIKE ?", "%"+w.WorkerName+"%")
	}

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number LIKE ?", "%"+w.PhoneNumber+"%")
	}

	db = db.Where("last_operate = ?", LastOperateYes)

	itemCounts := make([]*ItemCount, 0)
	err = db.Select("worker_id, code_type, COUNT(code_type) as total , SUM(price) as sum_price,SUM(fine_amount) as sum_minus, SUM(quality_inspection_count) as sum_quantity").
		Where("code_type IN (?)", operateTypes).
		Group("worker_id,code_type").
		Scan(&itemCounts).Error
	if err != nil {
		return nil, nil, err
	}

	itemClassify := make(map[int64][]*ItemCount)
	for _, oneItem := range itemCounts {
		_, has := itemClassify[oneItem.WorkerId]
		if !has {
			itemClassify[oneItem.WorkerId] = make([]*ItemCount, 0)
		}
		itemClassify[oneItem.WorkerId] = append(itemClassify[oneItem.WorkerId], oneItem)
	}

	// 数量统计
	resp = make(map[int64]map[int]int64)
	for workerID, oneItemClassify := range itemClassify {
		operateTypeMap := make(map[int]int64)
		for _, oneItem := range oneItemClassify {
			if oneItem.CodeType == CodeTypeInspectionQualityCheckout {
				operateTypeMap[oneItem.CodeType] = oneItem.SumQuantity
			} else {
				operateTypeMap[oneItem.CodeType] = oneItem.Total
			}
		}
		resp[workerID] = operateTypeMap
	}

	// 价格统计
	priceResp = make(map[int64]map[int]float32)
	for workerID, oneItemClassify := range itemClassify {
		operateTypeMap := make(map[int]float32)
		for _, oneItem := range oneItemClassify {
			// 人为库存损耗统计fine_amount
			if oneItem.CodeType == CodeTypeMinus {
				operateTypeMap[oneItem.CodeType] = oneItem.SumMinus
			} else {
				operateTypeMap[oneItem.CodeType] = oneItem.SumPrice
			}
		}
		priceResp[workerID] = operateTypeMap
	}

	return
}

// 根据分类获取绩效
func (w *WorkLog) CountTotalPerformance(timeS, timeE int64, operateTypes []int) (resp map[int]*ItemCount, err error) {

	db := mysql.NewConn().Table(w.TableName())

	db = db.Where("last_operate = ?", LastOperateYes)

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(w.WorkerName) > 0 {
		db = db.Where("worker_name LIKE ?", "%"+w.WorkerName+"%")
	}

	if len(w.PhoneNumber) > 0 {
		db = db.Where("phone_number LIKE ?", "%"+w.PhoneNumber+"%")
	}

	itemCounts := make([]*ItemCount, 0)
	err = db.Select("code_type, COUNT(code_type) as total , SUM(price) as sum_price,SUM(fine_amount) as sum_minus, SUM(quality_inspection_count) as sum_quantity").
		Where("code_type IN (?)", operateTypes).
		Group("code_type").
		Scan(&itemCounts).Error
	if err != nil {
		return nil, err
	}
	// 初始化防止类型不存在异常
	resp = make(map[int]*ItemCount)
	for k, _ := range CodeTypeDesc {
		resp[k] = &ItemCount{}
	}
	for _, item := range itemCounts {
		resp[item.CodeType] = item
	}
	return
}

// 查找
func (w *WorkLog) Query() (list []*WorkLog, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if w.LastOperate > 0 {
		db = db.Where("last_operate = ?", w.LastOperate)
	}

	db = db.Order("id desc")
	err = db.Find(&list).Error

	return
}

// 查找
func (w *WorkLog) QueryListForWorkOrderDetail() (list []*WorkLog, err error) {
	db := mysql.NewConn().Table(w.TableName())

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	db = db.Order("id asc")
	err = db.Find(&list).Error

	return
}

// 查找
func (w *WorkLog) QueryListByCreatedAt(timeS, timeE int64) (list []*WorkLog, err error) {
	db := mysql.NewConn().Table(w.TableName())
	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	if len(w.WorkerOrderId) > 0 {
		db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	}

	if w.WorkerId > 0 {
		db = db.Where("worker_id = ?", w.WorkerId)
	}

	err = db.Find(&list).Error

	return
}

func (w *WorkLog) CountByCreatedAt(timeS, timeE int64) (count int, err error) {
	db := mysql.NewConn().Table(w.TableName())
	db = db.Where("last_operate = ?", LastOperateYes)
	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at < FROM_UNIXTIME(?)", timeE)
	}

	//if w.FactoryId > 0 {
	//	db = db.Where("factory_id = ?", w.FactoryId)
	//}

	if w.MinusTime > 0 {
		db = db.Where("minus_time = 0 OR minus_time > ?", w.MinusTime)
	} else {
		db = db.Where("minus_time = 0")
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.WorkerId > 0 {
		db = db.Where("worker_id = ?", w.WorkerId)
	}

	err = db.Count(&count).Error

	return
}

func (w *WorkLog) CountByCodeType(timeS, timeE int64) (count int, err error) {
	db := mysql.NewConn().Table(w.TableName())
	db = db.Where("last_operate = ?", LastOperateYes)
	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at < FROM_UNIXTIME(?)", timeE)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	err = db.Count(&count).Error

	return
}

type WorkLogPerformance struct {
	Total         int64  `json:"total"`                       // 总数量
	WorkerId      int64  `json:"worker_id"  gorm:"not null"`  // 工人 id
	PhoneNumber   string `json:"phone_number"`                // 手机号
	WorkerName    string `json:"worker_name"`                 // 工人姓名
	WorkerOrderId string `json:"worker_order_id"`             // 工单 id
	CodeType      int    `json:"code_type"`                   // 二维码类型，1：打印，2：质检，3：发货，4：库存损耗，5：吊牌打印，6：库存损耗（吊牌），7：包装打印，8：库存损耗（包装）, 9: 投影仪前处理
	FaceCode      int    `json:"face_code"`                   // 打印面码，如果是刺绣的话，就是第一个绣框
	CodeDesc      string `json:"code_desc"`                   // 发货，质检，正面-中部，目前用于记录衣服打印的是哪个面，例如，正面-左下
	CheckType     int    `json:"check_type" gorm:"default:0"` // 质检分类 0-无 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	MinusReason   int    `json:"minus_reason"`                // 库存损耗原因
	//LastOperate   int    `json:"last_operate" gorm:"default:1"` // 1：不是最后一次扫描，2：是最后一次扫描
}

func (w *WorkLog) GetListByRange(timeS, timeE int64) (list []*WorkLogPerformance, err error) {
	db := mysql.NewConn().Table(w.TableName())
	db = db.Where("last_operate = ?", LastOperateYes)

	db = db.Select("COUNT(*) as total,worker_id,phone_number,worker_name,worker_order_id,code_type,face_code,code_desc,check_type,minus_reason")

	// 时间筛选
	if timeS > 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE > 0 {
		db = db.Where("created_at < FROM_UNIXTIME(?)", timeE)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if w.CodeType > 0 {
		db = db.Where("code_type = ?", w.CodeType)
	}

	// 每个人每个工序进行统计
	db = db.Group("worker_id,code_type,face_code,check_type")

	err = db.Find(&list).Error

	return
}

// 更新，目前根据 id 更新
func (w *WorkLog) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLog{})

	err = db.Updates(w).Where("id = ?", w.ID).First(w).Error

	return
}

// 更新，目前根据 id 更新
func (w *WorkLog) SetLastOperateByWokCode(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLog{})

	err = db.Updates(w).First(w).Error

	return
}

// 被再次打印了，就将之前的工作记录失效，不记录到绩效里面
func (w *WorkLog) SetOldLogDisable(tx ...*gorm.DB) (list []*WorkLog, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLog{}).Where("worker_order_id = ?", w.WorkerOrderId)
	if w.CodeType == CodeTypePrePrint {
		db = db.Where("code_type = ?", w.CodeType)
		w.CodeType = 0
	} else {
		db = db.Where("code_type = ?", w.CodeType)
	}

	if w.FaceCode > 0 {
		db = db.Where("face_code = ?", w.FaceCode)
	}

	if len(w.CodeDesc) > 0 {
		db = db.Where("code_desc = ?", w.CodeDesc)
	}

	// 只更新未损耗过的工作记录，这里通过小于10来判断，预留一些状态
	db = db.Where("minus_time < 10")

	err = db.Updates(w).Find(&list).Error

	return
}

func (c *WorkLog) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*WorkLog, 0)

	err := db.Find(&list).Error

	return list, err
}

func (w *WorkLog) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&WorkLog{}).Where("id IN (?)", ids).Delete(w).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *WorkLog) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *WorkLog) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(WorkLog)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *WorkLog) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 工单进行了损耗，将某个工人的工作记录设置为不统计进绩效
func (w *WorkLog) CancelWorkPerformance(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLog{})

	db = db.Where("worker_id = ? AND worker_order_id = ?", w.WorkerId, w.WorkerOrderId)

	// 损耗的工作记录排除掉
	db = db.Where("code_type != ?", CodeTypeMinus)

	// 不更新工人 id
	w.WorkerId = 0

	err = db.Updates(w).Error
	return
}

// 工单进行了损耗，给非负责人的工作记录标记上损耗时间，但是仍然统计进绩效
func (w *WorkLog) UpdateMinusTime(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(&WorkLog{})

	if w.WorkerId > 0 {
		db = db.Where("worker_id != ? ", w.WorkerId)
		// 不更新工人 id
		w.WorkerId = 0
	}

	db = db.Where("worker_order_id = ?", w.WorkerOrderId)
	err = db.Updates(w).Error
	return
}

// 筛选专属拣车生产任务的绩效
func (w *WorkLog) GetWorkLogByWorkCodeLength(minLen, maxLen int, minWorkLogId, maxWorkLogId int64) (list []*WorkLog, err error) {

	db := mysql.NewConn().Model(&WorkLog{})

	// 筛选
	db = db.Where("LENGTH(worker_order_id) >= ? AND LENGTH(worker_order_id) <= ?", minLen, maxLen)

	db = db.Where("id >= ? AND id <= ?", minWorkLogId, maxWorkLogId)

	err = db.Find(&list).Error

	return
}

// 查询指定创建时间之后创建的工作记录
func (w *WorkLog) GetWorkLogByCreateTime(createTimeStart, createTimeEnd int64) (list []*WorkLog, err error) {

	db := mysql.NewConn().Model(&WorkLog{})

	db = db.Where("created_at >= FROM_UNIXTIME(?) AND created_at < FROM_UNIXTIME(?)", createTimeStart, createTimeEnd)

	err = db.Find(&list).Error

	return
}

type WorkerInfo struct {
	WorkerId    int64  `json:"id"  gorm:"not null"` // 工人 id
	PhoneNumber string `json:"-"`                   // 手机号
	WorkerName  string `json:"name"`                // 工人姓名
}

// 获取时间范围内存在有效工作记录（金额大于0 & 有效工作记录）的员工列表
func (w *WorkLog) GetPerformanceWorkerList(startTime time.Time, endTime time.Time) (workerList []*WorkerInfo, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 只筛选有效的工作记录
	db = db.Where("last_operate = ?", LastOperateYes)

	// 时间筛选
	db = db.Where("created_at >= ?", startTime)
	db = db.Where("created_at < ?", endTime) // 不包括 endTime

	// 绩效金额大于0 | 罚款金额大于0
	db = db.Where("price > 0 OR fine_amount > 0")

	// 工人信息
	db = db.Select("worker_id, phone_number, worker_name")

	// 分组
	db = db.Group("worker_id")

	err = db.Find(&workerList).Error

	return
}

// WorkLogDetail 工作记录详细信息
type WorkLogDetail struct {
	CreatedAt              time.Time `json:"created_at"`
	WorkerId               int64     `json:"worker_id"  gorm:"not null"`                // 工人 id
	PhoneNumber            string    `json:"phone_number"`                              // 手机号
	WorkerName             string    `json:"worker_name"`                               // 工人姓名
	CodeType               int       `json:"code_type"`                                 // 二维码类型，1：打印，2：质检，3：发货，4：库存损耗，5：吊牌打印，6：库存损耗（吊牌），7：包装打印，8：库存损耗（包装）, 9: 投影仪前处理
	FaceCode               int       `json:"face_code"`                                 // 打印面码，如果是刺绣的话，就是第一个绣框
	CodeDesc               string    `json:"code_desc"`                                 // 发货，质检，正面-中部，目前用于记录衣服打印的是哪个面，例如，正面-左下
	MinusReason            int       `json:"minus_reason"`                              // 库存损耗原因
	FineAmount             float32   `json:"fine_amount"`                               // 人为损耗的罚款金额
	Price                  float32   `json:"price"`                                     // 单价
	CheckType              int       `json:"check_type" gorm:"default:0"`               // 质检分类 0-无 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	Total                  float32   `json:"total"`                                     // 绩效总金额
	TotalFine              float32   `json:"total_fine"`                                // 罚款金额
	Count                  int       `json:"count"`                                     // 完成的数量，分组时使用
	QualityInspectionCount int       `json:"quality_inspection_count" gorm:"default:0"` // 来料质检数量
	QualityCountTotal      int       `json:"quality_count_total"`                       // 来料质检总数量
}

// 获取工作记录描述，例如 打印（正面-顶部）
func (w *WorkLogDetail) GetFullCodeDesc() (fullCodeDesc string) {

	// 质检分类 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	if w.CheckType > 0 {
		fullCodeDesc = fmt.Sprintf("%s（%s）", CodeTypeDesc[w.CodeType], CheckTypeDesc[w.CheckType])
		return
	}

	if w.CodeType == CodeTypeMinus {
		//fullCodeDesc = MinusReasonDesc[w.MinusReason]
		fullCodeDesc = CodeTypeDesc[w.CodeType]
		return
	}

	// 来料质检
	if w.CodeType == CodeTypeInspectionQualityCheckout {
		fullCodeDesc = CodeTypeDesc[w.CodeType]
		return
	}

	fullCodeDesc = fmt.Sprintf("%s（%s）", CodeTypeDesc[w.CodeType], w.CodeDesc)

	return
}

// 统计工人绩效单
func (w *WorkLog) GetPerformanceList(startTime time.Time, endTime time.Time, idsInt []int) (list []*WorkLogDetail, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 只筛选有效的工作记录
	db = db.Where("last_operate = ?", LastOperateYes)

	// 时间筛选
	db = db.Where("created_at >= ?", startTime)
	db = db.Where("created_at < ?", endTime) // 不包括 endTime

	// 绩效金额大于0 | 罚款金额大于0
	db = db.Where("price > 0 OR fine_amount > 0")

	// 绩效信息
	db = db.Select("SUM(price) AS total, COUNT(price) AS count, SUM(fine_amount) AS total_fine, SUM(quality_inspection_count) AS quality_count_total, " +
		"created_at, worker_id, phone_number, worker_name, code_type, face_code, code_desc, check_type, minus_reason, price, fine_amount, quality_inspection_count")

	// 工人 id 筛选
	if len(idsInt) > 0 {
		db = db.Where("worker_id IN (?)", idsInt)
	}

	// 根据 code_type face_code check_type 进行分组，计算绩效总金额和罚款金额
	db = db.Group("worker_id, code_type, face_code, check_type, price").Order("total DESC, total_fine DESC")

	err = db.Find(&list).Error

	return
}

// 获取工作记录描述，例如 打印（正面-顶部）
func (w *WorkLog) GetFullCodeDesc() (fullCodeDesc string) {

	// 质检分类 1-单件-无包装 2-多件-无包装 3-单件-有包装 4-多件-有包装
	if w.CheckType > 0 {
		fullCodeDesc = fmt.Sprintf("%s（%s）", CodeTypeDesc[w.CodeType], CheckTypeDesc[w.CheckType])
		return
	}

	if w.CodeType == CodeTypeMinus {
		//fullCodeDesc = MinusReasonDesc[w.MinusReason]
		fullCodeDesc = CodeTypeDesc[w.CodeType]
		return
	}

	// 来料质检
	if w.CodeType == CodeTypeInspectionQualityCheckout {
		fullCodeDesc = CodeTypeDesc[w.CodeType]
		return
	}

	fullCodeDesc = fmt.Sprintf("%s（%s）", CodeTypeDesc[w.CodeType], w.CodeDesc)

	return
}

// GetFineDetail 根据工人 id 获取罚款明细
func (w *WorkLog) GetFineDetail(fineWorkerIds []int64, startTime, endTime time.Time) (list []*WorkLog, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 绩效金额大于0 | 罚款金额大于0
	db = db.Where("fine_amount > 0")

	// 时间筛选
	db = db.Where("created_at >= ?", startTime)
	db = db.Where("created_at < ?", endTime) // 不包括 endTime

	// 筛选有效的工作记录
	db = db.Where("last_operate = ?", LastOperateYes)

	// 工人 id 筛选
	db = db.Where("worker_id IN (?)", fineWorkerIds)

	err = db.Find(&list).Error

	return
}

// 判断是否已经存在工作记录
func (w *WorkLog) Exist() (exist bool, err error) {

	db := mysql.NewConn().Table(w.TableName())

	// 确保有筛选条件
	hasCondition := false

	// 是否有id
	if w.ID > 0 {
		db = db.Where("id = ?", w.ID)
		hasCondition = true
	}

	// 如果没有添加过筛选条件，则返回错误
	if !hasCondition {
		err = errors.New("no condition")
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}

	err = db.First(&WorkLog{}).Error

	if err == nil {
		exist = true // 存在
	} else if err == gorm.ErrRecordNotFound {
		err = nil
		exist = false // 不存在
	} else {
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
	}

	return
}
