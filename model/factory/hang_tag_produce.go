package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	. "zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	RegisterTable((*HangTagProduce)(nil), OPTION_USE_INNODB_ENGINE,
		OPTION_USE_UTF8mb4_CHARSET,
		OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*HangTagProduce)(nil))
}

const (
	ProduceStateNot    = 1  // 未生产
	ProduceStateYes    = 2  // 已生产
	ProduceStateCancel = 10 // 取消生产，目前用于一个生产任务里面的所有工单都取消的情况

	ProduceTypeSingle = 1 // 单面
	ProduceTypeDouble = 2 // 双面
)

const (
	ProducingStateProducing = 1 // 管理后台点击了下载图片，更改生产中
	ProducingStateComplete  = 2 // 管理后台点击了生产完成
)

type HangTagProduce struct {
	ID                 uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	Type               int       `json:"type" gorm:"default:1"`                 // 打印类型，1：单面，2：双面，默认1单面
	ProduceState       int       `json:"produce_state" gorm:"default:1"`        // 状态，1：未完成，2：已完成
	CompleteTime       int64     `json:"complete_time"`                         // 完成时间
	WorkCodes          string    `json:"work_codes"  gorm:"type:BLOB;"`         // 所有工单的工单id，一维数组
	HangTagItemsJson   string    `json:"hang_tag_item"  gorm:"type:BLOB;"`      // 对应每个工单的详细信息
	ProducingState     int       `json:"producing_state" gorm:"default:0"`      // 生产状态，1：正在生产中
	JumpTime           int64     `json:"jump_time"  gorm:"default:0"`           // 插队时间，时间戳
	LatestProduceState int64     `json:"latest_produce_state" gorm:"default:0"` // 最近一次生产的时间
	ProduceBit         uint64    `json:"produce_bit" gorm:"default:0"`          // 生产面，如果等于0的话说明已经生产完成了
	PriorityB          uint64    `json:"priority_b"  gorm:"default:0"`          // 优先分配到 B 区，优先级最高的排序，bit 位标识，也就是说可以标记 64 个优先级，并且越大，优先级越高，！！！逻辑搞反了，优先级越高，是越不要往B区分配
	FactoryId          int       `json:"factory_id" gorm:"default:1"`           // 工厂id
}

type HangTagProduceListResp struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Type         int       `json:"type" gorm:"default:1"`          // 打印类型，1：单面，2：双面，默认1单面
	ProduceState int       `json:"produce_state" gorm:"default:1"` // 状态，1：未完成，2：已完成
	CompleteTime int64     `json:"complete_time"`                  // 完成时间
	WorkCodes    string    `json:"work_codes"  gorm:"type:BLOB;"`  // 所有工单的工单id，一维数组
}

type HangTagProduceItem struct {
	Version              int    `json:"version" gorm:"default:0"` // 版本号，默认为0对应旧版数据，当前版本为1，后续如果适配新版数据，则版本号+1。 linxb 2024-03-22
	DeliveryCar          string `json:"delivery_car"`
	InventoryStorageName string `json:"inventory_storage_name"`
	DesignInfo           string `json:"design_info"`
	OrderCode            string `json:"order_code"`
	WorkCode             string `json:"work_code"`
	CName                string `json:"c_name"`
	HangTagInfoId        int64  `json:"hang_tag_info_id"`
	TagModel             string `json:"tag_model"`     // 吊牌的模特图
	State                int    `json:"state"`         // 0: 默认状态为正常状态（也为了适配旧数据），10：已取消
	SKU                  string `json:"sku"`           // sku信息，额外的筛选项
	OrderItemId          uint   `json:"order_item_id"` // 订单子项 id
}

func (h *HangTagProduce) TableName() string {
	return "hang_tag_produce"
}

// 新增
func (h *HangTagProduce) Create(tx ...*gorm.DB) error {
	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(h.TableName()).Create(h).Error
}

// 查询
func (h *HangTagProduce) GetList(pn, ps int, timeS, timeE int64, orderType int) (list []*HangTagProduceListResp, totalCount int, notProduceCount int, err error) {
	db := NewConn().Table(h.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	dbEmpty := db

	// id
	if h.ID > 0 {
		db = db.Where("id = ?", h.ID)
	}

	// 生成时间
	if timeS != 0 {
		db = db.Where("created_at >= FROM_UNIXTIME(?)", timeS)
	}
	if timeE != 0 {
		db = db.Where("created_at <= FROM_UNIXTIME(?)", timeE)
	}

	// 工单号
	if len(h.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+h.WorkCodes+"%")
	}

	// 打印类型，单面双面
	if h.Type > 0 {
		db = db.Where("type = ?", h.Type)
	}

	// 是否已经完成
	if h.ProduceState > 0 {
		db = db.Where("produce_state = ?", h.ProduceState)
	}

	err = db.Count(&totalCount).Error
	if err != nil {
		return
	}

	err = dbEmpty.Where("produce_state = ?", ProduceStateNot).Count(&notProduceCount).Error
	if err != nil {
		return
	}

	// 先根据类型排序（单面排在前面），然后根据 id 进行排序
	if orderType == 1 {
		err = db.Order("type asc,produce_state asc,id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	} else {
		err = db.Order("type asc,produce_state asc,id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	}

	// 先根据类型排序（单面排在前面），然后根据 id 进行排序，0：升序，1：降序
	if orderType == 1 { // 1：降序
		// 未生产的话，根据生成时间排序
		if h.ProduceState == 1 {
			err = db.Order("type asc,produce_state asc,id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		} else {
			// 已生产，就根据完成时间
			err = db.Order("type asc,produce_state asc,complete_time desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		}
	} else { // 0：升序
		// 未生产的话，根据生成时间排序
		if h.ProduceState == 1 {
			err = db.Order("type asc,produce_state asc,id asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		} else {
			// 已生产，就根据完成时间
			err = db.Order("type asc,produce_state asc,complete_time asc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
		}
	}

	return
}

func (h *HangTagProduce) Query() (list []*HangTagProduceListResp, err error) {
	db := NewConn().Table(h.TableName())
	if len(h.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+h.WorkCodes+"%")
	}
	if h.ID > 0 {
		db = db.Where("id = ?", h.ID)
	}
	if h.ProducingState > 0 {
		db = db.Where("producing_state = ?", h.ProducingState)
	}
	err = db.Find(&list).Error
	return
}

func (h *HangTagProduce) GetDetail() (err error) {
	db := NewConn().Table(h.TableName()).Where("id = ?", h.ID)
	return db.First(h).Error
}

func (h *HangTagProduce) GetNotProduce() (err error) {
	db := NewConn().Table(h.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).Order("type asc,id asc")
	if h.Type > 0 {
		db = db.Where("type = ?", h.Type)
	}
	return db.First(h).Error
}

func (h *HangTagProduce) GetNotProduceList() (list []*HangTagProduce, err error) {
	db := NewConn().Table(h.TableName()).Where("produce_state = ?", ProduceStateNot).Order("type asc,id desc")
	err = db.Find(&list).Error
	return
}

// 根据工单号来查，目前是用来删除已退款的工单的查询，所以这里要排除掉生产到一半的
func (h *HangTagProduce) GetListByWorkCode(workCodes []string) (list []*HangTagProduce, err error) {
	db := NewConn().Table(h.TableName())

	sqlStr := ""

	for _, oneWorkCode := range workCodes {
		if len(oneWorkCode) > 0 {
			//db = db.Where("work_codes LIKE ?", "%"+oneWorkCode+"%")
			if len(sqlStr) > 0 {
				sqlStr = fmt.Sprintf("%s OR work_codes LIKE '%s'", sqlStr, "%"+oneWorkCode+"%")
			} else {
				sqlStr = fmt.Sprintf("work_codes LIKE '%s'", "%"+oneWorkCode+"%")
			}
		}
	}

	if len(sqlStr) < 1 {
		return
	}

	db = db.Where(sqlStr)

	// 目前是用来删除已退款的工单的查询，所以这里要排除掉生产到一半的
	//db = db.Where("latest_produce_state < 10")

	err = db.Order("type asc,id desc").Find(&list).Error
	return
}

// 获取未生产的数量
func (h *HangTagProduce) GetNotProduceCount() (notProduceCount int64, err error) {
	db := NewConn().Table(h.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	err = db.Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).Count(&notProduceCount).Error
	return
}

func (h *HangTagProduce) QueryListByIds(idList []uint) (list []*HangTagProduce, err error) {
	db := NewConn().Table(h.TableName())
	if len(h.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+h.WorkCodes+"%")
	}
	if h.ProduceState > 0 {
		db = db.Where("produce_state = ?", h.ProduceState)
	}
	//if p.ID > 0 {
	//	db = db.Where("id = ?", p.ID)
	//}

	if len(idList) > 0 {
		db = db.Where("id IN (?)", idList)
	}

	if h.ProducingState > 0 {
		db = db.Where("producing_state = ?", h.ProducingState)
	}
	err = db.Find(&list).Error
	return
}

// 获取损耗展示页面的数据
func (h *HangTagProduce) QueryPreMinus(idList []uint) (list []*HangTagProduce, err error) {
	db := NewConn().Table(h.TableName())

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	//if p.ID > 0 {
	//	db = db.Where("id != ?", p.ID)
	//}

	if len(idList) > 0 {
		db = db.Where("id NOT IN (?)", idList)
	}

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	db = db.Limit(2).Order("latest_produce_state desc")

	err = db.Find(&list).Error
	return
}

func (h *HangTagProduce) QueryOne() (err error) {
	db := NewConn().Table(h.TableName())
	if len(h.WorkCodes) > 0 {
		db = db.Where("work_codes LIKE ?", "%"+h.WorkCodes+"%")
	}
	if h.ProduceState > 0 {
		db = db.Where("produce_state = ?", h.ProduceState)
	}
	if h.ID > 0 {
		db = db.Where("id = ?", h.ID)
	}
	if h.ProducingState > 0 {
		db = db.Where("producing_state = ?", h.ProducingState)
	}
	err = db.Order("id desc").First(h).Error
	return
}

// 更新，并且更新0
func (h *HangTagProduce) UpdatePrduceState(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["produce_state"] = h.ProduceState
	updateMap["updated_at"] = h.UpdatedAt
	updateMap["complete_time"] = h.CompleteTime
	updateMap["producing_state"] = h.ProducingState
	//updateMap["produce_bit"] = h.ProduceBit
	updateMap["latest_produce_state"] = h.LatestProduceState

	err = db.Table(h.TableName()).Where("id = ?", h.ID).Updates(updateMap).Error
	return
}

// 获取一个新的生产任务id，排除掉当前正在生产未完成的id（正在生产的有两个）
func (h *HangTagProduce) GetNotProduceExcludeId(id uint) (err error) {
	db := NewConn().Table(h.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b asc, jump_time desc,type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	if h.Type > 0 {
		db = db.Where("type = ?", h.Type)
	}
	db = db.Where("id != ?", id)
	return db.First(h).Error
}

// 完成一个包装吊牌生产任务后，获取一个新的任务，并根据 B 区优先级排序
func (h *HangTagProduce) GetNotProduceExcludeIdWithPriorityB(id uint) (err error) {
	db := NewConn().Table(h.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b desc, jump_time desc,type asc,id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	if h.Type > 0 {
		db = db.Where("type = ?", h.Type)
	}
	db = db.Where("id != ?", id)
	return db.First(h).Error
}

func (h *HangTagProduce) GetListByIds(ids []uint) (list []*HangTagProduce, err error) {
	db := NewConn().Table(h.TableName())

	db = db.Where("id IN (?)", ids)

	err = db.Order("type asc,id asc").Find(&list).Error
	return
}

func (h *HangTagProduce) GetProduceList(count int) (list []*HangTagProduce, err error) {

	db := NewConn().Table(h.TableName()).Where("produce_state = ?", ProduceStateNot).Where("producing_state != ?", ProducingStateProducing).
		Order("priority_b desc, jump_time desc, type asc, id asc")

	// 排除掉已取消的生产任务
	db = db.Where("produce_state != ?", ProduceStateCancel)

	if h.Type > 0 {
		db = db.Where("type = ?", h.Type)
	}

	if h.FactoryId > 0 {
		db = db.Where("factory_id = ?", h.FactoryId)
	}

	err = db.Limit(count).Find(&list).Error

	return
}

// 更新
func (h *HangTagProduce) Update(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&HangTagProduce{}).Where("id = ?", h.ID).Updates(h).First(h).Error
	return
}

func (h *HangTagProduce) UpdateByIds(ids []uint, tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&HangTagProduce{}).Where("id IN (?)", ids).Updates(h).First(h).Error
	return
}

func (c *HangTagProduce) GetDetailList(ids []uint) (interface{}, error) {

	db := NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*HangTagProduce, 0)

	err := db.Find(&list).Error

	return list, err
}

func (h *HangTagProduce) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&HangTagProduce{}).Where("id IN (?)", ids).Delete(h).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *HangTagProduce) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *HangTagProduce) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(HangTagProduce)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *HangTagProduce) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 删除
func (h *HangTagProduce) Delete(tx ...*gorm.DB) (err error) {

	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(&HangTagProduce{}).Where("id = ?", h.ID).Delete(h).Error
	return
}

// 取消生产
func (h *HangTagProduce) Cancel(tx ...*gorm.DB) (err error) {
	db := NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	updateMap := make(map[string]interface{})
	updateMap["produce_state"] = ProduceStateCancel
	updateMap["updated_at"] = time.Now()

	err = db.Model(&HangTagProduce{}).Where("id = ?", h.ID).Updates(updateMap).Error
	return
}
