package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*StorageSite)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*StorageSite)(nil))
}

// 库位表
type StorageSite struct {
	ID              uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	ProductName     string    `json:"product_name"`      // 商品名称 可编辑
	StorageName     string    `json:"storage_name"`      // 库位名称, spuid
	StorageNameEdit string    `json:"storage_name_edit"` // 库位名称. 可编辑
	ModelUrl        string    `json:"model_url"`         // 展示图 可编辑
	PurchaseID      uint      `json:"purchase_id"`       // 采购工厂ID
	OwnPurchaseID   string    `json:"own_purchase_id"`   // 自有采购工厂ID
	ProductCName    string    `json:"product_c_name"`    // 商品中文名

	ColorIdList string `json:"color_id_list"` //选择的颜色id 使用|分割
	SizeIdList  string `json:"size_id_list"`  //选择的尺码ID

	Enable int     `json:"enable"` // 0 1 未启用，2启用   多工厂配置
	Cost   float64 `json:"cost"`   // 成本，目前用来工厂损耗时进行成本展示，linxb 2025年3月10日17:22:13

	PurchaseFactory model.PurchaseFactory `json:"purchase_factory" gorm:"foreignkey:id;association_foreignkey:purchase_id"`
}

func (s *StorageSite) TableName() string {
	return "storage_site"
}

// 新增
func (s *StorageSite) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&StorageSite{}).Create(s).Error
}

// 更新
func (s *StorageSite) Update(updateMap map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()

	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&StorageSite{}).Where("id = ?", s.ID).Updates(updateMap).Error
}

func (s *StorageSite) GetList(pn, ps int, idList ...[]uint) (list []*StorageSite, count int, err error) {

	db := mysql.NewConn().Table(s.TableName()).Preload("PurchaseFactory")

	if len(idList) != 0 {
		db = db.Where("id IN (?)", idList[0])
	}

	if s.StorageName != "" {
		db = db.Where("storage_name like ?", "%"+s.StorageName+"%")
	}

	if s.StorageNameEdit != "" {
		db = db.Where("storage_name_edit like ?", "%"+s.StorageNameEdit+"%")
	}

	if s.ProductName != "" {
		db = db.Where("product_name like ?", "%"+s.ProductName+"%")
	}
	if s.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", s.PurchaseID)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Offset((pn - 1) * ps).Limit(ps).Find(&list).Error
	return
}

func (s *StorageSite) Query() (err error) {
	db := mysql.NewConn().Table(s.TableName()).Preload("PurchaseFactory")
	if s.ID != 0 {
		db = db.Where("id = ?", s.ID)
	}
	return db.First(s).Error

}

// GetDetail
func (s *StorageSite) GetDetail() (err error) {
	db := mysql.NewConn().Table(s.TableName())
	if s.ID != 0 {
		db = db.Where("id = ?", s.ID)
	}
	return db.First(s).Error
}

func (c *StorageSite) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*StorageSite, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *StorageSite) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&StorageSite{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *StorageSite) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *StorageSite) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(StorageSite)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *StorageSite) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 根据 spu 查询库位
func (s *StorageSite) QueryBySpu(spu string) (err error) {

	db := mysql.NewConn().Table(s.TableName())

	// 根据 spu 进行模糊查询
	db = db.Where("storage_name like ?", "%"+spu+"%")

	err = db.First(s).Error

	return
}
