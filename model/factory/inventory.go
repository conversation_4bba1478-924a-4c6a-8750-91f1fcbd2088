package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

const (
	_ = iota
	UPDATE_INVENTORY_ADD
	UPDATE_INVENTORY_REDUCE
	UPDATE_INVENTORY_THRESHOLD
	UPDATE_INVENTORY_NAME
)

func init() {
	mysql.RegisterTable((*Inventory)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*Inventory)(nil))

}

// 具体的库存表
type Inventory struct {
	ID                   uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	StorageID            uint      `json:"storage_id"`
	ColorID              string    `json:"color_id"`               //颜色id
	ColorName            string    `json:"color_name"`             //颜色名称
	SizeID               string    `json:"size_id"`                //尺码id
	SizeName             string    `json:"size_name"`              //尺码名称
	Location             string    `json:"location"`               //颜色ID+尺码ID ，sku的后6位
	InventoryStorageName string    `json:"inventory_storage_name"` //库存的库位名称

	FactoryID   uint   `json:"factory_id"`   // 工厂ID
	Enable      int    `json:"enable"`       // 0 1 未启用，2启用
	FactoryName string `json:"factory_name"` // 工厂名称 预留

	Quality   int `json:"quality"`   //库存
	Threshold int `json:"threshold"` //预警值

	TotalSales      int     `json:"total_sales"`                     // 历史总销量
	FifteenDaySales int     `json:"fifteen_day_sales"`               // 15天总销量
	AverageSales    float64 `json:"average_sales"`                   // 15天平均销量
	FifteenCount    string  `json:"fifteen_count" gorm:"type:BLOB;"` // 15天 json,数组
	TodaySales      int     `json:"today_sales"`                     // 今日销量

	StorageSite   StorageSite   `json:"storage_site"  gorm:"foreignkey:id;association_foreignkey:storage_id"`
	InventoryRoam InventoryRoam `json:"-"  gorm:"foreignkey:location;association_foreignkey:location"`
}

type SidList struct {
	StorageID uint `json:"storage_id"`
}

func (i *Inventory) TableName() string {
	return "inventory"
}

// 新增
func (i *Inventory) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(i.TableName()).Create(i).Error
}

func (i *Inventory) GetListBySid() (list []*Inventory, err error) {
	err = mysql.NewConn().Table(i.TableName()).Preload("InventoryRoam").Where("storage_id = ?", i.StorageID).Find(&list).Error
	return
}

// GetListByColorID
func (i *Inventory) GetListByColorID() (list []*Inventory, err error) {
	err = mysql.NewConn().Table(i.TableName()).Preload("InventoryRoam").Where("color_id = ?", i.ColorID).Find(&list).Error
	return
}

// 根据 location 查找
func (i *Inventory) Query() error {
	db := mysql.NewConn().Table(i.TableName()).Preload("StorageSite").Preload("StorageSite.PurchaseFactory")
	if len(i.Location) > 0 {
		db = db.Where("location = ?", i.Location)
	}
	if i.ColorName != "" {
		db = db.Where("color_name = ?", i.ColorName)
	}
	if i.SizeName != "" {
		db = db.Where("size_name = ?", i.SizeName)
	}
	if i.ColorID != "" {
		db = db.Where("color_id = ?", i.ColorID)
	}

	return db.First(i).Error
}

// 根据 location 查找 List
func (i *Inventory) QueryList() (list []*Inventory, err error) {
	err = mysql.NewConn().Table(i.TableName()).Where("location = ?", i.Location).Find(&list).Error
	return
}

func (i *Inventory) UpdateQualityTest(num int, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Inventory{}).Where("location = ?", i.Location).Update("quality", num).Error
	return
}

func (i *Inventory) updateStorageName(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Inventory{}).Where("location = ?", i.Location).Update("inventory_storage_name", i.InventoryStorageName).Error
	return
}

// 更改sku库存  status = 1 加库存  2 减库存  3 更新预警值
func (i *Inventory) UpdateSkuQuality(status, num int, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&Inventory{}).Where("location = ?", i.Location)
	if i.FactoryID != 0 {
		db = db.Where("factory_id = ?", i.FactoryID)
	}

	if status == UPDATE_INVENTORY_ADD {
		err := db.UpdateColumn("quality", gorm.Expr("quality + ?", num)).Error
		return err
	}
	if status == UPDATE_INVENTORY_REDUCE {
		err := db.UpdateColumn("quality", gorm.Expr("quality - ?", num)).Error
		return err
	}
	//更新告警阈值
	if status == UPDATE_INVENTORY_THRESHOLD {
		err := db.UpdateColumn("threshold", num).Error
		return err
	}
	if status == UPDATE_INVENTORY_NAME {
		err := db.UpdateColumn("inventory_storage_name", i.InventoryStorageName).Error
		return err
	}

	return nil
}

// 减库存操作
func (i *Inventory) MinusSkuQuality() error {

	db := mysql.NewConn().Model(&Inventory{})

	db = db.Where("factory_id = ?", i.FactoryID)

	return db.Where("location = ?", i.Location).
		UpdateColumn("quality", gorm.Expr("quality - ?", 1)).
		First(i).Error
}

// 减库存操作 传参方式
func (i *Inventory) MinusSkuQualityCount(count int) error {

	db := mysql.NewConn().Model(&Inventory{}).Where("location = ?", i.Location)

	if i.FactoryID != 0 {
		db = db.Where("factory_id = ?", i.FactoryID)
	}

	return db.UpdateColumn("quality", gorm.Expr("quality - ?", count)).UpdateColumn("today_sales", gorm.Expr("today_sales + ?", count)).
		First(i).Error
}

// 加库存操作 传参方式
func (i *Inventory) AddSkuQualityCount(count int) error {
	db := mysql.NewConn().Model(&Inventory{})
	db = db.Where("factory_id = ?", i.FactoryID)
	return db.Where("location = ?", i.Location).
		UpdateColumn("quality", gorm.Expr("quality + ?", count)).
		First(i).Error
}

func (i *Inventory) IsExists(colorList []string) (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(i.TableName()).Where("color_id IN (?)", colorList).Count(&count)
	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil
}

// 筛选查找SPU List
func (i *Inventory) GetSIDList() (list []*SidList, err error) {
	err = mysql.NewConn().Table(i.TableName()).Select("storage_id").Group("storage_id").Where("quality < ?", i.Quality).Find(&list).Error
	return
}

func (i *Inventory) GetListBySku() (list []*Inventory, err error) {
	err = mysql.NewConn().Table(i.TableName()).Where("color_id = ?", i.ColorID).Where("size_id = ?", i.SizeID).Find(&list).Error
	return
}

func (i *Inventory) GetInventoryByLocation() (err error) {
	err = mysql.NewConn().Table(i.TableName()).Where("location = ?", i.Location).Preload("StorageSite").Preload("StorageSite.PurchaseFactory").First(i).Error
	return
}

// 不包含Preload 的查询
func (i *Inventory) GetInventoryByLocationOn() (err error) {
	db := mysql.NewConn().Table(i.TableName()).Where("location = ?", i.Location)
	if i.FactoryID != 0 {
		db = db.Where("factory_id = ?", i.FactoryID)
	}
	err = db.First(i).Error
	return
}

// 根据 location list 获取相关的库存
func (i *Inventory) GetListByLocationList(locationList []string) (list []*Inventory, err error) {
	db := mysql.NewConn().Table(i.TableName()).Where("location IN (?)", locationList)
	if i.FactoryID != 0 {
		db = db.Where("factory_id = ?", i.FactoryID)
	}
	err = db.Find(&list).Error
	return
}

func (i *Inventory) GetInventoryList(pn, ps int) (list []*Inventory, count int, err error) {

	db := mysql.NewConn().Table(i.TableName()).Where("quality < ?", i.Quality).Preload("StorageSite").Preload("StorageSite.PurchaseFactory")

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (i *Inventory) GetAllList() (list []*Inventory, err error) {
	err = mysql.NewConn().Table(i.TableName()).Find(&list).Error
	return
}

func (i *Inventory) PutSalesInfo() (err error) {

	err = mysql.NewConn().Model(&Inventory{}).Where("id = ?", i.ID).Updates(map[string]interface{}{
		"total_sales":       i.TotalSales,
		"fifteen_day_sales": i.FifteenDaySales,
		"average_sales":     i.AverageSales,
		"fifteen_count":     i.FifteenCount,
		"today_sales":       i.TodaySales,
	}).Error

	return
}

func (i *Inventory) GetListByColorName() (list []*Inventory, err error) {
	db := mysql.NewConn().Table(i.TableName()).Preload("StorageSite")
	err = db.Where("color_name = ?", i.ColorName).Find(&list).Error
	return
}

func (c *Inventory) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Inventory, 0)

	err := db.Find(&list).Error

	return list, err
}

func (i *Inventory) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&Inventory{}).Where("id IN (?)", ids).Delete(i).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Inventory) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Inventory) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Inventory)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Inventory) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

type ColorItem struct {
	ColorID   string `json:"color_id"`   //颜色id
	ColorName string `json:"color_name"` //颜色名称
}

// GetColorID
func (i *Inventory) GetColorID() (colorList []*ColorItem, err error) {
	// 将方法改为查询 location count > 1的数据，即有重复的库存，然后返回重复库存中去重后颜色ID
	db := mysql.NewConn().
		Table(i.TableName())
	if i.ColorName != "" {
		db = db.Where("color_name like ?", "%"+i.ColorName+"%")
	}

	err = db.
		Select("color_id, color_name").
		Group("color_id").
		Having("count(distinct factory_id) > 1").
		Find(&colorList).Error
	return
}

type SizeItem struct {
	SizeID   string `json:"size_id"`   //尺码id
	SizeName string `json:"size_name"` //尺码名称
}

// get Size By colorID
func (i *Inventory) GetSizeByColorID(colorID string) (sizeList []*SizeItem, err error) {
	err = mysql.NewConn().
		Table(i.TableName()).
		Select("size_id, size_name").
		Where("color_id = ?", colorID).
		Group("size_id, size_name").
		Find(&sizeList).Error
	return
}

// QueryListByLocation
func (i *Inventory) QueryListByLocation(locations []string) (list []*InventoryRedItem, err error) {
	err = mysql.NewConn().Table(i.TableName()).Where("location IN (?)", locations).Preload("InventoryRoam").Find(&list).Error
	return
}

type InventoryRedItem struct {
	ID                   uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	StorageID            uint      `json:"storage_id"`
	ColorID              string    `json:"color_id"`               //颜色id
	ColorName            string    `json:"color_name"`             //颜色名称
	SizeID               string    `json:"size_id"`                //尺码id
	SizeName             string    `json:"size_name"`              //尺码名称
	Location             string    `json:"location"`               //颜色ID+尺码ID ，sku的后6位
	InventoryStorageName string    `json:"inventory_storage_name"` //库存的库位名称

	FactoryID   uint   `json:"factory_id"`   // 工厂ID
	Enable      int    `json:"enable"`       // 0 1 未启用，2启用
	FactoryName string `json:"factory_name"` // 工厂名称 预留

	Quality   int `json:"quality"`   //库存
	Threshold int `json:"threshold"` //预警值

	TotalSales      int     `json:"total_sales"`                     // 历史总销量
	FifteenDaySales int     `json:"fifteen_day_sales"`               // 15天总销量
	AverageSales    float64 `json:"average_sales"`                   // 15天平均销量
	FifteenCount    string  `json:"fifteen_count" gorm:"type:BLOB;"` // 15天 json,数组
	TodaySales      int     `json:"today_sales"`                     // 今日销量

	StorageSite   StorageSite   `json:"storage_site"  gorm:"foreignkey:id;association_foreignkey:storage_id"`
	InventoryRoam InventoryRoam `json:"-"  gorm:"foreignkey:location;association_foreignkey:location"`

	Red    int `json:"red"`
	Yellow int `json:"yellow"`
	Green  int `json:"green"`
}
