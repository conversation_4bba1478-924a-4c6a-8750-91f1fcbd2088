package design

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ProductPiece)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ProductPiece)(nil))
}

type ProductPiece struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	ProductID    uint      `json:"product_id"`    //对应商品id
	Type         int       `json:"type"`          // 0/1DTG 2 EMB
	SurfaceName  string    `json:"surface_name"`  // 对应线框图名称 （如：正面）
	CanvasName   string    `json:"canvas_name"`   // 画板名称 （如：正面-左下）
	MaskImage    string    `json:"mask_image"`    // 图片URL （同一个surface_name 图片一致，不用重复上传）
	OuterWidth   int       `json:"outer_width"`   // 容器宽度
	OuterHeight  int       `json:"outer_height"`  // 容器高度
	CanvasLeft   int       `json:"canvas_left"`   // 画板相对容器的左边的位置
	CanvasTop    int       `json:"canvas_top"`    // 画板相对容器的左边的位置
	CanvasWidth  int       `json:"canvas_width"`  // 画板的宽度
	CanvasHeight int       `json:"canvas_height"` // 画板的高度
	RealWidth    int       `json:"real_width"`    // 真实宽
	RealHeight   int       `json:"real_height"`   // 真实高
	CanvasZoom   float32   `json:"canvas_zoom"`   // 缩放系数
	EmbZoom      float32   `json:"emb_zoom"`      // 绣框缩放系数 type!=2 时忽略该参数
	CanvasRotate int       `json:"canvas_rotate"` // 旋转系数
	Price        int       `json:"price"`         // 价格

	Layers          string `json:"layers" gorm:"type:BLOB;"`           // 具体的设计的图层信息
	Platens         string `json:"platens"`                            // 具体的绣框信息
	OptionalPlatens string `json:"optional_platens" gorm:"type:BLOB;"` // 配置的可选绣框 [{id:1,name:"16*20"}]
	// 保存相关字段
	IsApplied int `json:"is_applied"  gorm:"default:1"`
	// 1 0(兼容旧) 代表已经应用到pod端，2 代表有修改未应用到pod端 ,3 代表新增的数据还未应用过，pod端不需要获取 4 代表删除未应用，在后台不显示，pod端正常获取
	// 这里详细解释下，pod端获取的时候，除了is_applied =3 外都获取ProductDesign的正常数据。is_applied =3 过滤数据不返回
	// 在管理后台获取的时候，如果is_applied为1，则获取上面的数据，如果is_applied为2、3，则序列化save_product_design的数据返回
	// 任何修改编辑操作，都会将is_applied 设置为对应的值，新的内容保存在save_product_design中
	// 点击同步（应用）的时候，将save_product_design的数据同步到ProductDesign中，然后将is_applied设置为1,如果是删除就直接删掉此条数据
	SaveProductPiece string `json:"save_product_piece"   gorm:"type:BLOB;"`
}

var PiecePrice = map[string]int{
	"Front Side - Middle":          399,
	"Front Side - Top":             399,
	"Front Side - Bottom":          399,
	"Front Side - Lower Left":      399,
	"Front Side - Lower Right":     399,
	"Front Side - Shoulder Left":   199,
	"Front Side - Shoulder Right":  199,
	"Front Side - Pocket":          299,
	"Back Side - Middle":           399,
	"Back Side - Top":              399,
	"Back Side - Bottom":           399,
	"Back Side - Lower Left":       399,
	"Back Side - Lower Right":      399,
	"Back Side - Neck Label Outer": 199,
	"Sleeve Left":                  199,
	"Sleeve Right":                 199,
	"Long Sleeve Left":             299,
	"Long Sleeve Right":            299,
	"Neck Label Inner":             199,
	// 2022-11-22 18:19:40 新增裤子台板价格
	"Front Side - Left Leg Center":  299,
	"Front Side - Left Leg Top":     299,
	"Front Side - Left Leg Bottom":  299,
	"Front Side - Right Leg Center": 299,
	"Front Side - Right Leg Top":    299,
	"Front Side - Right Leg Bottom": 299,

	"Back Side - Left Leg Center":  299,
	"Back Side - Left Leg Top":     299,
	"Back Side - Left Leg Bottom":  299,
	"Back Side - Right Leg Center": 299,
	"Back Side - Right Leg Top":    299,
	"Back Side - Right Leg Bottom": 299,
	"Back Side - Left Pocket":      199,
	"Back Side - Right Pocket":     199,

	"Front Side - Left Leg Pocket":  299,
	"Front Side - Right Leg Pocket": 299,
}

func (p *ProductPiece) TableName() string {
	return "product_piece"
}

func (p *ProductPiece) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

func (p *ProductPiece) GetListByPID(productID uint) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", productID).Find(&list).Error
	return
}

func (p *ProductPiece) GetListByPIDNoNew(productID uint) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", productID).Where("is_applied != 3").Find(&list).Error
	return
}

func (p *ProductPiece) GetListByPIDNoNewAndEmb(productID uint) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", productID).Where("is_applied != 3").Where("type = 2").Find(&list).Error
	return
}

func (p *ProductPiece) GetListByIsApplied(productID uint) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", productID).Where("is_applied != 4").Find(&list).Error
	return
}

// 根据 pid 查找未被应用的 piece
func (p *ProductPiece) GetListByNoApplied(productID uint, isApplied int) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", productID).Where("is_applied != 1", isApplied).Find(&list).Error
	return
}

// 更新
func (p *ProductPiece) UpdatePiece(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(p).Where("id = ?", p.ID).Where("product_id = ?", p.ProductID).Updates(map[string]interface{}{
		"surface_name":       p.SurfaceName,
		"canvas_name":        p.CanvasName,
		"mask_image":         p.MaskImage,
		"outer_width":        p.OuterWidth,
		"outer_height":       p.OuterHeight,
		"canvas_left":        p.CanvasLeft,
		"canvas_top":         p.CanvasTop,
		"canvas_width":       p.CanvasWidth,
		"canvas_height":      p.CanvasHeight,
		"real_width":         p.RealWidth,
		"real_height":        p.RealHeight,
		"canvas_zoom":        p.CanvasZoom,
		"canvas_rotate":      p.CanvasRotate,
		"price":              p.Price,
		"is_applied":         p.IsApplied,
		"save_product_piece": p.SaveProductPiece,
		"type":               p.Type,
		"optional_platens":   p.OptionalPlatens,
		"emb_zoom":           p.EmbZoom,
	})

	return db.Error
}

// 根据 productID 和 id,更新指定信息
func (p *ProductPiece) UpdatePieceByID(data map[string]interface{}, tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(&ProductPiece{}).Where("product_id = ?", p.ProductID)
	return db.Where("id = ?", p.ID).Updates(data).Error
}

func (p *ProductPiece) DeleteModel(idList []int) error {
	db := mysql.NewConn().Table(p.TableName()).Where("product_id = ?", p.ProductID)
	return db.Where("id IN (?)", idList).Delete(&ProductPiece{}).Error
}

// 根据 productID 和 idList，编辑信息
func (p *ProductPiece) UpdatePieceByIDList(idList []int, data map[string]interface{}) error {
	db := mysql.NewConn().Model(&ProductPiece{}).Where("product_id = ?", p.ProductID)
	return db.Where("id IN (?)", idList).Updates(data).Error
}

// 根据productID 和 idList，查询列表信息
func (p *ProductPiece) GetListByPIDAndIDList(idList []int) (list []*ProductPiece, err error) {
	err = mysql.NewConn().Table(p.TableName()).Where("product_id = ?", p.ProductID).Where("id IN (?)", idList).Find(&list).Error
	return
}

func (p *ProductPiece) Delete() error {
	return mysql.NewConn().Table(p.TableName()).Where("product_id = ?", p.ProductID).Delete(p).Error
}

func (p *ProductPiece) DeleteByID(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ProductPiece{}).Where("id = ?", p.ID).Delete(p).Error
	return
}

func (p *ProductPiece) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(p.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ProductPiece, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *ProductPiece) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&ProductPiece{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (p *ProductPiece) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(p.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", p.ID).Updates(p).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", p.ID).Update("updated_at", p.UpdatedAt).Error

	return
}

func (p *ProductPiece) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ProductPiece)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (p *ProductPiece) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(p.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
