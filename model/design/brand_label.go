package design

import (
	"github.com/jinzhu/gorm"
	"time"
	"zx/unit/pkg/mysql"
)

func init() {
	mysql.RegisterTable((*BrandLabel)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	//model.SyncRegister((*BrandLabel)(nil))
}

const (
	BRAND_DRAFT = 1
	BRAND_OK    = 2
)

type BrandLabel struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	UserID    uint      `json:"user_id"`                //所属用户
	State     int       `json:"state" gorm:"default:1"` //状态，默认 1：草稿  2：正常
	Title     string    `json:"title"`                  //品牌领标名称

	DarkDesign  string `json:"dark_design" gorm:"type:BLOB;"`  //深色领标设计信息
	LightDesign string `json:"light_design" gorm:"type:BLOB;"` //浅色领标设计信息
}

//设计信息序列化
type BrandDesignJson struct {
	SurfaceName  string  `json:"surface_name"`  //对应可打印面名称
	CanvasName   string  `json:"canvas_name"`   //画板名称
	MaskImage    string  `json:"mask_image"`    //图片URL
	OuterWidth   int     `json:"outer_width"`   //容器宽度
	OuterHeight  int     `json:"outer_height"`  //容器高度
	CanvasLeft   int     `json:"canvas_left"`   // 画板相对容器的左边的位置
	CanvasTop    int     `json:"canvas_top"`    // 画板相对容器的左边的位置
	CanvasWidth  int     `json:"canvas_width"`  // 画板的宽度
	CanvasHeight int     `json:"canvas_height"` // 画板的高度
	RealWidth    int     `json:"real_width"`    //真实宽
	RealHeight   int     `json:"real_height"`   //真实高
	OutputImg    string  `json:"output_img"`    //输出素材图
	CanvasZoom   float32 `json:"canvas_zoom"`   //缩放系数
	CanvasRotate int     `json:"canvas_rotate"` //旋转系数
	Price        int     `json:"price"`         //价格
	Layers       string  `json:"layers"`        //具体的设计的图层信息
}

func (b *BrandLabel) TableName() string {
	return "brand_label"
}

func (b *BrandLabel) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(b.TableName()).Create(b).Error
}

func (b *BrandLabel) UpdatesByID(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(&BrandLabel{}).Where("id = ?", b.ID).Updates(b).First(b).Error
}

func (b *BrandLabel) GetListByUserID() (list []*BrandLabel, err error) {

	db := mysql.NewConn().Table(b.TableName()).Where("user_id = ?", b.UserID)

	err = db.Find(&list).Error

	return
}

func (b *BrandLabel) GetOneByID() error {
	db := mysql.NewConn().Table(b.TableName()).Where("id = ?", b.ID)
	if b.UserID != 0 {
		db = db.Where("user_id = ?", b.UserID)
	}
	return db.First(b).Error
}

func (b *BrandLabel) CountByUID() (count int, err error) {
	err = mysql.NewConn().Table(b.TableName()).Where("user_id = ?", b.UserID).Count(&count).Error
	return
}

func (b *BrandLabel) DeleteByID() error {
	return mysql.NewConn().Table(b.TableName()).Where("id = ?", b.ID).Delete(b).Error
}
