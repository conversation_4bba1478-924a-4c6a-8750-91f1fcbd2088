package design

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/mysql"
	"zx/zxgo/log"

	"gorm.io/gorm"
)

// 在package和import后添加注册
func init() {
	mysql.RegisterTable((*PackagingInfoV2)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET, mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
}

// PackagingInfoV2 包装信息表 - GORM v2版本
type PackagingInfoV2 struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 基础信息
	PackID              string `json:"pack_id" gorm:"unique;not null"` // 包装ID 类似 N001
	Name                string `json:"name"`                           // 包装名称
	State               int    `json:"state" gorm:"default:1"`         // 1 待上架 2 上架 3 淘汰
	ProduceFactoryID    uint   `json:"produce_factory_id"`             // 生产工厂id
	PurchaseFactoryID   uint   `json:"purchase_factory_id"`            // 采购工厂id
	PurchaseFactoryName string `json:"purchase_factory_name"`          // 采购工厂名
	ProduceFactoryName  string `json:"produce_factory_name"`           // 生产工厂名
	Description         string `json:"description" gorm:"type:BLOB"`   // 整体描述
	Price               int    `json:"price"`                          // 包装定价 美分
	SingleSided         bool   `json:"single_sided"`                   // 是否单面

	// 与库存相关的字段
	Cname                string `json:"cname"`                    // 包装中文名
	InventoryStorageName string `json:"inventory_storage_name"`   // 库存的库位名称
	Quality              int    `json:"quality" gorm:"default:0"` // 库存
	Threshold            int    `json:"threshold"`                // 预警值

	// 与设计相关
	DesignPiece string `json:"design_piece" gorm:"type:BLOB"` // 设计区信息
	Models      string `json:"models" gorm:"type:BLOB"`       // 模特展示图
	ModelImg    string `json:"model_img" gorm:"type:BLOB"`    // 模特图片
}

func (p *PackagingInfoV2) TableName() string {
	return "packaging_info"
}

// 统一数据库接口方法
func (p *PackagingInfoV2) Create(db mysql.DBInterface) error {
	return db.Create(p).Error()
}

func (p *PackagingInfoV2) Query(db mysql.DBInterface) error {
	return db.Where(p).First(p).Error()
}

func (p *PackagingInfoV2) GetList(db mysql.DBInterface, pn, ps int, conditions map[string]interface{}) ([]*PackagingInfoV2, int64, error) {
	var list []*PackagingInfoV2
	var count int64

	query := db.Model(&PackagingInfoV2{})
	for key, value := range conditions {
		if key == "name_like" {
			query = query.Where("name LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		} else {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err()
	}

	offset := (pn - 1) * ps
	if err := query.Offset(offset).Limit(ps).Order("id desc").Find(&list).Error; err != nil {
		return nil, 0, err()
	}

	return list, count, nil
}

func (p *PackagingInfoV2) Update(db mysql.DBInterface) error {
	return db.Save(p).Error()
}

func (p *PackagingInfoV2) UpdateWithZero(db mysql.DBInterface) error {
	return db.Select("*").Updates(p).Error()
}

func (p *PackagingInfoV2) Delete(db mysql.DBInterface) error {
	return db.Delete(p).Error()
}

// 根据PackID查询
func (p *PackagingInfoV2) QueryByPackID(db mysql.DBInterface, packID string) error {
	return db.Where("pack_id = ?", packID).First(p).Error()
}

// 减少库存
func (p *PackagingInfoV2) MinusQuality(db mysql.DBInterface, count int) error {
	return db.Model(p).Update("quality", gorm.Expr("quality - ?", count)).Error()
}

// 恢复库存
func (p *PackagingInfoV2) RecoverQuality(db mysql.DBInterface, count int) error {
	return db.Model(p).Update("quality", gorm.Expr("quality + ?", count)).Error()
}

// 创建或更新 - 工厂同步
func (p *PackagingInfoV2) CreateOrUpdate(detail interface{}, idMapType map[uint]int) error {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return err
	}

	detailObj := new(PackagingInfoV2)
	if err := json.Unmarshal(detailData, detailObj); err != nil {
		log.Error(err)
		return err
	}

	db := mysql.NewUnifiedDB()
	var existingRecord PackagingInfoV2
	err = db.Where("id = ?", detailObj.ID).First(&existingRecord).Error()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(err)
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		if err := db.Create(detailObj).Error; err != nil {
			log.Error("创建PackagingInfo失败: ", err)
			return err()
		}
		log.Info("创建PackagingInfo成功: ", detailObj.ID)
	} else {
		if err := db.Save(detailObj).Error; err != nil {
			log.Error("更新PackagingInfo失败: ", err)
			return err()
		}
		log.Info("更新PackagingInfo成功: ", detailObj.ID)
	}

	return nil
}
