package design

import (
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/json"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*HangTagInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*HangTagInfo)(nil))
}

// 应用于pod平台的可设计吊牌款式基础信息 表
type HangTagInfo struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	//基础信息
	TagID               string `json:"tag_id" gorm:"unique;not null"` //吊牌ID  类似 S01
	Name                string `json:"name"`                          //吊牌名称
	State               int    `json:"state"  gorm:"default:1"`       // 1 待上架 2 上架  3 淘汰
	ProduceFactoryID    uint   `json:"produce_factory_id"`            //生产工厂id
	PurchaseFactoryID   uint   `json:"purchase_factory_id"`           //采购工厂id
	PurchaseFactoryName string `json:"purchase_factory_name"`         //采购工厂名
	ProduceFactoryName  string `json:"produce_factory_name"`          //生产工厂名
	Description         string `json:"description" gorm:"type:BLOB;"` //整体描述
	Price               int    `json:"price"`                         // 吊牌定价 美分
	// 与库存相关的字段
	Cname                string `json:"cname"`                    //吊牌中文名
	InventoryStorageName string `json:"inventory_storage_name"`   //库存的库位名称
	Quality              int    `json:"quality" gorm:"default:0"` //库存
	Threshold            int    `json:"threshold"`                //预警值
	//与设计相关
	DesignPiece string `json:"design_piece" gorm:"type:BLOB;"` // 设计区信息,预置写死 类型[]DesignPiece
	DesignModel string `json:"design_model" gorm:"type:BLOB;"` // 模特图片信息,除图片路径与对应设计区外信息写死 类型 []DesignModel
}

// DesignPiece 的详细数据
type DesignPiece struct {
	ID           int     `json:"id"`
	SurfaceName  string  `json:"surface_name"`  //对应线框图名称 （如：正面）
	OutputImg    string  `json:"output_img"`    // 输出图片
	CanvasName   string  `json:"canvas_name"`   //画板名称 （如：正面-左下）
	MaskImage    string  `json:"mask_image"`    //线框图图片URL
	OuterWidth   int     `json:"outer_width"`   //容器宽度
	OuterHeight  int     `json:"outer_height"`  //容器高度
	CanvasLeft   int     `json:"canvas_left"`   // 画板相对容器的左边的位置
	CanvasTop    int     `json:"canvas_top"`    // 画板相对容器的左边的位置
	CanvasWidth  int     `json:"canvas_width"`  // 画板的宽度
	CanvasHeight int     `json:"canvas_height"` // 画板的高度
	RealWidth    int     `json:"real_width"`    //真实宽
	RealHeight   int     `json:"real_height"`   //真实高
	CanvasZoom   float32 `json:"canvas_zoom"`   //缩放系数
	CanvasRotate int     `json:"canvas_rotate"` //旋转系数
	Price        int     `json:"price"`         //价格

	Layers string `json:"layers" gorm:"type:BLOB;"` //具体的设计的图层信息
}

// DesignModel 的详细数据
type DesignModel struct {
	ID          int     `json:"id"`
	SurfaceName string  `json:"surface_name"` //对应线框图、设计区名称 （如：正面）
	Name        string  `json:"name"`         //模特名称
	MaskImage   string  `json:"mask_image"`   //模特图片
	OuterWidth  int     `json:"outer_width"`  //容器宽度
	OuterHeight int     `json:"outer_height"` //容器高度
	MaskColor   string  `json:"mask_color"`   //遮罩颜色，默认为空字符  用来通过目标色计算底色
	MaskOpacity float32 `json:"mask_opacity"` //遮罩透明度，默认为0   用来通过目标色计算底色
	MappingTop  bool    `json:"mapping_top"`  // 是否将设计图置于模特最上册
	Mapping     string  `json:"mapping"`      //映射关系 数组，序列化存储 类型[]MappingJson
}

func (h *HangTagInfo) TableName() string {
	return "hangtag_info"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (h *HangTagInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(h.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (h *HangTagInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(HangTagInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (h *HangTagInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(h.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", h.ID).Updates(h).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", h.ID).Update("updated_at", h.UpdatedAt).Error

	return
}

func (h *HangTagInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(h.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*HangTagInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (h *HangTagInfo) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&HangTagInfo{}).Where("id IN (?)", ids).Delete(h).Error
	return
}

// 新增
func (h *HangTagInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(h.TableName()).Create(h).Error
}

func (h *HangTagInfo) Query() (err error) {
	return mysql.NewConn().Table(h.TableName()).Where("id = ?", h.ID).First(h).Error
}

func (h *HangTagInfo) UpdateDetails(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(h).Updates(h).First(h).Error
}

func (h *HangTagInfo) MinusQuality(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&HangTagInfo{}).Where("id = ?", h.ID).
		UpdateColumn("quality", gorm.Expr("quality - ?", 1)).
		First(h).Error
}

func (h *HangTagInfo) MinusQualityByCount(count int, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&HangTagInfo{}).Where("id = ?", h.ID).
		UpdateColumn("quality", gorm.Expr("quality - ?", count)).Error
}

func (h *HangTagInfo) RecoverQuality(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&HangTagInfo{}).Where("id = ?", h.ID).
		UpdateColumn("quality", gorm.Expr("quality + ?", 1)).
		First(h).Error
}

func (h *HangTagInfo) QueryList(pn, ps int) (list []*HangTagInfo, count int, err error) {

	db := mysql.NewConn().Table(h.TableName())

	if h.TagID != "" {
		db = db.Where("tag_id = ?", h.TagID)
	}
	if h.Name != "" {
		db = db.Where("name Like ?", "%"+h.Name+"%")
	}
	if h.State != 0 {
		db = db.Where("state = ?", h.State)
	}
	if h.InventoryStorageName != "" {
		db = db.Where("inventory_storage_name = ?", h.InventoryStorageName)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}

func (h *HangTagInfo) QueryByIds(ids []int64) (list []*HangTagInfo, err error) {
	db := mysql.NewConn().Table(h.TableName())
	db = db.Where("id IN (?)", ids)
	err = db.Find(&list).Error

	return
}
