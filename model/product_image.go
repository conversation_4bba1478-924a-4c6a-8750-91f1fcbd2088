package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*ProductImage)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*ProductImage)(nil))
}

type ProductImage struct {
	ID         uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	ProductID  uint      `json:"product_id" gorm:"index"` //关联商品 id
	Sort       int       `json:"sort"`                    //排序 1最前
	ImgURL     string    `json:"img_url"`                 // 图片url路径
	IsDisplay  bool      `json:"is_display"`              //是否展示
	IsChartlet bool      `json:"is_chartlet"`             //是否贴图
}

func (p *ProductImage) TableName() string {
	return "product_image"
}

func (p *ProductImage) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

func (p *ProductImage) UploadImageInfo() error {
	var count = 0
	db := mysql.NewConn().Table(p.TableName())
	if p.ProductID != 0 {
		db = db.Where("product_id = ?", p.ProductID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return err
	}

	p.Sort = count + 1

	return p.Create()
}

func (p *ProductImage) UpdateImageInfo() error {

	if p.Sort > 0 {
		var count = 0
		// 如果对应的 product_id 已经存在图片，则要确定是否存在相同的 sort，如果存在，则返回错误
		db := mysql.NewConn().Model(&ProductImage{}).Where("product_id = ? AND sort = ?", p.ProductID, p.Sort)
		if err := db.Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			err := ecode.New(ecode.CODE_PARAMS_INVALID, "sort 已经存在")
			return err
		}
		return mysql.NewConn().Model(&ProductImage{}).Where("id = ?", p.ID).Updates(
			map[string]interface{}{
				"sort":       p.Sort,
				"is_display": p.IsDisplay,
			}).First(p).Error
	} else {
		//如果sort小于1，则只更新状态
		return mysql.NewConn().Model(&ProductImage{}).Where("id = ?", p.ID).Updates(
			map[string]interface{}{
				"is_display": p.IsDisplay,
			}).First(p).Error
	}
}

func (p *ProductImage) DeleteImageInfoByProductID() error {
	return mysql.NewConn().Table(p.TableName()).Where("product_id = ?", p.ProductID).Delete(p).Error
}

func (p *ProductImage) DeleteImageInfoByID() error {
	return mysql.NewConn().Table(p.TableName()).Where("id = ?", p.ID).Delete(p).Error
}

func (c *ProductImage) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ProductImage, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *ProductImage) DeleteByIds(ids []uint, tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	err = db.Model(&ProductImage{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ProductImage) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ProductImage) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ProductImage)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ProductImage) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
