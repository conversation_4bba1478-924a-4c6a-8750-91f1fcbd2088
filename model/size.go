package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"
)

func init() {
	mysql.RegisterTable((*Size)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*Size)(nil))
}

type Size struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	SizeId    string    `json:"size_id"      gorm:"unique;not null"` //尺码id
	Name      string    `json:"name"       gorm:"not null"`          //名称
	Sort      int       `json:"sort"       gorm:"not null"`          //排序
}

func (s *Size) TableName() string {
	return "size"
}

// 新增size
func (s *Size) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 查询
func (s *Size) Query() (info []*Size, count int, err error) {
	db := mysql.NewConn().Table(s.TableName())
	if s.SizeId != "" {
		db = db.Where("size_id = ?", s.SizeId)
	}
	err = db.Count(&count).Error
	if err != nil {
		return
	}
	err = db.Order("sort desc").Find(&info).Error
	return
}

// 查询一条数据
func (s *Size) QueryOne() (err error) {
	db := mysql.NewConn().Table(s.TableName())

	if len(s.SizeId) > 0 {
		db = db.Where("size_id = ?", s.SizeId)
	}

	err = db.First(s).Error
	return
}

// 查询相邻的数据
func (s *Size) GetAdjoin() (list []*Size, err error) {
	db := mysql.NewConn().Table(s.TableName())

	db = db.Where("sort = ? OR sort = ? ", s.Sort-1, s.Sort+1)

	err = db.Order("sort asc").Find(&list).Error
	return
}

// 获取全部size 根据sort排序
func (s *Size) GetList() (list []*Size, err error) {
	err = mysql.NewConn().Table(s.TableName()).Order("sort asc").Find(&list).Error
	return
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (s *Size) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(s.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (s *Size) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Size)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (s *Size) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(s.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", s.ID).Updates(s).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", s.ID).Update("updated_at", s.UpdatedAt).Error

	return
}

func (s *Size) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(s.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Size, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *Size) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}
	err = db.Model(&Size{}).Where("id IN (?)", ids).Delete(s).Error
	return
}
