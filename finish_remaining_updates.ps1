# PowerShell script to finish updating remaining DeleteByIds methods
# This script handles the remaining files that need to be updated

$remainingFiles = @(
    "model\front\order\order_item.go",
    "model\front\order\order_produce.go", 
    "model\front\order\order_suspend.go",
    "model\front\order\order_suspend_v2.go",
    "model\front\order\outbound_log_v2.go",
    "model\front\order\outbound_v2.go",
    "model\front\order\track_info.go",
    "model\front\order\track_limit.go",
    "model\front\template\design\design.go",
    "model\front\template\design\pack_design.go",
    "model\front\template\design\tag_design.go",
    "model\front\template\shop\shop_product.go",
    "model\front\template\shop\shop_product_image.go",
    "model\front\template\shop\shop_product_variant.go",
    "model\front\user\associated_shop.go",
    "model\front\user\discount.go",
    "model\front\user\material.go",
    "model\front\user\material_folder.go",
    "model\front\user\sample_address.go",
    "model\front\user\trade_record.go",
    "model\front\user\wallet.go",
    "model\front\user\wallet_log.go",
    "model\front\article\help_article.go",
    "model\front\shop\third_shop\third_shop_product.go"
)

Write-Host "Processing remaining $($remainingFiles.Count) files..." -ForegroundColor Green

foreach ($file in $remainingFiles) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        $originalContent = $content
        
        # Standard GORM v1 pattern replacement
        $pattern = 'func \(([^)]+)\) DeleteByIds\(ids \[\]uint, tx \.\.\.\*gorm\.DB\) \(err error\) \{\s*db := mysql\.NewConn\(\)\s*if len\(tx\) != 0 \{\s*db = tx\[0\]\s*\}'
        $replacement = 'func ($1) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConn()

	if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}'
        
        $content = $content -replace $pattern, $replacement
        
        # GORM v2 pattern replacement
        $pattern2 = 'func \(([^)]+)\) DeleteByIds\(ids \[\]uint, tx \.\.\.\*gorm2\.DB\) \(err error\) \{\s*db := mysql\.NewConnV2\(\)\s*if len\(tx\) != 0 \{\s*db = tx\[0\]\s*\}'
        $replacement2 = 'func ($1) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	db := mysql.NewConnV2()

	if len(param) > 0 {
		db = param[0].(*gorm2.DB)
	}'
        
        $content = $content -replace $pattern2, $replacement2
        
        # Unified DB interface pattern replacement
        $pattern3 = 'func \(([^)]+)\) DeleteByIds\(ids \[\]uint, tx \.\.\.[^)]+DBInterface\) \([^)]*error\) \{\s*var db [^}]+if len\(tx\) != 0 \{\s*db = tx\[0\]\s*\} else \{\s*db = mysql\.NewUnifiedDB\(\)\s*\}'
        $replacement3 = 'func ($1) DeleteByIds(ids []uint, param ...interface{}) (err error) {

	var db mysql.DBInterface

	if len(param) > 0 {
		db = param[0].(mysql.DBInterface)
	} else {
		db = mysql.NewUnifiedDB()
	}'
        
        $content = $content -replace $pattern3, $replacement3
        
        if ($content -ne $originalContent) {
            Set-Content $file $content -NoNewline
            Write-Host "  ✓ Updated successfully" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ No matching pattern found or already updated" -ForegroundColor Orange
        }
    } else {
        Write-Host "  ✗ File not found: $file" -ForegroundColor Red
    }
}

Write-Host "Remaining files processing completed!" -ForegroundColor Green
