# Simple PowerShell script to update DeleteByIds method signatures
# This script performs simple text replacements

# Get all Go files that contain SyncRegister
$files = Get-ChildItem -Recurse -Filter "*.go" | Where-Object { 
    (Get-Content $_.FullName -Raw) -match "SyncRegister" 
}

Write-Host "Found $($files.Count) files with SyncRegister" -ForegroundColor Green

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Pattern 1: Standard GORM v1 pattern
    $content = $content -replace 'func \(([^)]+)\) DeleteByIds\(ids \[\]uint, tx \.\.\.\*gorm\.DB\) \(err error\) \{', 'func ($1) DeleteByIds(ids []uint, param ...interface{}) (err error) {'
    
    # Pattern 2: Parameter handling for GORM v1
    $content = $content -replace 'if len\(tx\) != 0 \{\s*db = tx\[0\]\s*\}', 'if len(param) > 0 {
		db = param[0].(*gorm.DB)
	}'
    
    # Pattern 3: GORM v2 / Unified DB pattern
    $content = $content -replace 'func \(([^)]+)\) DeleteByIds\(ids \[\]uint, tx \.\.\.[^)]+DBInterface\) \([^)]*error\) \{', 'func ($1) DeleteByIds(ids []uint, param ...interface{}) (err error) {'
    
    # Pattern 4: Parameter handling for GORM v2
    $content = $content -replace 'if len\(tx\) != 0 \{\s*db = tx\[0\]\s*\} else \{\s*db = mysql\.NewUnifiedDB\(\)\s*\}', 'if len(param) > 0 {
		db = param[0].(mysql.DBInterface)
	} else {
		db = mysql.NewUnifiedDB()
	}'
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName $content -NoNewline
        Write-Host "Updated: $($file.FullName)" -ForegroundColor Yellow
    }
}

Write-Host "Batch update completed!" -ForegroundColor Green
